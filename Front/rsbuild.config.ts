import { defineConfig, loadEnv } from '@rsbuild/core';
import { pluginReact } from '@rsbuild/plugin-react';
import { pluginSass } from '@rsbuild/plugin-sass';
import rspack from '@rspack/core';
import AntdDayjsWebpackPlugin from 'antd-dayjs-webpack-plugin';
import fs from 'fs';
import NodePolyfillPlugin from 'node-polyfill-webpack-plugin';
import path from 'path';
import { getBaseUrl, isDev } from './src/utils/getEnv';

const { publicVars, rawPublicVars } = loadEnv({ prefixes: ['REACT_APP_'] });

const proxyUrl = fs.readFileSync('./target.env', 'utf8').trim();
const isMatch = (params) => process.argv.includes(params);

module.exports = defineConfig({
  output: {
    distPath: { root: 'build' },
    polyfill: 'entry'
    // sourceMap: {
    //   js: 'source-map',
    //   css: false
    // }
  },
  dev: {
    watchFiles: {
      paths: isDev ? ['./target.env'] : [],
      type: 'reload-server'
    }
  },
  source: {
    define: {
      ...publicVars,
      'process.env': JSON.stringify({
        ...rawPublicVars,
        IS_FULL: isMatch('full'),
        IS_GF: isMatch('gf'),
        IS_SW: isMatch('sw'),
        __PROXYURL__: proxyUrl
      })
    },
    tsconfigPath: './tsconfig.json',
    // todo 不支持低版本谷歌浏览器，需要使用polyfill，已经在 版本Chromium 57.0.2955.0 测试过
    include: [/node_modules[\\/]react-i18next[\\/]/, /node_modules[\\/]i18next[\\/]/, /node_modules[\\/]zustand[\\/]/]
  },
  html: {
    template: './public/index.html',
    favicon: './public/favicon.ico'
  },
  tools: {
    rspack: (config, { appendPlugins, appendRules }) => {
      appendRules({
        test: /\.scss$/,
        use: [
          {
            loader: 'sass-resources-loader',
            options: {
              resources: path.resolve(__dirname, './src/assets/css/variable.scss')
            }
          }
        ]
      });

      appendPlugins([
        new AntdDayjsWebpackPlugin(),
        new NodePolyfillPlugin(),
        new rspack.CssExtractRspackPlugin({ filename: 'static/css/[name].css' }),
        new rspack.ProvidePlugin({
          stream: require.resolve('stream-browserify'),
          buffer: require.resolve('buffer'),
          process: require.resolve('process/browser'),
          react: require.resolve('react'),
          React: require.resolve('react'),
          _: 'lodash',
          dayjs: 'dayjs'
        })
      ]);
      config.resolve ||= {
        alias: {},
        fallback: {}
      };
      config.resolve.alias ||= {};
      config.resolve.alias = {
        ...config.resolve.alias,
        '@': path.resolve(__dirname, 'src'),
        utils: path.resolve(__dirname, 'src/utils'),
        assets: path.resolve(__dirname, 'src/assets'),
        components: path.resolve(__dirname, 'src/components'),
        service: path.resolve(__dirname, 'src/service'),
        store: path.resolve(__dirname, 'src/store'),
        context: path.resolve(__dirname, 'src/context'),
        pages: path.resolve(__dirname, 'src/pages'),
        react: require.resolve('react')
      };
      // config.resolve.fallback ||= {};
      // config.resolve.fallback.stream = require.resolve('stream-browserify');
      config.ignoreWarnings ||= [];
      // 排除antd 列表刷新警告
      config.ignoreWarnings.push(/Conflicting order/);

      config.cache = true;
      config.experiments = {
        cache: {
          type: 'persistent'
        },
        parallelCodeSplitting: true
      };
      return config;
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      assets: path.resolve(__dirname, 'src/assets')
    }
  },
  plugins: [
    pluginReact(),
    pluginSass({
      sassLoaderOptions: [
        {
          api: 'modern-compiler',
          sassOptions: {
            silenceDeprecations: ['import', 'mixed-decls']
          }
        }
      ]
    })
  ],
  server: {
    host: '0.0.0.0',
    port: 8081,
    open: true,
    base: getBaseUrl(),
    proxy: {
      '/api/': {
        context: ['/analyzer/**/*.do', '/usercenter/**/*.do'],
        target: proxyUrl,
        changeOrigin: true,
        router: () => proxyUrl
      }
    }
  },
  performance: {
    removeConsole: process.env.NODE_ENV === 'production' ? ['log', 'warn'] : [],
    printFileSize: {
      include: (asset) => asset.size > 100 * 1000
    }
  }
});
