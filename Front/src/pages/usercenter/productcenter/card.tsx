import { AutoGrid } from '@/components/autoGrid';
import { idToName } from '@/pages/home/<USER>/dataPermissions/config';
import { useStore } from '@/store/globalStore';
import { getDeptId, setDeptId } from '@/utils/commonUtils';
import { transformUrl } from '@/utils/universal';
import { Button, Select, Tooltip } from 'antd';
import _ from 'lodash';
import React, { useCallback } from 'react';
import { t } from 'utils/translation';

interface Product {
  name: string;
  id: string;
}

const { Option } = Select;

const hexToRgba = (hex: string, alpha: number) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

/**
 * 获取主题色
 */
const getCssVariableValue = () => {
  const value = getComputedStyle(document.documentElement).getPropertyValue('--ant-primary-color').trim();
  if (value.startsWith('rgb')) {
    const rgb = value.match(/\d+/g);
    if (rgb && rgb.length >= 3) {
      const hex = `#${rgb
        .slice(0, 3)
        .map((x) => parseInt(x).toString(16).padStart(2, '0'))
        .join('')}`;
      return hex;
    }
  }
  return value;
};

const getBackground = (color: string) => {
  return `linear-gradient(to left bottom, ${hexToRgba(color || getCssVariableValue(), 0.08)} 0%, rgba(255, 255, 255, 0) 50%), white`;
};

export function UserCenterCard(props: {
  productList: Product[];
  projectList: any;
  currentProjectId: string;
  loginUser: any;
  enterProduct: (product: Product) => void;
  loading: boolean;
  changeProject: (project: any) => void;
}) {
  const { productList, projectList, currentProjectId, loginUser, enterProduct, loading, changeProject } = props;
  const { triggerDepartmentRefresh, organizationList } = useStore();

  const renderDept = useCallback(() => {
    const userDepartment = JSON.parse(localStorage.getItem('userDepartment') || '{}');
    const _departmentListAll = JSON.parse(localStorage.getItem('departmentListAll') || '{}');

    return (
      <Select
        className="mr-[-12px]"
        value={getDeptId() || undefined}
        onChange={(value) => {
          setDeptId(value);
          triggerDepartmentRefresh();
        }}
        style={{ maxWidth: 160 }}
        bordered={false}
        dropdownMatchSelectWidth={false}
        optionLabelProp="label"
      >
        {!_.isEmpty(userDepartment) &&
          userDepartment.map((item: any) => {
            const value = _.find(_departmentListAll, {
              id: parseInt(item?.deptId)
            });
            const deptKeyValue = JSON.parse(localStorage.getItem('deptKeyValue')!);

            return (
              <Option key={value?.id} value={value?.id} label={value?.name}>
                <div className="flex flex-col">
                  <div className="my-text-overflow" title={value?.name}>
                    {value?.name}
                  </div>
                  <div
                    className="text-[rgba(0,0,0,.45)] my-text-overflow"
                    title={idToName(deptKeyValue, `${value?.routes}${value?.id}`).join(' / ')}
                  >
                    {idToName(deptKeyValue, `${value?.routes}${value?.id}`).join(' / ')}
                  </div>
                </div>
              </Option>
            );
          })}
      </Select>
    );
  }, []);

  // 定义 getSpan 函数，用于 AutoGrid
  const getSpanVal = (screenWidth: number) => {
    if (screenWidth < 1280) return 12;
    if (screenWidth >= 1280 && screenWidth < 1600) return 12;
    return 8;
  };
  return (
    <div className="flex gap-40 justify-between">
      <div className="flex-1 flex flex-col gap-40 justify-between">
        <div>
          <div className="card flex flex-col gap-24">
            <AutoGrid getSpan={getSpanVal}>
              {productList.map((item: any) => (
                <div
                  key={item.id}
                  className="h-230 p-24 rounded-[6px] flex flex-col gap-8 hover:shadow-lg transition-all duration-300"
                  style={{
                    background: `${getBackground(item?.backgroundColor)}`
                  }}
                >
                  <img width={48} height={48} src={transformUrl(item?.pictureUrl) || ''} alt="" />
                  <div>
                    <Tooltip title={item.name} placement="topLeft">
                      <div className="flex text-ellipsis overflow-hidden whitespace-nowrap w-[300px]">
                        <div className="text-18 font-bold text-ellipsis overflow-hidden whitespace-nowrap mr-8">
                          {item.name}
                        </div>
                        {item.expired && (
                          <div className="rounded-[12px] bg-[#FAAD14] h-[22px] leading-[22px] px-8 text-12">已到期</div>
                        )}
                      </div>
                    </Tooltip>
                    <div className="text-14 text-gray-400 mb-8 line-clamp-2 h-44" title={item.memo}>
                      {item.memo}
                    </div>
                  </div>
                  <Button
                    style={{ width: 88, padding: 0, borderRadius: 6 }}
                    type="primary"
                    disabled={item.expired || _.isEmpty(projectList)}
                    onClick={() => {
                      enterProduct(item);
                    }}
                    loading={loading}
                  >
                    {t('productCenter-x5_IhOtua2')}
                  </Button>
                </div>
              ))}
            </AutoGrid>
          </div>
        </div>
      </div>
      <div className="w-320 h-304 bg-white rounded-[6px] p-16 ">
        <div className="flex gap-8 flex-col">
          <div className="text-16 font-bold">{t('productCenter-NyCK4lCG2p')}</div>
          <Select
            className="w-full [&_>div]:!rounded-[6px] mb-8"
            value={localStorage.getItem('organizationId')}
            onChange={(value) => {
              localStorage.setItem('organizationId', value);
              localStorage.removeItem('deptId');
              localStorage.removeItem('deptShowType');
              window.location.reload();
            }}
          >
            {organizationList.map((item: any) => (
              <Option value={`${item.id}`} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        </div>
        <div className="[&_>div]:h-40 [&_>div]:flex [&_>div]:items-center [&_>div]:justify-between">
          <div>
            <span>{t('productCenter-GgVT1oJytP')}</span>
            {renderDept()}
          </div>
          <div>
            <span>{t('productCenter-QxAtuCSR6A')}</span>
            <Select
              style={{ maxWidth: 160 }}
              className="[&_>ant-select-selector]:rounded-[6px] mr-[-12px]"
              value={currentProjectId}
              onChange={(value) => {
                changeProject(value);
              }}
              bordered={false}
              showSearch
              optionFilterProp="children"
            >
              {projectList.map((item: any) => (
                <Option value={item.id} key={item.id}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </div>
          <div>
            <span>{t('productCenter-GInBtf3G5i')}</span>
            <span>{_.isEmpty(projectList) ? '' : projectList[0]?.roleName}</span>
          </div>
          <div>
            <span>{t('productCenter-pE9Pmaa3QM')}</span>
            <span>{projectList?.length || 0}</span>
          </div>
          <div>
            <span>{t('productCenter-bN2CEmlndw')}</span>
            <span>{dayjs(loginUser.lastLoginTime).format('YYYY-MM-DD HH:mm:ss')}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
