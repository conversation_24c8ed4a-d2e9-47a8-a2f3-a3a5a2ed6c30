import { Button, Form, Input, message, Modal } from 'antd';
import React from 'react';
import _organizationManageService from 'service/organizationmanageService';
import { t } from 'utils/translation';

export default function ProductCenterModal({
  loading,
  vaildData,
  loginUser
}: {
  loading: boolean;
  vaildData: any;
  loginUser: any;
}) {
  const [pwdForm] = Form.useForm();

  const handleOk = async () => {
    try {
      const baseParams = await pwdForm.validateFields();
      await _organizationManageService.modifyPassWord(baseParams);
      localStorage.removeItem('modifyPassWord');
      message.success(t('productCenter-3slknq3bcu'));
      // @ts-ignore
      global.__LOGOUT__();
    } catch (e) {
      console.error(e);
    }
  };

  const compareToFirstPassword = (rule: any, value: any, callback: any) => {
    if (value && value !== pwdForm.getFieldValue('pwd')) {
      callback(t('productCenter-0x8YvKaw3J'));
    } else {
      callback();
    }
  };

  return (
    <Modal
      title={t('productCenter-i6_TT_m4LY')}
      open
      maskClosable={false}
      keyboard={false}
      closable={false}
      footer={
        <Button onClick={handleOk} type="primary">
          {t('productCenter-x5_IhOtua2')}
        </Button>
      }
      cancelButtonProps={undefined}
      confirmLoading={loading}
    >
      <Form form={pwdForm} layout="vertical">
        <Form.Item
          label={t('productCenter-j9dRwHbq4E')}
          name="originalPwd"
          rules={[{ required: true, message: t('productCenter-_5TqAH0Qpo') }]}
        >
          <Input.Password placeholder={t('productCenter-_5TqAH0Qpo')} />
        </Form.Item>
        <Form.Item
          label={t('productCenter-P9j2emJSci')}
          name="pwd"
          rules={[
            { required: true, message: t('productCenter-P9j2emJSci') },
            {
              pattern:
                vaildData?.value === 'ON'
                  ? new RegExp(
                      `^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()\\-_=+{};:,<.>])(?!.*[\u4E00-\u9FA5])(?!.*\\s).{${vaildData.config.minLength},24}$`
                    )
                  : /^[0-9A-Za-z\\W_=?.。~!@#$%^&*()-=]{6,32}$/,
              message:
                vaildData?.value === 'ON'
                  ? t('productCenter-CtjKvZL1ih', { minLength: vaildData.config.minLength })
                  : t('productCenter-fesWlm_NJn')
            },
            // @ts-ignore
            vaildData?.value === 'ON' && {
              validator: async (_, inputValue) => {
                try {
                  if (
                    inputValue === loginUser.email ||
                    inputValue === loginUser.mobile ||
                    inputValue === loginUser.jobNo
                  ) {
                    return Promise.reject(t('productCenter-nsIZGuovAw'));
                  } else {
                    return Promise.resolve();
                  }
                } catch (error) {
                  return Promise.reject(t('productCenter-NxUPPM4qS8'));
                }
              }
            }
          ]}
        >
          <Input.Password placeholder={t('productCenter-P9j2emJSci')} />
        </Form.Item>
        <Form.Item
          label={t('productCenter-EoyyiRrtjn')}
          name="confirmPwd"
          rules={[{ required: true, message: t('productCenter-EoyyiRrtjn') }, { validator: compareToFirstPassword }]}
        >
          <Input.Password placeholder={t('productCenter-EoyyiRrtjn')} />
        </Form.Item>
      </Form>
    </Modal>
  );
}
