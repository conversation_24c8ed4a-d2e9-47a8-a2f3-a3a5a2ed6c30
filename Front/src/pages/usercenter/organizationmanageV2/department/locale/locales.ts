export default {
  cn: {
    // department/index.tsx
    'userCenter-hkfyu0hUEuAQ': '部门名称',
    'userCenter-gbA09BoDrElo': '部门编号',
    'userCenter-xJu51uppJ7CM': '部门类型',
    'userCenter-6P9DaWXuQt5w': '部门层级',
    'userCenter-ggt6y0H0O2OO': '级',
    'userCenter-nyoUvXQFyY6n': '人数',
    'userCenter-TyzVa7ZfCLrr': '操作',
    'userCenter-qftNMJ3FpoON': '查看',
    'userCenter-BSIW61r47eaZ': '添加子部门',
    'userCenter-htTJL3yMXedN': '编辑',
    'userCenter-lX7SPT25UZxZ': '上移',
    'userCenter-24mETTmPNtC8': '下移',
    'userCenter-NyB46KPokyxs': '移动成功',
    'userCenter-A46tUTfejdjy': '保存成功',
    'userCenter-IoDZVNqcRFQS': '添加子部门',
    'userCenter-SsxaY2DWHdhg': '部门信息',
    'userCenter-102emTuyC6ai': '编辑部门',
    'userCenter-FOjN1WYNLmgp': '新建部门',

    // department/create/index.tsx
    'userCenter-UPBniS4EfMt9': '上级部门：',
    'userCenter-iuybZ9Lv1CaG': '部门名称：',
    'userCenter-IFZ3aOmP4kVS': '部门编号：',
    'userCenter-nqOgkhhqkIuw': '部门类型：',
    'userCenter-gKAouPSywwqN': '上级部门',
    'userCenter-V8iUchsT0JwP': '请选择上级部门',
    'userCenter-MvaUqDZbYwmY': '请选择',
    'userCenter-JuUaczhhfRCw': '部门名称',
    'userCenter-I5aizgeClvxK': '请输入部门名称',
    'userCenter-FJmESCEqLtbk': '最大长度不能超过100字符',
    'userCenter-mlM1jF6eVPe2': '不能输入空格',
    'userCenter-rI1ws21EZA2U': '请输入',
    'userCenter-MIqefAqp9vK9': '部门编号',
    'userCenter-2SsJkXOhNv4J': '只能输入字母、数字、中划线、下划线',
    'userCenter-21nMQl0a106X': '最大长度不能超过32字符',
    'userCenter-cCoXe0mIf50O': '部门编码已存在',
    'userCenter-3qNLvENQBrOU': '部门类型',
    'userCenter-C5YkA7OhPvJq': '取消',
    'userCenter-qm9uG2CK6L7e': '确认',
    'userCenter-kb0kZpNZzQ': '不填写则自动生成，填写后不可修改',

    // department/header/index.tsx
    'userCenter-hMkPhzB0Gdw5': '部门管理',
    'userCenter-QWoFBkoj2L2w': '搜索部门名称、部门编号',
    'userCenter-RH9yIsZMQ3s1': '批量导入 / 导出',
    'userCenter-vHNjgBUWXjUG': '部门类型管理',
    'userCenter-Ln8DVbI2ywyw': '新建',

    // department/typeManage/index.tsx
    'userCenter-6DZNzdPeSVO3': '共 {{total}} 条',
    'userCenter-q9xhTw3bXRDj': '部门类型标识',
    'userCenter-mdP4zB9U60UY': '部门类型名称',
    'userCenter-QB1W11ScidcW': '部门数量',
    'userCenter-CANOmcQmlp5E': '查看部门类型',
    'userCenter-ZqlEW7jZ1BjS': '编辑部门类型',
    'userCenter-9WTw4nPhJqpl': '新建部门类型',

    // department/typeManage/create/index.tsx
    'userCenter-fkgXjSbW9KoW': '部门类型名称：',
    'userCenter-ARIrMggSv3m6': '部门类型标识：',
    'userCenter-wxMAxJhazej9': '部门数量：',
    'userCenter-urVKWs93YKRr': '部门类型名称',
    'userCenter-ShmBxI3OPDk4': '请输入部门类型名称',
    'userCenter-NemvKkNJwOrB': '部门类型名称已存在',
    'userCenter-7CFqowLOi83T': '部门类型标识',
    'userCenter-uMZ2yemPd5G3': '请输入部门类型标识',
    'userCenter-vpl4pDESWWyL': '部门类型标识已存在',
    'userCenter-kVqmGhzrbj': '填写后不可修改'
  },
  en: {
    // department/index.tsx
    'userCenter-hkfyu0hUEuAQ': 'Department Name',
    'userCenter-gbA09BoDrElo': 'Department Code',
    'userCenter-xJu51uppJ7CM': 'Department Type',
    'userCenter-6P9DaWXuQt5w': 'Department Level',
    'userCenter-ggt6y0H0O2OO': 'Level',
    'userCenter-nyoUvXQFyY6n': 'Number of People',
    'userCenter-TyzVa7ZfCLrr': 'Actions',
    'userCenter-qftNMJ3FpoON': 'View',
    'userCenter-BSIW61r47eaZ': 'Add Sub-department',
    'userCenter-htTJL3yMXedN': 'Edit',
    'userCenter-lX7SPT25UZxZ': 'Move Up',
    'userCenter-24mETTmPNtC8': 'Move Down',
    'userCenter-NyB46KPokyxs': 'Move Successful',
    'userCenter-A46tUTfejdjy': 'Save Successful',
    'userCenter-IoDZVNqcRFQS': 'Add Sub-department',
    'userCenter-SsxaY2DWHdhg': 'Department Information',
    'userCenter-102emTuyC6ai': 'Edit Department',
    'userCenter-FOjN1WYNLmgp': 'Create Department',

    // department/create/index.tsx
    'userCenter-UPBniS4EfMt9': 'Parent Department:',
    'userCenter-iuybZ9Lv1CaG': 'Department Name:',
    'userCenter-IFZ3aOmP4kVS': 'Department Code:',
    'userCenter-nqOgkhhqkIuw': 'Department Type:',
    'userCenter-gKAouPSywwqN': 'Parent Department',
    'userCenter-V8iUchsT0JwP': 'Please select parent department',
    'userCenter-MvaUqDZbYwmY': 'Please select',
    'userCenter-JuUaczhhfRCw': 'Department Name',
    'userCenter-I5aizgeClvxK': 'Please enter department name',
    'userCenter-FJmESCEqLtbk': 'Maximum length cannot exceed 100 characters',
    'userCenter-mlM1jF6eVPe2': 'Cannot enter spaces',
    'userCenter-rI1ws21EZA2U': 'Please enter',
    'userCenter-MIqefAqp9vK9': 'Department Code',
    'userCenter-2SsJkXOhNv4J': 'Only letters, numbers, hyphens, and underscores are allowed',
    'userCenter-21nMQl0a106X': 'Maximum length cannot exceed 32 characters',
    'userCenter-cCoXe0mIf50O': 'Department code already exists',
    'userCenter-3qNLvENQBrOU': 'Department Type',
    'userCenter-C5YkA7OhPvJq': 'Cancel',
    'userCenter-qm9uG2CK6L7e': 'Confirm',
    'userCenter-kb0kZpNZzQ': 'Leave blank to generate automatically, and cannot be modified after filling',

    // department/header/index.tsx
    'userCenter-hMkPhzB0Gdw5': 'Department Management',
    'userCenter-QWoFBkoj2L2w': 'Search department name, department code',
    'userCenter-RH9yIsZMQ3s1': 'Batch Import / Export',
    'userCenter-vHNjgBUWXjUG': 'Department Type Management',
    'userCenter-Ln8DVbI2ywyw': 'Create',

    // department/typeManage/index.tsx
    'userCenter-6DZNzdPeSVO3': 'Total {{total}} items',
    'userCenter-q9xhTw3bXRDj': 'Department Type ID',
    'userCenter-mdP4zB9U60UY': 'Department Type Name',
    'userCenter-QB1W11ScidcW': 'Department Count',
    'userCenter-CANOmcQmlp5E': 'View Department Type',
    'userCenter-ZqlEW7jZ1BjS': 'Edit Department Type',
    'userCenter-9WTw4nPhJqpl': 'Create Department Type',

    // department/typeManage/create/index.tsx
    'userCenter-fkgXjSbW9KoW': 'Department Type Name:',
    'userCenter-ARIrMggSv3m6': 'Department Type ID:',
    'userCenter-wxMAxJhazej9': 'Department Count:',
    'userCenter-urVKWs93YKRr': 'Department Type Name',
    'userCenter-ShmBxI3OPDk4': 'Please enter department type name',
    'userCenter-NemvKkNJwOrB': 'Department type name already exists',
    'userCenter-7CFqowLOi83T': 'Department Type ID',
    'userCenter-uMZ2yemPd5G3': 'Please enter department type ID',
    'userCenter-vpl4pDESWWyL': 'Department type ID already exists',
    'userCenter-kVqmGhzrbj': 'Cannot be modified after filling'
  }
};
