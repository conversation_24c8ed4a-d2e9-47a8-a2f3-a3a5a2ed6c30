export default {
  cn: {
    // Frame.jsx
    'userCenter-oDCYbbEnVgbK': '导航栏按钮控制，只有两种状态，用bool值控制。true:侧边导航栏，false：头部导航栏',
    'userCenter-mnROkjHZ9bdd': '获取history中的参数，判断是否隐藏header',
    'userCenter-4klJjZmeBuhu': '发现自动登录不成功，保存当前登录地址，并跳转到登录页面',
    'userCenter-uyRegaNUawKW': '自动登录成功之后，查看是否有登录前地址，如果有登录前地址，跳转到登录前地址，并清除',
    'userCenter-V4zh29XKNwqJ': '当前地址是home且有当前菜单，且当前地址不是第一个菜单地址，就跳转至第一个菜单',
    'userCenter-gATYPwxzmZUq': '检查授权是否到期',
    'userCenter-KeQ7Vfyfmj9h': '如果返回结果是过期',
    'userCenter-1Uk2qQdYgZT1': '如果是即将过期',
    'userCenter-fbm4Au4lDaIm': '授权即将过期提醒',
    'userCenter-mWJQpz8qig0z': 'Datatist智能营销云系统使用授权将于{{dueTime}}到期，授权到期将影响系统正常使用，请联系客户经理及时续期',
    'userCenter-C6LAJaAvmeg6': '自动登录，并将数据保存在store中',
    'userCenter-GL2ubJpRWs45': '必须放最后一行',
    'userCenter-DUtuRSjftc4X': '无授权警告',
    'userCenter-bJ9fNKAmfFSZ': 'Datatist智能营销云系统使用授权已经到期，授权到期将影响系统正常使用，请联系客户经理及时续期',
    'userCenter-SBdpfQZQUubj': '正在检验是否登录',
    'userCenter-juj0Cs2n3oX9': '正在初始化数据',
    'userCenter-tzRaPTaG7hwG': '这里可以根据各属性动态添加，如果属性值为true则为其添加该类名，如果值为false，则不添加。这样达到了动态添加class的目的',
    'userCenter-3FLG7lkOBxas': '个人中心',

    // FrameHeade.jsx
    'userCenter-CHRoERRz7map': '产品中心',
    'userCenter-st3SIUWEZFvJ': '组织管理',
    'userCenter-dIulMWbPauos': '个人设置',
    'userCenter-52vXJfWBnIKS': '安全退出',
    'userCenter-aSh2sefwc89Q': '监听窗口变化，动态给文档设置高度',
    'userCenter-KCqQgGTVoFOB': '密码过期提醒',
    'userCenter-VN8qUMS1rmZZ': '每隔5分钟调用一下自动登陆',
    'userCenter-pGW4DkjJFllM': '登陆已过期，请重新登陆',
    'userCenter-EMTWUzVL7lEf': '获取部门树列表',
    'userCenter-YKgVV4TwY4Xd': '退出登录,退出登录清除登陆成功信息，并将初始化loading和自动登录状态重新初始化。',
    'userCenter-37uMPzJv5Wql': '获取自己所有列表 然后转换成keyValue形式',
    'userCenter-oB5qXhNICFkv': '这个所有的部门列表，不是用户自己的部门',
    'userCenter-fwUu3LjYTKex': '获取上次登录的部门ID',
    'userCenter-jsLwB2Q1qfmA': '设置选定的部门ID',
    'userCenter-jCn6VmAPdKwX': '清除ssoDeptId',
    'userCenter-etj6zbtgVs0R': '组织切换',
    'userCenter-z7AqCHqtiMwf': '部门切换',
    'userCenter-Yz236n7QMjVc': '切换语言',
    'userCenter-aIRxYShQSmEu': '欢迎您：',
    'userCenter-9iP4gg1LRns3': '使用useStore的refreshDepartmentTimestamp来控制组件的重新渲染',

    // menuConfig.js
    'userCenter-PhktjfXT6rfY': '菜单配置文件，支持一级二级菜单配置。',
    'userCenter-HycBAB0ZUksI': '看板',
    'userCenter-3FaWMgNfKZbc': '营销中心',
    'userCenter-DE9IpJCOumVB': '分析中心',
    'userCenter-Ycml6j9XiLAC': '画像中心',
    'userCenter-Eeo9icShEBpw': '用户分群',
    'userCenter-gOEecMYyQpd1': '数据中心',
    'userCenter-iwGUWFNCywRW': 'AI智能中心',

    // organizationmanage/index.jsx
    'userCenter-DVVJEmzfV4Ep': '选中的组织',
    'userCenter-h87a8YuL7HPL': '获取localStorage中的组织信息',
    'userCenter-Suf3n8YWWObF': '转让权限',
    'userCenter-HKziA9G4AheV': '操作完成',
    'userCenter-PCmRzPNBLo9y': '密码不正确，请重试',
    'userCenter-G2Srjwz1MZPH': '警告',
    'userCenter-aeP7GDfcOVBc': '你不是当前组织的拥有者或管理员，请联系管理员',
    'userCenter-1MP4t3TGHdDs': '组织基本信息',
    'userCenter-kgxqXl0hah2p': '组织名称',
    'userCenter-s5U1L8rB9XM5': '组织ID',
    'userCenter-WRDueMuJyNFR': '转让组织拥有权限',
    'userCenter-CXkoFYKccvoR': '拥有者',
    'userCenter-sEwth76wUy5o': '成员数量',
    'userCenter-QPRMUvs7pVC2': '创建时间',
    'userCenter-aAGi0Afq9VhP': '组织管理',
    'userCenter-PME1ZcFISJcI': '暂无组织',
    'userCenter-CM64ohBLOD5n': '组织概览',
    'userCenter-281A0OBvPhiT': '组织项目',
    'userCenter-w3jPJROnyDL9': '组织成员'
  },
  en: {
    // Frame.jsx
    'userCenter-oDCYbbEnVgbK': 'Navigation bar button control, only two states, controlled by bool value. true: side navigation bar, false: header navigation bar',
    'userCenter-mnROkjHZ9bdd': 'Get parameters from history to determine whether to hide header',
    'userCenter-4klJjZmeBuhu': 'Found automatic login unsuccessful, save current login address and jump to login page',
    'userCenter-uyRegaNUawKW': 'After successful automatic login, check if there is a pre-login address, if so, jump to pre-login address and clear',
    'userCenter-V4zh29XKNwqJ': 'Current address is home and has current menu, and current address is not the first menu address, then jump to the first menu',
    'userCenter-gATYPwxzmZUq': 'Check if authorization is expired',
    'userCenter-KeQ7Vfyfmj9h': 'If the return result is expired',
    'userCenter-1Uk2qQdYgZT1': 'If it is about to expire',
    'userCenter-fbm4Au4lDaIm': 'Authorization expiration reminder',
    'userCenter-mWJQpz8qig0z': 'Datatist intelligent marketing cloud system usage authorization will expire on {{dueTime}}, authorization expiration will affect normal system use, please contact customer manager for timely renewal',
    'userCenter-C6LAJaAvmeg6': 'Automatic login and save data in store',
    'userCenter-GL2ubJpRWs45': 'Must be placed on the last line',
    'userCenter-DUtuRSjftc4X': 'No authorization warning',
    'userCenter-bJ9fNKAmfFSZ': 'Datatist intelligent marketing cloud system usage authorization has expired, authorization expiration will affect normal system use, please contact customer manager for timely renewal',
    'userCenter-SBdpfQZQUubj': 'Checking login status',
    'userCenter-juj0Cs2n3oX9': 'Initializing data',
    'userCenter-tzRaPTaG7hwG': 'Here you can dynamically add according to various attributes, if the attribute value is true, add the class name, if the value is false, do not add. This achieves the purpose of dynamically adding class',
    'userCenter-3FLG7lkOBxas': 'Personal Center',

    // FrameHeade.jsx
    'userCenter-CHRoERRz7map': 'Product Center',
    'userCenter-st3SIUWEZFvJ': 'Organization Management',
    'userCenter-dIulMWbPauos': 'Personal Settings',
    'userCenter-52vXJfWBnIKS': 'Logout',
    'userCenter-aSh2sefwc89Q': 'Listen to window changes and dynamically set document height',
    'userCenter-KCqQgGTVoFOB': 'Password expiration reminder',
    'userCenter-VN8qUMS1rmZZ': 'Call automatic login every 5 minutes',
    'userCenter-pGW4DkjJFllM': 'Login has expired, please log in again',
    'userCenter-EMTWUzVL7lEf': 'Get department tree list',
    'userCenter-YKgVV4TwY4Xd': 'Logout, clear login success information and reinitialize initialization loading and automatic login status.',
    'userCenter-37uMPzJv5Wql': 'Get all your own lists and convert them to keyValue form',
    'userCenter-oB5qXhNICFkv': "This is all department lists, not the user's own departments",
    'userCenter-fwUu3LjYTKex': 'Get last login department ID',
    'userCenter-jsLwB2Q1qfmA': 'Set selected department ID',
    'userCenter-jCn6VmAPdKwX': 'Clear ssoDeptId',
    'userCenter-etj6zbtgVs0R': 'Organization Switch',
    'userCenter-z7AqCHqtiMwf': 'Department Switch',
    'userCenter-Yz236n7QMjVc': 'Switch Language',
    'userCenter-aIRxYShQSmEu': 'Welcome:',
    'userCenter-9iP4gg1LRns3': "Use useStore's refreshDepartmentTimestamp to control component re-rendering",

    // menuConfig.js
    'userCenter-PhktjfXT6rfY': 'Menu configuration file, supports first and second level menu configuration.',
    'userCenter-HycBAB0ZUksI': 'Dashboard',
    'userCenter-3FaWMgNfKZbc': 'Marketing Center',
    'userCenter-DE9IpJCOumVB': 'Analysis Center',
    'userCenter-Ycml6j9XiLAC': 'Portrait Center',
    'userCenter-Eeo9icShEBpw': 'User Segmentation',
    'userCenter-gOEecMYyQpd1': 'Data Center',
    'userCenter-iwGUWFNCywRW': 'AI Intelligence Center',

    // organizationmanage/index.jsx
    'userCenter-DVVJEmzfV4Ep': 'Selected organization',
    'userCenter-h87a8YuL7HPL': 'Get organization information from localStorage',
    'userCenter-Suf3n8YWWObF': 'Transfer permissions',
    'userCenter-HKziA9G4AheV': 'Operation completed',
    'userCenter-PCmRzPNBLo9y': 'Password is incorrect, please try again',
    'userCenter-G2Srjwz1MZPH': 'Warning',
    'userCenter-aeP7GDfcOVBc': 'You are not the owner or administrator of the current organization, please contact the administrator',
    'userCenter-1MP4t3TGHdDs': 'Organization Basic Information',
    'userCenter-kgxqXl0hah2p': 'Organization Name',
    'userCenter-s5U1L8rB9XM5': 'Organization ID',
    'userCenter-WRDueMuJyNFR': 'Transfer Organization Ownership',
    'userCenter-CXkoFYKccvoR': 'Owner',
    'userCenter-sEwth76wUy5o': 'Member Count',
    'userCenter-QPRMUvs7pVC2': 'Creation Time',
    'userCenter-aAGi0Afq9VhP': 'Organization Management',
    'userCenter-PME1ZcFISJcI': 'No Organization',
    'userCenter-CM64ohBLOD5n': 'Organization Overview',
    'userCenter-281A0OBvPhiT': 'Organization Projects',
    'userCenter-w3jPJROnyDL9': 'Organization Members'
  }
};
