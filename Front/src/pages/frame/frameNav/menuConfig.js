// 菜单配置文件，支持一级二级菜单配置。
import { t } from '@/utils/translation';

const menuConfig = [
  {
    id: 12,
    name: t('setting-lhWHChQcFQvv'),
    parentId: 0,
    orderNum: 1,
    icon: 'pie-chart',
    route: 'kanban',
    children: []
  },
  {
    id: 13,
    name: t('setting-jM0BhAmCmbmy'),
    parentId: 0,
    orderNum: 2,
    icon: 'pie-chart',
    route: 'yingxiaozhongxin',
    children: []
  },
  {
    id: 14,
    name: t('setting-rv937rFLRvZY'),
    parentId: 0,
    orderNum: 3,
    icon: 'pie-chart',
    route: 'fenxizhongxin',
    children: []
  },
  {
    id: 15,
    name: t('setting-xg90DA6DCZ9O'),
    parentId: 0,
    orderNum: 4,
    icon: 'pie-chart',
    route: '',
    children: [
      {
        id: 18,
        name: t('setting-MQWqAU8dT6Dc'),
        parentId: 15,
        orderNum: 1,
        icon: 'pie-chart',
        route: '/aimarketer/home/<USER>/userGroup',
        domain: 'http://aimarketer/home.dev.datatist.cn',
        children: []
      }
    ]
  },
  {
    id: 16,
    name: t('setting-WvG94agrk9hz'),
    parentId: 0,
    orderNum: 5,
    icon: 'pie-chart',
    route: 'shujuzhongxin',
    children: []
  },
  {
    id: 17,
    name: t('setting-Q8X1NzyijF0h'),
    parentId: 0,
    orderNum: 6,
    icon: 'pie-chart',
    route: 'aizhongxin',
    children: []
  }
];

export { menuConfig };
