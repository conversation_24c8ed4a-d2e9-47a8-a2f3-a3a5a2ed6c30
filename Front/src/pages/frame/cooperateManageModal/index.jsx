import { useStore } from '@/store/share';
import { Button, Dropdown, Form, Input, Modal, Popover, Select, Space, Table, Tag, Tooltip, message } from 'antd';
import React, { useEffect, useState } from 'react';

import shareService from '@/service/shareService';

import { getDeptPath } from '@/pages/home/<USER>/dataPermissions/config';
import { calcPageNo, getCurrentPageRolesList } from '@/utils/universal';
import { DownOutlined, SearchOutlined } from '@ant-design/icons';
import _ from 'lodash';
import './index.scss';
import { t } from '@/utils/translation';

const { Option } = Select;
const mapArr = ['aim_segment_edit', 'aim_campaignV2_edit'];

const pagination = {
  showTotal: (totals) => t('setting-NDcne9ATqKj9', { totals }),
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['5', '10', '20', '50', '100']
};

const CooperateManageModal = () => {
  const { cooperateOpen, dispatchShare, shareInfo, shareOpen, reflash } = useStore();

  const [userList, setUserList] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectRowInfo, setSelectRowInfo] = useState([]);
  const [editOpen, setEditOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [rolesList, setRolesList] = useState([]);
  const [editType, setEditType] = useState('');
  const [editUserInfo, setEditUserInfo] = useState({});
  const [param, setParam] = useState({
    page: 1,
    search: [],
    size: 10,
    sorts: [{ propertyName: 'createTime', direction: 'desc' }]
  });

  const [form] = Form.useForm();

  useEffect(() => {
    const init = async () => {
      try {
        const rolesRes = await shareService.getActionGroupByRole({
          id: Number(localStorage.getItem('roleId'))
        });

        let rolesResult = getCurrentPageRolesList(shareInfo.type, rolesRes) || [];

        rolesResult = _.map(rolesResult, (item) => {
          if (_.includes(mapArr, item.code)) {
            return {
              ...item,
              name: t('setting-i3OEEtTie0x0')
            };
          }
          return item;
        });

        // todo 本次只做查看
        rolesResult = rolesResult.filter(
          (item) =>
            item.code === 'aim_segment_view' ||
            item.code === 'aim_campaignV2_view' ||
            item.code === 'aim_campaigns_view' ||
            item.code === 'aim_segment_edit' ||
            item.code === 'aim_campaignV2_edit'
        );
        setRolesList(rolesResult);
      } catch (error) {
        console.error(error);
      }
    };
    cooperateOpen && init();
  }, [cooperateOpen]);

  useEffect(() => {
    const getData = async () => {
      setLoading(true);
      try {
        const _param = _.cloneDeep(param);
        _param.search = [
          ...param.search,
          {
            propertyName: 'shareContentId',
            operator: 'EQ',
            value: shareInfo?.id
          },
          {
            propertyName: 'type',
            operator: 'EQ',
            value: shareInfo?.type
          }
        ];

        const res = await shareService.query(_param);

        pagination.total = res.totalElements;
        pagination.current = param.page;
        pagination.pageSize = param.size;

        setUserList(res.content);

        setLoading(false);
      } catch (error) {
        setLoading(false);
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    cooperateOpen && getData();
  }, [cooperateOpen, param, reflash]);

  const columns = [
    {
      title: t('setting-xayoWmftJ7Yq'),
      dataIndex: 'grantShareUserName',
      key: 'grantShareUserName',
      width: 284,
      render: (text, render) => (
        <div>
          <Tooltip title={text} placement="topLeft">
            <div className="overflow-hidden text-ellipsis whitespace-nowrap">{text}</div>
          </Tooltip>

          <Tooltip title={getDeptPath(render.deptId)} placement="topLeft">
            <div className="text-[rgba(0,0,0,.45)] overflow-hidden text-ellipsis whitespace-nowrap text-[12px]">
              {getDeptPath(render.deptId)}
            </div>
          </Tooltip>
        </div>
      )
    },
    {
      title: t('setting-aRwwOp5Zfbri'),
      dataIndex: 'shareRoleAuthVoList',
      key: 'shareRoleAuthVoList',
      width: 284,
      render: (text, record) => {
        if (text) {
          const notNullData = _.map(
            record.shareRoleAuthVoList.filter((item) => item),
            (item) => {
              if (_.includes(mapArr, item.authId)) {
                return {
                  ...item,
                  authName: t('setting-i3OEEtTie0x0')
                };
              }
              return item;
            }
          );
          if (notNullData.length > 3) {
            const threeOnes = _.slice(notNullData, 0, 3);
            threeOnes.push({ authId: 'x', authName: '...' });
            return (
              <Popover
                overlayStyle={{ maxWidth: 640 }}
                trigger="click"
                overlayClassName="cooperateMorePop"
                placement="top"
                title={t('setting-aRwwOp5Zfbri')}
                content={_.map(notNullData, (item) => (
                  <Tag key={item.authId} className="mb-[8px]">
                    {item.authName}
                  </Tag>
                ))}
              >
                <div>
                  {_.map(threeOnes, (item) => (
                    <Tag key={item.authId}>{item.authName}</Tag>
                  ))}
                </div>
              </Popover>
            );
          }
          return (
            <div>
              {_.map(notNullData, (item) => (
                <Tag key={item.authId}>{item.authName}</Tag>
              ))}
            </div>
          );
        }
      }
    },
    {
      title: t('setting-CDFQeiIVYTRB'),
      dataIndex: 'operter',
      key: 'operter',
      width: 132,
      render: (text, render) => (
        <div>
          <a className="mr-[16px]" onClick={() => onMultiEditChange('option', render)}>
            {t('setting-6meX0EZcXuO5')}
          </a>
          <a onClick={() => onMultiDeleteChange('option', render)}>{t('setting-ruuLBoG1gSrK')}</a>
        </div>
      )
    }
  ];

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const onCancel = () => {
    setSelectedRowKeys([]);
    setSelectRowInfo([]);
    setParam({ ...param, search: [] });
    dispatchShare({ cooperateOpen: false });
  };

  const onMultiEditChange = (type, render) => {
    if (type === 'multi' && _.isEmpty(selectedRowKeys)) {
      message.warning(t('setting-xNM9GiOZ3eNA'));
      return;
    }

    if (type === 'multi' && selectedRowKeys.length === 1) {
      form.setFieldsValue({
        roles: selectRowInfo[0].shareRoleAuthVoList.map((item) => item.authId)
      });
    } else if (type === 'option') {
      setEditUserInfo(render);
      form.setFieldsValue({
        roles: render.shareRoleAuthVoList.map((item) => item.authId)
      });
    }

    setEditType(type);
    setEditOpen(true);
  };

  const onEditCancel = () => {
    form.setFieldsValue({ roles: undefined });
    setEditOpen(false);
  };

  const onMultiEditOk = async () => {
    try {
      const res = await form.validateFields();

      let saveParams = [];

      if (editType === 'multi') {
        saveParams = selectRowInfo.map((item) => {
          return {
            authIds: res.roles.join(','),
            id: item.id
          };
        });
      } else {
        saveParams = [
          {
            id: editUserInfo.id,
            authIds: res.roles.join(',')
          }
        ];
      }

      await shareService.editSave(saveParams);

      message.success(t('setting-3DSnmxyQXVbk'));
      setParam({ ...param });
      setSelectedRowKeys([]);
      setSelectRowInfo([]);
      setEditUserInfo({});

      onEditCancel();
    } catch (error) {
      console.error(error);
    }
  };

  const onMultiDeleteChange = (type, render) => {
    if (type === 'multi' && _.isEmpty(selectedRowKeys)) {
      message.warning(t('setting-xNM9GiOZ3eNA'));
      return;
    }

    setEditUserInfo(render);

    Modal.confirm({
      title: t('setting-ruuLBoG1gSrK'),
      className: 'shareDelModal',
      width: 480,
      centered: true,
      content: (
        <div className="text-[rgba(0,0,0,.65)]">
          <div>
            {t('setting-a3EkP8sFVoED', {
              names:
                type === 'multi'
                  ? selectRowInfo.map((item) => item.grantShareUserName).join('、')
                  : render.grantShareUserName
            })}
          </div>
          <div className="mt-[8px]">{t('setting-J5un0lea4ICz')}</div>
        </div>
      ),
      okText: t('setting-NHWmKQNIsVUt'),
      okType: 'primary',
      cancelText: t('setting-iSHSnumCeFUp'),
      async onOk() {
        try {
          type === 'multi' ? await shareService.deleteByIds(selectedRowKeys) : await shareService.deleteById(render.id);

          let page;
          if (type === 'multi' && selectedRowKeys.length !== 1) {
            let total = _.cloneDeep(pagination.total);
            selectedRowKeys.forEach(() => {
              total -= 1;
              page = calcPageNo(total, pagination.current, pagination.pageSize);
            });
          } else {
            page = calcPageNo(pagination.total, pagination.current, pagination.pageSize);
          }

          setParam({ ...param, page });
          setSelectedRowKeys([]);
          setSelectRowInfo([]);
          setEditUserInfo({});
          message.success(t('setting-Nm24EcCPLqaf'));
        } catch {
          message.success(t('setting-XS26rqVUe9FP'));
        }
      },
      onCancel() {}
    });
  };

  const items = [
    {
      label: t('setting-xllyUyGgh8ug'),
      onClick: (e) => onMultiEditChange('multi', e),
      key: 'multiEdit'
    },
    {
      label: t('setting-WhK6I26ydMgD'),
      onClick: (e) => onMultiDeleteChange('multi', e),
      key: 'multiDelete'
    }
  ];

  const addCooperateUser = () => {
    if (shareOpen) {
      dispatchShare({
        cooperateOpen: false,
        shareInfo: { type: shareInfo.type, id: shareInfo.id }
      });
    } else {
      dispatchShare({
        shareOpen: true,
        shareInfo: { type: shareInfo.type, id: shareInfo.id }
      });
    }
  };

  const onTableSelectChange = (newSelectedRowKeys, node) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectRowInfo(node);
  };

  const rowSelection = {
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    onChange: onTableSelectChange
  };

  const onCooperateSearch = (val) => {
    setParam({
      ...param,
      page: 1,
      search: [
        {
          propertyName: 'user.name',
          operator: 'LIKE',
          value: val
        }
      ]
    });
  };

  return (
    <>
      <Modal
        confirmLoading={loading}
        open={cooperateOpen}
        onCancel={onCancel}
        footer={null}
        className="cooperateOpenModal"
        destroyOnClose
        width={800}
        title={t('setting-lOpkAVvCvvU5')}
      >
        <div>
          <div className="flex items-center justify-between mb-[16px]">
            <div className="flex items-center">
              <span className="text-[16px] font-[600] mr-[16px]">{t('setting-ql8v0rQ8hU3c')}</span>
              {selectedRowKeys.length ? (
                <span className="text-[rgba(0,0,0,.65)]">
                  {t('setting-mYE1b7eTlbV7', { count: selectedRowKeys.length })}
                </span>
              ) : null}
            </div>

            <div className="flex">
              <Input
                placeholder={t('setting-khBlTgCUQBf6')}
                className="mr-[16px] w-[264px]"
                onChange={(e) => onCooperateSearch(e.target.value)}
                suffix={<SearchOutlined className="text-[rgba(0,0,0,.65)]" />}
              />
              <Dropdown menu={{ items }}>
                <Button>
                  <Space>
                    {t('setting-hUrxHR8psPjp')} <DownOutlined />
                  </Space>
                </Button>
              </Dropdown>
              <Button type="primary" className="ml-[8px]" onClick={addCooperateUser}>
                {t('setting-bi32NHBRygg7')}
              </Button>
            </div>
          </div>

          <div>
            <Table
              loading={loading}
              rowKey="id"
              columns={columns}
              pagination={pagination}
              dataSource={userList}
              rowSelection={rowSelection}
              onChange={handleTableChange}
              scroll={{
                y: 'calc(100vh - 431px)'
              }}
            />
          </div>
        </div>
      </Modal>

      <Modal
        open={editOpen}
        className="cooperateOpenModal"
        title={t('setting-6meX0EZcXuO5')}
        zIndex={99999}
        centered
        width={560}
        onOk={onMultiEditOk}
        onCancel={onEditCancel}
      >
        <Form form={form} layout="vertical">
          <Form.Item label={t('setting-aRwwOp5Zfbri')} rules={[{ required: true }]} name="roles">
            <Select
              mode="multiple"
              placeholder={t('setting-KVOlBCJMbo8e')}
              allowClear
              dropdownStyle={{ zIndex: 99999 }}
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().includes(input.toLowerCase())}
            >
              {rolesList.map((item) => (
                <Option key={item.id} value={item.code}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default CooperateManageModal;
