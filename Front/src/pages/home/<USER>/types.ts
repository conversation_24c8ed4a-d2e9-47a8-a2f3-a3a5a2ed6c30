export type Message = {
  msgRecordId: number;
  time: number;
  title: string;
  messageType: MessageType;
  content: string;
  status: string;
  createTime: number;
  updateTime: number;
  noticeType: string;
};

export type ButtonType = 'delete' | 'read' | 'deleteAll' | 'readAll';

export type MessageType =
  | 'FORCE_LOGIN_OUT'
  | 'LOGIN'
  | 'PASSWORD_EXPIRE'
  | 'TOKEN_EXPIRE'
  | 'USER_GENERAL_MESSAGE'
  | 'GENERAL_MESSAGE'
  | 'EVENT_JUMP_MESSAGE';

export type Pagination = {
  sorts: { direction: string; propertyName: string }[];
  page: number;
  size: number;
  search?: any[];
};

export type Sort = {
  direction: 'asc' | 'desc';
  propertyName: string;
};

export type TableParams = {
  current: number;
  pageSize: number;
  sorter?: {
    field?: string;
    order?: string;
  };
  filters?: Record<string, any>;
};
