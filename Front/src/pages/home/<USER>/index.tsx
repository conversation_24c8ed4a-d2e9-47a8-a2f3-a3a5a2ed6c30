import { useRequest } from '@umijs/hooks';
import { Button, message, Modal, Table } from 'antd';
import type { ColumnType, TablePaginationConfig } from 'antd/es/table';
import type { FilterValue } from 'antd/es/table/interface';
import QueryForList from 'components/bussinesscoms/queryforlist/index';
import ColumnActionCom from 'components/featurecoms/tableactioncom/index';
import _ from 'lodash';
import React, { useCallback, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import UserService from 'service/UserService';
import { configElements } from './config';
import { PAGINATION_CONFIG, TABLE_COLUMNS } from './constants';
import { ButtonType, Message, Pagination, TableParams } from './types';
import { t } from '@/utils/translation';

const userService = new UserService();

const getTableList = async (params: { pagination: Pagination }) => {
  try {
    const data = await userService.websiteMessageQuery(params);
    return {
      total: data.totalElements,
      list: data.content
    };
  } catch (error) {
    console.error('Failed to fetch website messages:', error);
    return {
      total: 0,
      list: []
    };
  }
};

export const WebsiteMessage: React.FC = () => {
  const [search, setSearch] = React.useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<number[]>([]);
  const history = useHistory();

  // @ts-ignore
  const columns = useMemo<ColumnType<Message>[]>(() => {
    return [
      ...TABLE_COLUMNS,
      {
        title: t('setting-CbXu9H3kQLu1'),
        className: 'td-set',
        width: 70,
        fixed: 'right' as const,
        render: (text: any, record: Message) => {
          const featureData = record.noticeType === 'JUMP_MSG' && {
            detail: {
              text: t('setting-pvSSNe0Pb6GV')
            }
          };
          return (
            <ColumnActionCom
              closeDropdown
              featureData={featureData}
              record={record}
              onClick={() => history.push('/aimarketer/home/<USER>/subrouter/eventManageRouter/eventManage')}
            />
          );
        }
      }
    ];
  }, []);

  const { tableProps: rawTableProps } = useRequest(
    ({ current, pageSize, filters, sorter: s }: TableParams) => {
      const pagination: Pagination = {
        page: current,
        size: pageSize,
        sorts: []
      };
      if (s?.field && s?.order) {
        pagination.sorts = [
          {
            direction: _.includes(s?.order, 'desc') ? 'desc' : 'asc',
            propertyName: s?.field
          }
        ];
      } else if (!s) {
        pagination.sorts = [
          {
            direction: 'desc',
            propertyName: 'createTime'
          }
        ];
      }
      if (filters) {
        Object.entries(filters).forEach(([field, value]) => {
          // @ts-ignore - 动态属性赋值
          pagination[field] = value;
        });
      }
      return getTableList({
        pagination: { ...pagination, search }
      });
    },
    {
      paginated: true,
      defaultPageSize: PAGINATION_CONFIG.defaultPageSize,
      refreshDeps: [search]
    }
  );

  const handleRowSelection = useCallback((record: Message, selected: boolean) => {
    setSelectedRowKeys((prevKeys) => {
      if (selected) {
        return _.uniq([...prevKeys, record.msgRecordId]);
      }
      return prevKeys.filter((key) => key !== record.msgRecordId);
    });
  }, []);

  const handleSelectAll = useCallback((selected: boolean, selectedRows: Message[], changeRows: Message[]) => {
    const changeIds = changeRows.map((row) => row.msgRecordId);
    setSelectedRowKeys((prevKeys) => {
      if (selected) {
        return _.uniq([...prevKeys, ...changeIds]);
      }
      return prevKeys.filter((key) => !changeIds.includes(key));
    });
  }, []);

  const handleQuery = useCallback((data: any[]) => {
    const newVal = {} as any;
    data.forEach((n) => {
      if (n.value) {
        newVal[n.propertyName] = n.value;
      }
      if (n.propertyName === 'status') {
        n.value = _.join(n.value, ',');
      }
    });
    setSearch(_.cloneDeep(data));
  }, []);

  // 处理类型兼容性问题
  const tableProps = {
    ...rawTableProps,
    onChange: (pagination: TablePaginationConfig, filters: Record<string, FilterValue | null>, sorter: any) => {
      if (rawTableProps.onChange) {
        rawTableProps.onChange(pagination as any, filters, sorter);
      }
    }
  };

  const clickButton = async (type: ButtonType) => {
    try {
      if (type === 'read') {
        await userService.modifyWebsiteMessage({
          ids: selectedRowKeys,
          status: 'READ'
        });
      } else if (type === 'delete') {
        await userService.websiteMessageReceiverDelByIds(selectedRowKeys);
        setSelectedRowKeys([]);
      } else if (type === 'deleteAll') {
        await userService.websiteMessageUserAllDelMessage({
          receiverId: localStorage.getItem('userId')
        });
        setSelectedRowKeys([]);
      } else if (type === 'readAll') {
        await userService.websiteMessageReceiverUserAllReadMessage({
          status: 'READ',
          receiverId: localStorage.getItem('userId')
        });
        setSelectedRowKeys([]);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSearch([...search]);
      message.success(t('setting-6rYKcbeyyWNd'));
    }
  };

  return (
    <div>
      <header className="flex justify-between items-center mx-[-24px] mt-0 mb-[16px] px-[24px] pt-[16px] pb-0">
        <div className="title">
          <h1>{t('setting-s1Fys6nl01Oa')}</h1>
        </div>
      </header>

      <QueryForList show elements={configElements} onQuery={handleQuery} />

      <div className="bg-[#fff] p-20">
        <Table<Message>
          columns={columns}
          bordered={false}
          rowKey="msgRecordId"
          rowSelection={{
            selectedRowKeys,
            onSelect: handleRowSelection,
            onSelectAll: handleSelectAll
          }}
          {...tableProps}
          pagination={{
            ...PAGINATION_CONFIG,
            ...tableProps.pagination
          }}
        />
      </div>
      <div className="bg-[#fff] p-20 flex justify-end gap-20">
        <Button type="primary" onClick={() => clickButton('delete')} disabled={_.isEmpty(selectedRowKeys)}>
          {t('setting-dFwgHoVapjUG')}
        </Button>
        <Button type="primary" onClick={() => clickButton('read')} disabled={_.isEmpty(selectedRowKeys)}>
          {t('setting-URCSl4UhYstc')}
        </Button>
        <Button
          type="primary"
          onClick={() => {
            Modal.confirm({
              title: t('setting-nlPRBf3m4wbq'),
              content: (
                <>
                  <p>{t('setting-yVOCbjnOh0eN')}</p>
                  <p>{t('setting-MJlnNIzmfDVo')}</p>
                </>
              ),
              okText: t('setting-WTIhRZZjYGxR'),
              cancelText: t('setting-DXTyt99HiSRG'),
              okType: 'danger',
              onOk: async () => {
                try {
                  clickButton('deleteAll');
                } catch (error) {
                  console.error(error);
                }
              }
            });
          }}
        >
          {t('setting-nlPRBf3m4wbq')}
        </Button>
        <Button
          type="primary"
          onClick={() => {
            Modal.confirm({
              title: t('setting-ZkiFtd69CQAj'),
              content: (
                <>
                  <p>{t('setting-wntVLAYoyPvX')}</p>
                </>
              ),
              okText: t('setting-WTIhRZZjYGxR'),
              cancelText: t('setting-DXTyt99HiSRG'),
              okType: 'danger',
              onOk: async () => {
                try {
                  clickButton('readAll');
                } catch (error) {
                  console.error(error);
                }
              }
            });
          }}
        >
          {t('setting-ZkiFtd69CQAj')}
        </Button>
      </div>
      <div className="bg-[#fff] p-20 text-16">
        <div className="text-16 font-bold">{t('setting-wuogY6Ii34jq')}</div>
        <div>{t('setting-rDP6LQf02gE2')}</div>
        <div>
          <div>{t('setting-SHeeAjJPufGR')}</div>
          <div>{t('setting-QUbBpexbtrYc')}</div>
        </div>
      </div>
    </div>
  );
};

export default WebsiteMessage;
