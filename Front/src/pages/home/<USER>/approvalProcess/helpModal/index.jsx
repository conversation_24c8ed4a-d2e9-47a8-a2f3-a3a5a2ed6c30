import {
  ArrowRightOutlined,
  AuditOutlined,
  FormOutlined,
  GroupOutlined,
  LogoutOutlined,
  PlayCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import HelpImg1 from 'assets/images/helpImg1.png';
import HelpImg2 from 'assets/images/helpImg2.png';
import HelpImg3 from 'assets/images/helpImg3.png';
import HelpImg4 from 'assets/images/helpImg4.png';
import HelpImg5 from 'assets/images/helpImg5.png';

import { Drawer, Image } from 'antd';
import React from 'react';
import { t } from '@/utils/translation';

const HelpModal = (props) => {
  const { helpOpen, onClose } = props;
  return (
    <Drawer open={helpOpen} onClose={onClose} title={t('approve-VKhwiPx4tFKP')} width={880} className="helpDrawer">
      <div className="flex h-full flex-col">
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('approve-rqZt6BFMSQ4M')}
          </div>
          <div className="flex flex-col gap-[16px]">
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1EC78B', borderRadius: '50%', width: 24, height: 24 }}
              >
                <PlayCircleOutlined />
              </div>
              <div>{t('approve-1PkwDD6qaKur')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1EC78B', borderRadius: '50%', width: 24, height: 24 }}
              >
                <LogoutOutlined />
              </div>
              <div>{t('approve-XpqgQbjkm7Zl')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1EC78B', borderRadius: 6, width: 24, height: 24 }}
              >
                <UserOutlined />
              </div>
              <div>{t('approve-rndqOM0MlAyc')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1890FF', borderRadius: 6, width: 24, height: 24 }}
              >
                <AuditOutlined />
              </div>
              <div>{t('approve-OMu2FcCtjdw0')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#FAAD14', borderRadius: 6, width: 24, height: 24 }}
              >
                <FormOutlined />
              </div>
              <div>{t('approve-qZTCwSraY8gA')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#FAAD14', borderRadius: 6, width: 24, height: 24 }}
              >
                <GroupOutlined />
              </div>
              <div>{t('approve-HEAsaw5vYfIA')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#000000A6', borderRadius: '50%', width: 24, height: 24 }}
              >
                <ArrowRightOutlined />
              </div>
              <div>{t('approve-eMTZywrH1JuA')}</div>
            </div>
          </div>
        </div>
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('approve-9RUloodHzQiy')}
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg1} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>{t('approve-AXPxyNxOZwxq')}</div>
            <div>{t('approve-atpk8lKZ2OYU')}</div>
            <div>{t('approve-zUZt4Ty3076Q')}</div>
            <div>{t('approve-K71hz2zpWQHH')}</div>
            <div>{t('approve-a5okyekZeiZJ')}</div>
          </div>
        </div>
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('approve-pPj1VnUyB1Et')}
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg2} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>{t('approve-42nnq9AtjQm6')}</div>
            <div>{t('approve-jSwsuEZJBuIj')}</div>
            <div>{t('approve-9n31Be1Ex82K')}</div>
          </div>
        </div>
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('approve-Za7JBfSvS8M0')}
          </div>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('approve-5bRcVjMcqA7o')}
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg3} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>{t('approve-bgGWB49uCWxm')}</div>
            <div>{t('approve-jw7rUpXSXlFm')}</div>
            <div>{t('approve-yW7VuRHHhc9p')}</div>
          </div>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8, marginTop: 24 }}>
            {t('approve-gjrzBoET8D5g')}
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg5} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>{t('approve-fGTvLAcjd4HM')}</div>
            <div>{t('approve-0ymzxNFdD3GT')}</div>
            <div>{t('approve-oQUJV5MS6AHF')}</div>
          </div>
        </div>
        <div style={{ marginTop: 8 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('approve-OMjMttYSKP1Q')}
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg4} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16, marginBottom: 24 }}>
            <div>{t('approve-KxH61C7MGF2G')}</div>
            <div>{t('approve-VChQCaL1J0o1')}</div>
            <div>{t('approve-Pwi5FEZ0FDqX')}</div>
            <div>{t('approve-wOMxOXt6RlyC')}</div>
            <div>{t('approve-fuDEWXZXvS7x')}</div>
            <div>{t('approve-IkVD5QI8YLM5')}</div>
          </div>
        </div>
      </div>
    </Drawer>
  );
};

export default HelpModal;
