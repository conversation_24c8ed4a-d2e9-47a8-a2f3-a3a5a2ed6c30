import React from 'react';

import { Al<PERSON>, Drawer, Form } from 'antd';
import Tooltip from 'antd/es/tooltip';
import _ from 'lodash';
import { t } from '@/utils/translation';
import '../../index.scss';

const NodeDrawer = (props) => {
  const { nodeOpen, onClose, nodeInfo } = props;

  const [form] = Form.useForm();

  const onNodeCancel = () => {
    onClose();
  };

  return (
    <Drawer
      title={t('approve-afL0mXdRbMnT')}
      open={nodeOpen}
      onClose={onNodeCancel}
      width={600}
      className="approveDrawer"
    >
      <>
        <div className="content">
          <Alert message={t('approve-W0EGXsxW3fXM')} type="info" showIcon />

          <div className="approverContent">
            <div className="ConnectorPanel">
              <div className="TopLine" />
              <div className="VLine" />
              <div className="BottomLine" />
              <div className="w-[32px] h-[32px] flex justify-center items-center text-[12px] bg-[#FF6800] rounded-[6px] text-[#fff] Connector">
                {t('approve-JPfDhWb4SxnL')}
              </div>
            </div>
            <div className="FilterList">
              <Form form={form} name="approveForm">
                <Form.Item name="candidateGroupList" rules={[{ required: false }]} label={t('approve-MhdtH5vwMqJm')}>
                  {nodeInfo && !_.isEmpty(nodeInfo.candidateGroupList) ? (
                    <Tooltip
                      placement="topLeft"
                      title={nodeInfo.candidateGroupList.map((item) => item.name).join('、')}
                    >
                      <div className="text-ellipsis whitespace-nowrap overflow-hidden w-[438px]">
                        {nodeInfo.candidateGroupList.map((item) => item.name).join('、')}
                      </div>
                    </Tooltip>
                  ) : (
                    '-'
                  )}
                </Form.Item>
                <Form.Item
                  name="candidateRoleGroupList"
                  rules={[{ required: false }]}
                  label={t('approve-7ra9PHlm01Nq')}
                >
                  {nodeInfo && !_.isEmpty(nodeInfo.candidateRoleGroupList) ? (
                    <Tooltip
                      placement="topLeft"
                      title={nodeInfo.candidateRoleGroupList.map((item) => item.name).join('、')}
                    >
                      <div className="text-ellipsis whitespace-nowrap overflow-hidden w-[438px]">
                        {nodeInfo.candidateRoleGroupList.map((item) => item.name).join('、')}
                      </div>
                    </Tooltip>
                  ) : (
                    '-'
                  )}
                </Form.Item>
                <Form.Item name="candidateUserList" rules={[{ required: false }]} label={t('approve-wTTHNcfeAxc6')}>
                  {nodeInfo && !_.isEmpty(nodeInfo.candidateUserList) ? (
                    <Tooltip placement="topLeft" title={nodeInfo.candidateUserList.map((item) => item.name).join('、')}>
                      <div className="text-ellipsis whitespace-nowrap overflow-hidden w-[410px]">
                        {nodeInfo.candidateUserList.map((item) => item.name).join('、')}
                      </div>
                    </Tooltip>
                  ) : (
                    '-'
                  )}
                </Form.Item>
              </Form>
            </div>
          </div>
        </div>
      </>
    </Drawer>
  );
};

export default NodeDrawer;
