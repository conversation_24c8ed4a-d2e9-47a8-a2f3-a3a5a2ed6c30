import { <PERSON>raph, Shape } from '@antv/x6';
import { register } from '@antv/x6-react-shape';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import MyToDoListService from 'service/myToDoListService';
import { t } from '@/utils/translation';

import { Button, Select, Tooltip } from 'antd';

import {
  AimOutlined,
  AuditOutlined,
  EditOutlined,
  FormOutlined,
  GroupOutlined,
  LogoutOutlined,
  MinusOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  UserOutlined
} from '@ant-design/icons';

import '../index.scss';
import { ports } from './config';
import EdgeDrawer from './edgeDrawer';
import HelpModal from './helpModal';
import NodeDrawer from './nodeDrawer';

const { Option } = Select;

const typeEnum = {
  startEvent: {
    icon: <PlayCircleOutlined />,
    title: t('approve-rZvtIJKAyDfa'),
    style: { backgroundColor: '#1EC78B', borderRadius: '50%' }
  },
  endEvent: {
    icon: <LogoutOutlined />,
    title: t('approve-bMiohBG5R94F'),
    style: { backgroundColor: '#1EC78B', borderRadius: '50%' }
  },
  userTask: {
    icon: <AuditOutlined />,
    title: t('approve-JT2kMDYvQjS0'),
    style: { backgroundColor: '#1890FF', borderRadius: 6 }
  },
  approvalTask: {
    icon: <UserOutlined />,
    title: t('approve-5pq16hJ4nnMJ'),
    style: { backgroundColor: '#1EC78B', borderRadius: 6 }
  },
  parallelGateway: {
    icon: <FormOutlined />,
    title: t('approve-3GrVjDRmzfC1'),
    style: { backgroundColor: '#FAAD14', borderRadius: 6 }
  },
  // parallelGateway: {
  //   icon: <CloseSquareOutlined />,
  //   title: '或签',
  //   style: { backgroundColor: '#FAAD14', borderRadius: 6, transform: 'rotate(45deg)' }
  // }
  subProcess: {
    icon: <GroupOutlined />,
    title: (
      <div>
        <span style={{ marginRight: '4px' }}>{t('approve-xR8HeqBv0PPw')}</span>
        <Tooltip title={t('approve-1efXgB608hJe')}>
          <QuestionCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
        </Tooltip>
      </div>
    ),
    style: { backgroundColor: '#FAAD14', borderRadius: 6 }
  }
};

const ApprovalProcess = (props) => {
  const [canvaGrape, setCanvaGrape] = useState(null);
  const [nodeOpen, setNodeOpen] = useState(false);
  const [edgeOpen, setEdgeOpen] = useState(false);
  const [nodeInfo, setNodeInfo] = useState({});
  const [helpOpen, setHelpOpen] = useState(false);
  const [edgeInfo, setEdgeInfo] = useState({});

  const [scaleFator, setScaleFator] = useState(1);
  const [scaleSelectVal, setScaleSelectVal] = useState('100%');

  const { deployKey } = props;

  useEffect(() => {
    const initGrape = () => {
      const container = document.getElementById('containers');

      const graphContainer = document.createElement('div');
      graphContainer.id = 'graph-container';

      container.appendChild(graphContainer);

      // #region {t('approve-PP4ZUVU0oxEj')}
      const graph = new Graph({
        container: graphContainer,
        grid: true,
        highlight: true,
        autoResize: true,
        embedding: {
          enabled: true,
          findParent({ node }) {
            const bbox = node.getBBox();
            return this.getNodes().filter((itemNode) => {
              const data = itemNode.getData();
              if (data && data.type === 'subProcess') {
                const targetBBox = itemNode.getBBox();
                return bbox.isIntersectWithRect(targetBBox);
              }
              return false;
            });
          },
          validate: ({ child }) => {
            if (child.store.data.shape === 'userTask' && graph.getSelectedCells().length <= 1) {
              return true;
            } else {
              return false;
            }
          }
        },
        panning: {
          enabled: true
        },
        mousewheel: {
          enabled: true,
          zoomAtMousePosition: true,
          modifiers: 'ctrl',
          minScale: 0.5,
          maxScale: 3
        },
        connecting: {
          router: 'manhattan',
          allowLoop: false,
          connector: {
            name: 'rounded',
            args: {
              radius: 8
            }
          },
          anchor: 'center',
          connectionPoint: 'anchor',
          allowBlank: false,
          snap: {
            radius: 20
          },
          createEdge() {
            return new Shape.Edge({
              attrs: {
                line: {
                  stroke: '#A2B1C3',
                  strokeWidth: 2,
                  targetMarker: {
                    name: 'block',
                    width: 12,
                    height: 8
                  }
                }
              },
              zIndex: 0
            });
          }
        },
        interacting: {
          nodeMovable: false
        },
        highlighting: {
          magnetAdsorbed: {
            name: 'stroke',
            args: {
              attrs: {
                strokeWidth: 4,
                fill: '#1890FF',
                stroke: '#1890FF'
              }
            }
          }
        }
      });

      setCanvaGrape(graph);
    };

    document.getElementById('containers') && initGrape();
  }, []);

  useEffect(() => {
    const grapeEdgeNodes = async () => {
      try {
        const approvalRes = await MyToDoListService.getApprovalObject({ id: Number(deployKey) });

        const graphContainer = document.getElementById('graph-container');

        canvaGrape.on('edge:dblclick', async ({ cell }) => {
          const { store } = cell;
          const { source, target } = store.data;

          const _canvasJSON = _.cloneDeep(canvaGrape.toJSON());
          const { cells } = _canvasJSON;

          const nodeSourceType = cells.find((item) => item.id === source.cell).shape;
          const nodeTargetType = cells.find((item) => item.id === target.cell).shape;

          if (
            (nodeSourceType === 'approvalTask' && nodeTargetType === 'userTask') ||
            (nodeSourceType === 'userTask' && nodeTargetType === 'userTask') ||
            (nodeSourceType === 'approvalTask' && nodeTargetType === 'subProcess') ||
            (nodeSourceType === 'subProcess' && nodeTargetType === 'subProcess') ||
            (nodeSourceType === 'userTask' && nodeTargetType === 'subProcess') ||
            (nodeSourceType === 'subProcess' && nodeTargetType === 'userTask')
          ) {
            setEdgeOpen(true);

            setEdgeInfo(
              store.data.parameterListVo
                ? {
                    ...store.data,
                    approvalObjData: approvalRes
                  }
                : {
                    ...store.data,
                    data: { type: 'edge', parameterListVo: { businessRule: false } },
                    parameterListVo: { businessRule: false },
                    approvalObjData: approvalRes
                  }
            );
          }
        });

        canvaGrape.on('node:moving', ({ node }) => {
          if (_.isEmpty(node.store.data.children)) {
            node.toFront();
          }
        });

        canvaGrape.on('node:moved', ({ node }) => {
          if (node.store.data.parent) {
            node.removePorts();
          } else if (!node.hasPorts()) {
            node.addPorts(ports.items);
          }
        });

        if (_.isEmpty(approvalRes.json)) {
          const res = canvaGrape.addNode({
            shape: 'startEvent',
            x: graphContainer.offsetWidth / 4 + 25,
            y: graphContainer.offsetHeight / 2 - 50,
            data: { type: 'startEvent' },
            width: 100,
            height: 100,
            label: t('approve-rZvtIJKAyDfa'),
            component: NodeComponent,
            ports: { ...ports }
          });

          const res2 = canvaGrape.addNode({
            shape: 'approvalTask',
            x: graphContainer.offsetWidth / 4 + 200 + 25,
            y: graphContainer.offsetHeight / 2 - 50,
            data: { type: 'approvalTask' },
            width: 100,
            height: 100,
            label: t('approve-5pq16hJ4nnMJ'),
            component: NodeComponent,
            ports: { ...ports }
          });

          const res3 = canvaGrape.addNode({
            shape: 'userTask',
            x: graphContainer.offsetWidth / 4 + 400 + 25,
            y: graphContainer.offsetHeight / 2 - 50,
            data: { type: 'userTask' },
            width: 100,
            height: 100,
            label: t('approve-JT2kMDYvQjS0'),
            component: NodeComponent,
            ports: { ...ports }
          });

          const res4 = canvaGrape.addNode({
            shape: 'endEvent',
            x: graphContainer.offsetWidth / 4 + 600 + 25,
            y: graphContainer.offsetHeight / 2 - 50,
            data: { type: 'endEvent' },
            width: 100,
            height: 100,
            label: t('approve-bMiohBG5R94F'),
            component: NodeComponent,
            ports: { ...ports }
          });

          canvaGrape.addEdge({
            shape: 'edge',
            // data: { type: 'edge', parameterListVo: { businessRule: false } },
            parameterListVo: { businessRule: false },
            source: {
              cell: res.id,
              port: res.port.ports.find((item) => item.group === 'right').id
            },
            target: {
              cell: res2.id,
              port: res2.port.ports.find((item) => item.group === 'left').id
            },
            attrs: {
              line: {
                stroke: '#A2B1C3',
                strokeWidth: 2,
                targetMarker: {
                  name: 'block',
                  width: 12,
                  height: 8
                }
              }
            },
            component: NodeComponent,
            ports: { ...ports }
          });

          canvaGrape.addEdge({
            shape: 'edge',
            // data: { type: 'edge', parameterListVo: { businessRule: false } },
            parameterListVo: { businessRule: false },
            source: {
              cell: res2.id,
              port: res2.port.ports.find((item) => item.group === 'right').id
            },
            target: {
              cell: res3.id,
              port: res3.port.ports.find((item) => item.group === 'left').id
            },
            attrs: {
              line: {
                stroke: '#A2B1C3',
                strokeWidth: 2,
                targetMarker: {
                  name: 'block',
                  width: 12,
                  height: 8
                }
              }
            },
            component: NodeComponent,
            ports: { ...ports }
          });

          canvaGrape.addEdge({
            shape: 'edge',
            // data: { type: 'edge', parameterListVo: { businessRule: false } },
            parameterListVo: { businessRule: false },
            source: {
              cell: res3.id,
              port: res3.port.ports.find((item) => item.group === 'right').id
            },
            target: {
              cell: res4.id,
              port: res4.port.ports.find((item) => item.group === 'left').id
            },
            attrs: {
              line: {
                stroke: '#A2B1C3',
                strokeWidth: 2,
                targetMarker: {
                  name: 'block',
                  width: 12,
                  height: 8
                }
              }
            },
            component: NodeComponent,
            ports: { ...ports }
          });
        } else {
          const approvalFlowData = JSON.parse(approvalRes.json);

          let _approvalFlowData = _.cloneDeep(approvalFlowData);

          _approvalFlowData.forEach((item) => {
            if (item.shape === 'subProcess') {
              _approvalFlowData = [..._approvalFlowData, ...item.subProcessList];
            }
            if (item.shape === 'sequenceFlow') {
              item.shape = 'edge';
            }
          });
          canvaGrape.fromJSON(_approvalFlowData);
        }

        resizeGraph();
      } catch (err) {
        console.error(err);
      }
    };
    canvaGrape && grapeEdgeNodes();
  }, [canvaGrape]);

  const NodeComponent = ({ node }) => {
    const data = node?.getData();

    const onNodeEdit = async () => {
      const { store } = node;
      setNodeOpen(true);

      setNodeInfo({
        ...store.data
      });
    };

    return (
      <div
        className={`react-node h-full bg-[#fff] rounded-[6px] flex flex-col ${data.type === 'subProcess' ? 'contentParentNode' : 'contentNode'} relative`}
        style={{ justifyContent: 'center', alignItems: 'center' }}
      >
        <div
          className={`flex ${data.type === 'subProcess' ? 'parentTitleWrap' : 'titleWrap'}`}
          style={{ alignItems: 'center', flexDirection: 'column' }}
        >
          <div
            className="w-[32px] h-[32px] flex justify-center items-center text-[#fff] text-[16px] mb-[4px]"
            style={typeEnum[data.type].style}
          >
            {typeEnum[data.type].icon}
          </div>
          {data.type === 'userTask' &&
            (!_.isEmpty(data.candidateGroupList) ||
              !_.isEmpty(data.candidateUserList) ||
              !_.isEmpty(data.candidateRoleGroupList)) && (
              <div className="editIcon" onClick={onNodeEdit}>
                <EditOutlined />
              </div>
            )}

          <div className="text-[12px]">{typeEnum[data.type].title}</div>
        </div>

        {_.isEmpty(data.candidateGroupList) &&
        _.isEmpty(data.candidateUserList) &&
        _.isEmpty(data.candidateRoleGroupList) ? null : (
          <div className="w-full" style={{ padding: '0 12px' }}>
            <Tooltip
              title={
                _.isEmpty(data.candidateGroupList) ? '' : data.candidateGroupList.map((item) => item.name).join(',')
              }
            >
              <div className="text-[12px] text-[rgba(0,0,0.65)] mb-[4px] text-ellipsis overflow-hidden whitespace-nowrap">
                {t('approve-5WUxcufEBInY')}
                {_.isEmpty(data.candidateGroupList)
                  ? '-'
                  : data.candidateGroupList.map((item) => item.name).join(',')}{' '}
              </div>
            </Tooltip>
            <Tooltip
              title={
                _.isEmpty(data.candidateRoleGroupList)
                  ? ''
                  : data.candidateRoleGroupList.map((item) => item.name).join(',')
              }
            >
              <div className="text-[12px] text-[rgba(0,0,0.65)] mb-[4px] text-ellipsis overflow-hidden whitespace-nowrap">
                {t('approve-vbrpwxw5wc9R')}
                {_.isEmpty(data.candidateRoleGroupList)
                  ? '-'
                  : data.candidateRoleGroupList.map((item) => item.name).join(',')}
              </div>
            </Tooltip>

            <Tooltip
              title={_.isEmpty(data.candidateUserList) ? '' : data.candidateUserList.map((item) => item.name).join(',')}
            >
              <div className="text-[12px] text-[rgba(0,0,0.65)] text-ellipsis overflow-hidden whitespace-nowrap">
                {t('approve-Jj0zePRymoMs')}
                {_.isEmpty(data.candidateUserList)
                  ? '-'
                  : data.candidateUserList.map((item) => item.name).join(',')}{' '}
              </div>
            </Tooltip>
          </div>
        )}
      </div>
    );
  };

  register({
    shape: 'startEvent',
    width: 100,
    height: 100,
    component: NodeComponent,
    ports: { ...ports }
  });

  register({
    shape: 'endEvent',
    width: 100,
    height: 100,
    component: NodeComponent,
    ports: { ...ports }
  });

  register({
    shape: 'userTask',
    width: 100,
    height: 100,
    effect: ['data'],
    component: NodeComponent,
    ports: { ...ports }
  });

  register({
    shape: 'approvalTask',
    width: 100,
    height: 100,
    component: NodeComponent,
    ports: { ...ports }
  });

  register({
    shape: 'parallelGateway',
    width: 100,
    height: 100,
    component: NodeComponent,
    ports: { ...ports }
  });

  register({
    shape: 'subProcess',
    width: 208,
    height: 224,
    component: NodeComponent,
    zIndex: -9999999,
    ports: { ...ports }
  });

  const onScalToolChange = (val) => {
    canvaGrape.zoomTo(parseFloat(val) / 100);
    setScaleSelectVal(val);
    setScaleFator(parseFloat(val) / 100);
  };

  const onPlusScale = () => {
    if (scaleFator < 0.25) {
      setScaleFator(Number((scaleFator + 0.1).toFixed(2)));
      setScaleSelectVal(`${Number((scaleFator + 0.1).toFixed(2)) * 100}%`);

      canvaGrape.zoomTo(Number((scaleFator + 0.1).toFixed(2)));
    } else {
      setScaleFator(scaleFator + 0.25);
      setScaleSelectVal(`${(scaleFator + 0.25) * 100}%`);

      canvaGrape.zoomTo(scaleFator + 0.25);
    }
  };

  const onSmChange = () => {
    if (scaleFator <= 0.25) {
      setScaleFator(Number((scaleFator - 0.1).toFixed(2)));
      setScaleSelectVal(`${Number(scaleFator - 0.1).toFixed(2) * 100}%`);

      canvaGrape.zoomTo(Number(scaleFator - 0.1).toFixed(2));
    } else {
      setScaleFator(scaleFator - 0.25);
      setScaleSelectVal(`${(scaleFator - 0.25) * 100}%`);

      canvaGrape.zoomTo(scaleFator - 0.25);
    }
  };

  const resizeGraph = () => {
    canvaGrape.centerContent();

    setScaleFator(1);
    setScaleSelectVal('100%');

    canvaGrape.zoomTo(1);

    const { tx, ty } = canvaGrape.translate();

    canvaGrape.translate(tx + 30, ty);
  };

  const onNodeCancel = () => {
    setNodeInfo({});
    setNodeOpen(false);
  };

  const onEdgeCancel = () => {
    setEdgeInfo({});
    setEdgeOpen(false);
  };
  const onNodeConfirm = (values) => {
    const _flowData = _.cloneDeep(canvaGrape.toJSON());

    const { cells } = _flowData;

    const _cells = _.cloneDeep(cells);

    _cells.forEach((item) => {
      if (item.id === nodeInfo.id) {
        item.candidateUserList = values.candidateUserList;
        item.candidateRoleGroupList = values.candidateRoleGroupList;
        item.candidateGroupList = values.candidateGroupList;
        item.data = { ...values, type: item.shape };

        if (
          !_.isEmpty(values.candidateUserList) ||
          !_.isEmpty(values.candidateRoleGroupList) ||
          !_.isEmpty(values.candidateGroupList)
        ) {
          item.width = 160;
          item.height = 160;
        }
      }
    });

    canvaGrape.fromJSON(_cells);

    setNodeOpen(false);
  };

  const onEdgeConfirm = (values) => {
    const _flowData = _.cloneDeep(canvaGrape.toJSON());

    const { cells } = _flowData;

    const _cells = _.cloneDeep(cells);

    _cells.forEach((item) => {
      if (item.id === edgeInfo.id) {
        item.parameterListVo = values;
        item.data = { type: item.shape, parameterListVo: values };
      }
    });
    canvaGrape.fromJSON(_cells);

    setEdgeOpen(false);
  };

  return (
    <>
      <div className="flowEditor">
        <div className="h-full relative">
          <div
            style={{
              height: '100%',
              width: '100%'
            }}
          >
            <div id="containers" />
          </div>

          <div className="scaleToolsWrap">
            <div className={scaleFator === 0.05 ? 'scaleSm scaleDisable' : 'scaleSm'} onClick={onSmChange}>
              <MinusOutlined />
            </div>
            <Select
              bordered={false}
              value={scaleSelectVal}
              suffixIcon={null}
              onChange={onScalToolChange}
              popupClassName="scaleToolSelectWrap"
            >
              <Option value="400%">400%</Option>
              <Option value="300%">300%</Option>
              <Option value="200%">200%</Option>
              <Option value="150%">150%</Option>
              <Option value="100%">100%</Option>
              <Option value="50%">50%</Option>
              <Option value="25%">25%</Option>
            </Select>
            <div className={scaleFator === 4 ? 'scaleBg scaleDisable' : 'scaleBg'} onClick={onPlusScale}>
              <PlusOutlined />
            </div>
            <div className="gapLine" />
            <Tooltip title={t('approve-LMBzrBOxTIw8')} overlayClassName="border_6_Tooltip">
              <div onClick={resizeGraph} className="resizeWrap">
                <AimOutlined />
              </div>
            </Tooltip>
          </div>

          <div className="help" onClick={() => setHelpOpen(true)}>
            <Button type="primary">
              <QuestionCircleOutlined />
              <span>{t('approve-83cTZ80nnxFP')}</span>
            </Button>
          </div>
        </div>
      </div>

      {nodeOpen && <NodeDrawer onClose={onNodeCancel} nodeOpen onNodeConfirm={onNodeConfirm} nodeInfo={nodeInfo} />}
      {edgeOpen && <EdgeDrawer onClose={onEdgeCancel} edgeOpen onEdgeConfirm={onEdgeConfirm} edgeInfo={edgeInfo} />}
      {helpOpen && <HelpModal onClose={() => setHelpOpen(false)} helpOpen={helpOpen} />}
    </>
  );
};

export default ApprovalProcess;
