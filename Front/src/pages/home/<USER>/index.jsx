import { Badge, Tabs } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import UserService from 'service/UserService';
import MyToDoListService from 'service/myToDoListService';
import { t } from '@/utils/translation';
import MyToDoListDone from './done';
import './index.scss';
import MyToDoListRunning from './running';
import MyToDoListStart from './start';

const userService = new UserService();

const mapState = (store) => {
  return { systemInfo: store.systemInfo, messageInfo: store.messageInfo };
};

const ApprovalCenter = (props) => {
  const {
    location: { state }
  } = props;

  const [tabKey, setTabKey] = useState(state?.tabKey || '1');
  const [dictTypeList, setDictTypeList] = useState([]);
  const [userList, setUserList] = useState([]);
  const [startCount, setStartCount] = useState(null);
  const [runningCount, setRunningCount] = useState(null);
  const [reflash, setReflash] = useState(false);

  useEffect(() => {
    const getDictTypeList = async () => {
      try {
        const res =
          tabKey === '2'
            ? await MyToDoListService.getDictTypeV2({
                page: 1,
                size: 9999,
                sorts: [
                  {
                    direction: 'desc',
                    propertyName: 'updateTime'
                  }
                ],
                search: [
                  {
                    propertyName: 'pcode',
                    operator: 'EQ',
                    value: 0
                  }
                ]
              })
            : await MyToDoListService.getDictType([
                {
                  operator: 'EQ',
                  propertyName: 'businessType',
                  value: 'APPROVAL'
                },
                {
                  operator: 'EQ',
                  propertyName: 'code',
                  value: 'sys_approval_scope'
                }
              ]);

        setDictTypeList(tabKey === '2' ? res.content : res);

        const redata = await userService.listBy([]);
        setUserList(redata);
      } catch (err) {
        console.error(err);
      }
    };

    getDictTypeList();
  }, [tabKey]);

  useEffect(() => {
    const getStartCount = async () => {
      const finalParam = _.cloneDeep({
        page: 1,
        search: [
          {
            operator: 'EQ',
            propertyName: 'projectId',
            value: localStorage.getItem('projectId')
          },
          { operator: 'EQ', propertyName: 'promoterId', value: localStorage.getItem('userId') },
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          },
          {
            operator: 'EQ',
            propertyName: 'approvalType',
            value: 'ACTIVITI'
          },
          {
            operator: 'EQ',
            propertyName: 'status',
            value: 'RUNNING'
          }
        ],
        size: 10,
        sorts: [{ propertyName: 'createTime', direction: 'desc' }]
      });

      const startRes = await MyToDoListService.query2(finalParam);

      const runningRes = await MyToDoListService.acitvityQuery({
        companyId: Number(localStorage.getItem('organizationId')),
        isSuperAdmin: !!JSON.parse(localStorage.getItem('superAdmin'))
      });

      setStartCount(startRes.totalElements);
      setRunningCount(runningRes.dataList.length);
    };

    getStartCount();
  }, [reflash]);

  const items = [
    {
      label: (
        <Badge count={startCount} offset={[8, -4]}>
          {t('setting-OZohxg4DP5cu')}
        </Badge>
      ),
      key: '1',
      children: (
        <MyToDoListStart
          props={props}
          tabKey={tabKey}
          dictTypeList={dictTypeList}
          userList={userList}
          reflash={reflash}
          setReflash={setReflash}
        />
      )
    },
    {
      label: (
        <Badge count={runningCount} offset={[8, -4]}>
          {t('setting-0ZlBLIxFqQRy')}
        </Badge>
      ),
      key: '2',
      children: (
        <MyToDoListRunning
          props={props}
          tabKey={tabKey}
          dictTypeList={dictTypeList}
          userList={userList}
          reflash={reflash}
          setReflash={setReflash}
        />
      )
    },
    {
      label: t('setting-4c1LwXQHeqKP'),
      key: '3',
      children: <MyToDoListDone props={props} tabKey={tabKey} dictTypeList={dictTypeList} userList={userList} />
    }
  ];

  const onTabChange = (key) => {
    setTabKey(key);
  };

  return (
    <div className="todoList">
      <header>
        <h1>{t('setting-u00XgSxbIyFv')}</h1>
      </header>
      <Tabs activeKey={tabKey} onChange={onTabChange} items={items} />
    </div>
  );
};

export default connect(mapState)(ApprovalCenter);
