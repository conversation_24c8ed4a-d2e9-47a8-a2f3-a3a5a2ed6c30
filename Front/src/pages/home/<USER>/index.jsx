import React, { Component } from 'react';
import DataManageService from 'service/datamanageservice';
import './index.scss';

export default class index extends Component {
  state = {
    imgUrl: ''
  };

  componentDidMount() {
    this.list();
  }

  async list() {
    const params = {
      projectId: ''
    };
    const result = await DataManageService.weibangetUrlForIFrame(params);

    this.setState({
      imgUrl: result.loginUrl
    });
  }

  render() {
    return (
      <div className="weiban">
        <div id="iframe-body">
          <iframe title="weiban" id="iframe-b" frameBorder="0" scrolling="no" width="100%" src={this.state.imgUrl} />
        </div>
      </div>
    );
  }
}
