import qs from 'querystring';
import React, { Component } from 'react';
import DataManageService from 'service/datamanageservice';

export default class index extends Component {
  state = {
    Authorization: ''
  };

  componentDidMount() {
    const { search } = this.props.location;
    const { Authorization } = qs.parse(search.slice(1));
    this.setState(
      {
        Authorization
      },
      () => {
        this.list();
      }
    );
  }

  list = async () => {
    const params = {
      origin: window.location.origin
    };
    const res = await DataManageService.getUrlForIFrame(params);
    if (res.loginUrl) {
      // console.log("loginUrl",res.loginUrl)
      // localStorage.setItem("AuthorizationUrl",this.state.Authorization)
      // window.location.href = res.loginUrl
      this.props.history.push({
        pathname: '/aimarketer/home/<USER>/materialManage',
        query: { Authorization: this.state.Authorization }
      });
    }
  };

  render() {
    return <div />;
  }
}
