import { ExclamationCircleOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Input, Modal, Select, Table, message } from 'antd';
import ColumnActionCom from 'components/featurecoms/tableactioncom/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import UserService from 'service/UserService';
import BusinessEntity from 'service/businessEntity';
import BusinessVariable from 'service/businessVariable';
import CheckAuth from 'utils/checkAuth';
import getMenuTitle from 'utils/menuTitle';
import { setNativeValue } from 'utils/universal';
import './List.scss';
import { entityMap, initParam } from './config';
import EditScenario from './create';
import FilterComponents from './filterComponents';
import { t } from 'utils/translation';

const userService = new UserService();
const { confirm } = Modal;
const pagination = {
  showTotal: (totals) => `${t('operationCenter-In1O6JxcwUZL')} ${totals} ${t('operationCenter-5C3P8LLK7VBf')}`,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const stateToProps = (state) => {
  return {
    meunData: state.meunData.menus.main
  };
};

const TagList = (props) => {
  const [param, setParam] = useState(_.cloneDeep(initParam));
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editModalInfo, setEditModalInfo] = useState({
    visible: false,
    value: {}
  });

  const [search, setSearch] = useState([]);
  const [searchSelectVal, setSearchSelectVal] = useState('variableName');
  const inputRef = useRef(null);

  const [myFilter, setMyFilter] = useState([]);

  useEffect(() => {
    const getList = async () => {
      try {
        setLoading(true);
        const finalParam = _.cloneDeep(param);
        // finalParam.search.push({ operator: 'EQ', propertyName: 'product', value: 'BMS' });
        const result = await BusinessVariable.query(finalParam);
        pagination.total = result.totalElements;
        pagination.current = param.page;
        pagination.pageSize = param.size;
        setList(result.content);
        setLoading(false);
      } catch (error) {
        console.error(error.message);
        setLoading(false);
      }
    };
    getList();
  }, [param]);

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const editAction = (data, flag) => {
    setEditModalInfo({ ...editModalInfo, ...data });
    if (flag) {
      setParam({ ...param });
    }
  };

  const columns = [
    {
      title: t('operationCenter-hqp5vKPMWO4o'),
      dataIndex: 'entityCode',
      width: 160,
      render: (text) => entityMap[text]
    },
    {
      title: t('operationCenter-zONBL96vunbQ'),
      dataIndex: 'variableName',
      width: 150
    },
    {
      title: t('operationCenter-2r30DL271Cho'),
      dataIndex: 'variableDescribe',
      width: 200
    },
    {
      title: t('operationCenter-lqFLNGPyPd5T'),
      dataIndex: 'updateUserName',
      width: 120,
      ellipsis: true
    },
    {
      title: t('operationCenter-S14Ty5sZdnDe'),
      dataIndex: 'updateTime',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 200,
      sorter: true
    },
    {
      title: t('operationCenter-cyv9BRSHKcTw'),
      className: 'td-set',
      width: 120,
      fixed: 'right',
      render: (text, record) => {
        const featureData = {
          // 特征
          edit: {
            text: t('operationCenter-Yn5y0BZmmWXJ'),
            code: 'aim_business_variable_edit'
          },
          dropdownData: {
            delete: {
              text: t('operationCenter-86EraIQxQwG5'),
              code: 'aim_business_variable_edit'
            }
          }
        };
        return (
          <ColumnActionCom closeDropdown featureData={featureData} record={record} onClick={onColumnActionClick} />
        );
      }
    }
  ];

  const onColumnActionClick = (type, _, record) => {
    if (type === 'edit') {
      setEditModalInfo({ visible: true, value: record });
    } else if (type === 'delete') {
      confirm({
        title: <span style={{ fontWeight: '600' }}>{t('operationCenter-ezezKSbFP8qg')}</span>,
        icon: <ExclamationCircleOutlined />,
        okText: t('operationCenter-jaG6ZhLwoQjy'),
        content: (
          <div>
            <div>
              {t('operationCenter-pvFjPV5AS01B')}：{record.variableDescribe}[{record.variableName}]
            </div>
            <div>{t('operationCenter-yVv5OuU1mTvT')}</div>
          </div>
        ),
        async onOk() {
          try {
            await BusinessVariable.delById(record.id);
            message.success(t('operationCenter-uMcaVvnTzQir'));
            setParam({ ...param });
          } catch (err) {
            console.error(err.message);
          }
        },
        onCancel() {}
      });
    }
  };

  const onTextChange = _.debounce((val) => {
    let _search = _.cloneDeep(search);
    if (searchSelectVal === 'variableName') {
      const index = _.findIndex(_search, (v) => v.propertyName === 'variableName');
      if (index !== -1) {
        _search.splice(index, 1);
      }
      _search = [..._search, { operator: 'LIKE', propertyName: 'variableName', value: val }];
    } else {
      const index = _.findIndex(_search, (v) => v.propertyName === 'variableDescribe');
      if (index !== -1) {
        _search.splice(index, 1);
      }
      _search = [..._search, { operator: 'LIKE', propertyName: 'variableDescribe', value: val }];
    }
    setSearch(_search);
    setParam({ ...param, search: [...myFilter, ..._search], page: 1 });
  }, 500);

  const onSelect = (value) => {
    // inputRef.current.state.value = '';
    setNativeValue([inputRef.current.input], '');
    let _search = _.cloneDeep(search);
    const index = _.findIndex(_search, (v) => v.propertyName === 'variableName');
    if (index !== -1) {
      _search.splice(index, 1);
    }
    _search = [
      ..._search,
      {
        operator: 'EQ',
        propertyName: value,
        value: inputRef.current.input.value
      }
    ];
    setSearch(_search);
    setSearchSelectVal(value);
  };

  const [items, setItems] = useState([
    {
      connector: t('operationCenter-hqp5vKPMWO4o'),
      operator: 'EQ',
      type: 'select',
      label: t('operationCenter-hqp5vKPMWO4o'),
      name: 'entityCode',
      options: []
    },
    {
      connector: t('operationCenter-otP11NNUYub8'),
      type: 'more',
      components: [
        {
          label: t('operationCenter-lqFLNGPyPd5T'),
          type: 'select',
          operator: 'EQ',
          name: 'updateUserId',
          connector: 'AND',
          options: []
        },
        {
          label: t('operationCenter-S14Ty5sZdnDe'),
          type: 'date',
          name: 'updateTime',
          connector: 'AND',
          operator: 'DATE_BETWEEN'
        }
      ]
    }
  ]);

  useEffect(() => {
    (async () => {
      const reData = await Promise.all([BusinessEntity.listBy([]), userService.listBy()]);
      const _items = _.cloneDeep(items);
      _items[0].options = _.map(reData[0], (item) => ({
        label: item.entityName,
        value: item.entityCode
      }));
      _items[1].components[0].options = _.map(reData[1], (item) => ({
        label: item.name,
        value: item.id
      }));
      setItems(_items);
    })();
  }, []);

  const changeFilter = (value) => {
    setMyFilter(value);
    setParam({ ...param, search: [...value, ...search], page: 1 });
  };

  return (
    <div className="VarStyle">
      <header>
        <h1>{getMenuTitle(props.meunData, props.history.location.pathname)}</h1>
        <div className="rightSide">
          <Input.Group compact>
            <Select value={searchSelectVal} onSelect={onSelect}>
              <Select.Option value="variableName">{t('operationCenter-zONBL96vunbQ')}</Select.Option>
              <Select.Option value="variableDescribe">{t('operationCenter-2r30DL271Cho')}</Select.Option>
            </Select>
            <Input
              onChange={(e) => onTextChange(e.target.value)}
              style={{ width: '208px' }}
              suffix={<SearchOutlined />}
              ref={inputRef}
            />
          </Input.Group>
          <CheckAuth code="aim_business_variable_edit">
            <Button
              className="DTButton bor_ra-6"
              type="primary"
              onClick={() => setEditModalInfo({ visible: true, value: {} })}
            >
              {t('operationCenter-x2m22vWP0UvX')}
            </Button>
          </CheckAuth>
        </div>
      </header>
      <div className="filter">
        <div className="smallTitle">{t('operationCenter-WfrDHWHdjEGa')}</div>
        <div className="filterComponents">
          <FilterComponents items={items} onChange={changeFilter} />
        </div>
      </div>
      <div className="tableList">
        <Table
          columns={columns}
          dataSource={list}
          bordered={false}
          loading={loading}
          onChange={handleTableChange}
          pagination={pagination}
          rowKey="id"
          scroll={{ x: 1300 }}
        />
      </div>

      {editModalInfo.visible && <EditScenario {...editModalInfo} action={editAction} />}
    </div>
  );
};

export default connect(stateToProps)(TagList);
