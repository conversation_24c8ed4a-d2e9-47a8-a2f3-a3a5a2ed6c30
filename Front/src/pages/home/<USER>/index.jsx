/* eslint-disable */
import { Select } from 'antd';
import React, { Component } from 'react';
import DataManageService from 'service/datamanageservice';

import './index.scss';

const { Option } = Select;

export default class index extends Component {
  state = {
    Authorization: '',
    imgUrl: '',
    list: [],
    source: 'CREATION',
    type: localStorage.getItem('env') === 'SW' ? 'h5' : 'hd'
  };

  componentDidMount() {
    let is_from_callback = this.props.location.query?.Authorization;
    if (is_from_callback) {
      this.setState(
        {
          Authorization: is_from_callback
        },
        () => {
          this.tabList();
          this.list();
        }
      );
    } else {
      this.tabList();
      this.list();
    }
  }

  tabList = () => {
    let list = [
      {
        id: 1,
        name: '作品',
        flage: true,
        detailziduan: 'CREATION'
      },
      {
        id: 2,
        name: '模板',
        flage: false,
        detailziduan: 'TEMPLATE'
      },
      {
        id: 3,
        name: '企业模板',
        flage: false,
        detailziduan: 'TEMPLATE_ENTERPRISE'
      }
    ];
    this.setState({
      list
    });
  };

  list = async () => {
    const { source, type } = this.state;
    let params = {};
    if (!this.state.Authorization) {
      params = {
        origin: window.location.origin
      };
    } else {
      params = {
        authorization: this.state.Authorization,
        type,
        origin: window.location.origin,
        source
      };
    }
    const res = await DataManageService.getUrlForIFrame(params);

    let imgUrl = '';
    if (res.loginUrl) {
      window.location.href = res.loginUrl;
    } else {
      imgUrl = res.iframeUrl;
      window.addEventListener('message', this.receiveMessage, false);
    }
    this.setState(
      {
        imgUrl
      },
      () => {
        this.iframeClick();
      }
    );
  };

  //  iframe 通讯
  receiveMessage = async (event) => {
    if (event.data.eventType === 'quit') {
      const { source, type } = this.state;
      let params = {};
      params = {
        authorization: this.state.Authorization,
        type,
        origin: window.location.origin,
        source
      };
      const res = await DataManageService.getUrlForIFrame(params);
      let imgUrl = `${res.iframeUrl}&t=${Date.now()}`;
      this.setState({ imgUrl });
    } else if (event.data.eventType === 'publish') {
      const { list } = this.state;
      list.map((i) => {
        return i.id === 1 ? (i.flage = true) : (i.flage = false);
      });
      this.setState(
        {
          list,
          source: 'CREATION'
        },
        async () => {
          const { source, type } = this.state;
          let params = {};
          params = {
            authorization: this.state.Authorization,
            type,
            origin: window.location.origin,
            source
          };
          const res = await DataManageService.getUrlForIFrame(params);
          let imgUrl = `${res.iframeUrl}&t=${Date.now()}`;
          this.setState({ imgUrl });
        }
      );
    }
  };

  iframeClick() {
    let h = document.getElementById('iframe-body').scrollHeight;
    document.getElementById('iframe-b').style.height = `${h}px`;
  }

  click = (item) => {
    const { list } = this.state;
    list.map((i) => {
      return i.id === item.id ? (i.flage = true) : (i.flage = false);
    });
    this.setState(
      {
        list,
        source: item.detailziduan
      },
      () => {
        this.list();
      }
    );
  };

  handleChange = (value) => {
    const { list } = this.state;
    let newItme = list.filter((item) => item.flage === true);
    this.setState(
      {
        type: value
      },
      () => this.list()
    );
  };

  render() {
    const { list, type } = this.state;
    return (
      <div className="out">
        <div className="titleOut">
          {localStorage.getItem('env') === 'SW' ? (
            <Select value={type} className="selset" style={{ width: 240 }} onChange={this.handleChange}>
              <Option value="h5">h5</Option>
            </Select>
          ) : (
            <Select value={type} className="selset" style={{ width: 240 }} onChange={this.handleChange}>
              <Option value="hd">互动</Option>
              <Option value="h5">h5</Option>
              <Option value="design">海报</Option>
              <Option value="lc">长页</Option>
              <Option value="form">表单</Option>
            </Select>
          )}
          <div className="changetab">
            {list.map((item) => {
              return (
                <div
                  style={{
                    color: item.flage == true ? 'var(--ant-primary-color)' : '',
                    borderBottom: item.flage == true ? '2px solid $var(--ant-primary-color)' : '',
                    paddingBottom: '5px'
                  }}
                  onClick={() => this.click(item)}
                  key={item.id}
                >
                  {item.name}
                </div>
              );
            })}
          </div>
        </div>

        <div id="iframe-body">
          <iframe id="iframe-b" frameBorder="0" scrolling="no" width="100%" src={this.state.imgUrl} />
        </div>
      </div>
    );
  }
}
