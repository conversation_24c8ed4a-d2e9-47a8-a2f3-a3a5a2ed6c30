export default {
  cn: {
    'operationCenter-jA5V2MPLr4wV': '数据表',
    'operationCenter-ZeXD9AStm1in': '表字段',
    'operationCenter-msqdgVndHJ3b': '字段值',
    'operationCenter-HekpOyIJdB7F': '无限制',
    'operationCenter-crUeA5tB1lDq': '你确定离开此页面吗?',
    'operationCenter-CvsS6aGOlfBt': '保存成功',
    'operationCenter-hA35HiFvZK6W': '调用流程开始测试',
    'operationCenter-BhTCRWTv3NT6': '画布版本: V{{version}}',
    'operationCenter-tZgvde0i2lmn': '数据依赖',
    'operationCenter-j0m8zBo83cCS': '数据权限',
    'operationCenter-uf9udRuQQ9oW': '字段权限',
    'operationCenter-bpBAl1ABrPij': '脱敏字段',

    // nodeEditor/com/eventFilter.jsx
    'operationCenter-9iE5KqtAk6Pi': '属性名称',
    'operationCenter-ew29NZLd9jgN': '事件属性显示名',
    'operationCenter-3awsyk2Dsq02': '数据类型',
    'operationCenter-HZEPbclh5Eea': '日期',
    'operationCenter-ElVNhlRis2im': '发生次数',
    'operationCenter-7eKgQK9owNv3': '创建于',
    'operationCenter-VySLhyVALm5n': '自定义事件',
    'operationCenter-Zkv5lxHEPwq2': '埋点事件',
    'operationCenter-w9m3EJTOmmDl': '事件统计（近7天事件发生次数）',
    'operationCenter-kKjERCxJZGBr': '请选择事件',
    'operationCenter-h6rlrR66aYiC': '事件详情',
    'operationCenter-9NQOgyMRNRZr': '至少做过',
    'operationCenter-waSwZUVTBwgI': '次',
    'operationCenter-xHLHqnVmVEct': '应用变量',
    'operationCenter-y96lTAN9rX': '事件类型：',

    // nodeEditor/com/eventFilterV2.jsx
    'operationCenter-s8jfKpLaNFPr': '一',
    'operationCenter-psHggmZrrNy8': '二',
    'operationCenter-szNIje6geXC9': '三',
    'operationCenter-SPoF9w5w9KQ8': '四',
    'operationCenter-7sCWG5Plx6NF': '五',
    'operationCenter-t7y7DB8eVLn5': '事件',
    'operationCenter-Dgc1n9he9N': '事件类型：',
    'operationCenter-D5rUkt5YcQIH': '+ 添加事件',

    // nodeEditor/com/eventFilterV3.jsx
    'operationCenter-u8bUIpZ6zFva': '支持最多3个事件',
    'operationCenter-7s05fomJYRGC': '会话数据过滤',
    'operationCenter-t7hIaFn4l1MU': '+ 添加会话数据',
    'operationCenter-d4B7J23fXY06': '添加做过事件',
    'operationCenter-aFNx30uDw2i7': '添加未做过事件',

    // nodeEditor/com/multiEventFilter.jsx
    'operationCenter-JOnWSVFMMn2x': '分支一：',
    'operationCenter-ZoTRpXXYquL5': '分支二：',
    'operationCenter-8nVMMtcztcVB': '分支三：',
    'operationCenter-KUNVzTOdVzb7': '分支四：',
    'operationCenter-9S2glWLTaOob': '分支五：',
    'operationCenter-AAjGK3vR8RJw': '分支名称，不超过20字',
    'operationCenter-Lb4sisg3cg4l': '做过该事件',
    'operationCenter-Q0V8ivGDu11V': '渠道联动配置',
    'operationCenter-9Hkaz0kciVxn': '关联消息id',
    'operationCenter-v7dIg7fYgy9r': '无法添加更多分支，请先删除一个分支',
    'operationCenter-G1vPzWO1x6gr': '+ 添加事件分支',
    'operationCenter-ZNFfPoFXfn6S': '默认分支：未完成以上任意分支的剩余用户',
    'operationCenter-gkFnJwY1Uy': 'MA生成消息ID (key为messageld)，触达用户时推送下游。使用该功能时，下游需要将messageld在回执数据中回传',

    // nodeEditor/com/multiEventFilterV2.jsx
    'operationCenter-fJvJCIqqu6vD': '包含以下做过分支',
    'operationCenter-AQ6oFi3Cnxuc': '默认分支',
    'operationCenter-6TThINKzDGDU': '未完成以上任意分支的剩余用户',
    'operationCenter-BCqWj6m0BG': '分支',

    // nodeEditor/com/saveSegment.jsx
    'operationCenter-Ry6kjJ98swRS': '天',
    'operationCenter-ZzSEKXpmiFIZ': '小时',
    'operationCenter-vVA4BwBz8hO5': '分钟',

    // nodeEditor/com/segmentInfo.jsx
    'operationCenter-wF9bpkycPdeF': '未开始',
    'operationCenter-46czFZJFGS51': '计算中',
    'operationCenter-4DnRAmzIAoAt': '计算失败',
    'operationCenter-PZgqQ1mli93o': '计算成功',
    'operationCenter-eFnTWFGeHYNL': '等于',
    'operationCenter-hCgrcfXxFwHh': '大于',
    'operationCenter-1H3PxvhPq1gY': '大于等于',
    'operationCenter-PomFcFnhskGs': '小于',
    'operationCenter-U0Mykncchl5U': '小于等于',
    'operationCenter-3MyKqrPu6YVI': '分群',
    'operationCenter-1hPN9AZ5EwUQ': '分群规则',
    'operationCenter-tYbU3cQq2Kva': '属于以下分群',
    'operationCenter-LGqNdkCuUD08': '排除以下分群',
    'operationCenter-2tZSJJqnbkeH': '上传或流程分群无上传规则！',
    'operationCenter-r4aDWIrwtRcQ': '总点击次数：',
    'operationCenter-8BbFAsrW1u5s': '单次更新',
    'operationCenter-fn9pqPzoQsqf': '定期更新',
    'operationCenter-WnTOeBeF5RiE': '每日',
    'operationCenter-pCJYTqmvdXXG': '每周',
    'operationCenter-lU095A4iJ5u9': '每月',
    'operationCenter-zAie4eaIs45B': '计算状态',
    'operationCenter-qWdN4at1S9DF': '更新规则',
    'operationCenter-lj3vBXhqWZ2x': '计算规则',
    'operationCenter-r0bJWzwo3XZY': '人',
    'operationCenter-cirSFP5k66Yt': '请选择分群,支持ID、名称搜索',
    'operationCenter-dTC6IGRdwex1': '无可用批次',
    'operationCenter-2LCV0hm439lu': '分群计算结果校验',
    'operationCenter-zQTBYVbZPDYQ': '内分群最新可用的批次结果数据',
    'operationCenter-NarxeKYwHosL': '+添加分群',
    'operationCenter-hJVmj1uxzH': '流程画布批次运行时使用分群最新计算批次（判断分群最新计算批次在有效时间内且计算成功）',

    // nodeEditor/com/segmentInfoList.jsx
    'operationCenter-1d4Il4UZANQe': '分群列表',
    'operationCenter-Yhbz3YxGDEZj': '分群名称',
    'operationCenter-my2b4bQRChNm': '分群ID',

    // nodeEditor/com/segmentSplit.jsx
    'operationCenter-xiHRm5BYrZYL': '草稿',
    'operationCenter-lmsulLzFriG9': '无效',
    'operationCenter-DeZDQF0vk3ep': '正常',
    'operationCenter-xMI609ygMR77': '已停止',
    'operationCenter-xPNIDjleWAKc': '属于以下分群',
    'operationCenter-cZuiwcNm0HN5': '排除以下分群',
    'operationCenter-VoA5Ox4HxtZE': '上传或流程分群无上传规则！',
    'operationCenter-ymNluzzmfapq': '总点击次数：',
    'operationCenter-U1sRH59VvpG8': '单次更新',
    'operationCenter-cu6fRhzvZbfT': '定期更新',
    'operationCenter-TJxQyGzfFsuD': '每日',
    'operationCenter-MUeT27hFwEEj': '每周',
    'operationCenter-OsHd47wbk9X3': '每月',
    'operationCenter-pswIGgarywk8': '人',
    'operationCenter-njvJLFWlmIH8': '分群规则',
    'operationCenter-sjlezjHjbnlL': '画布使用在',
    'operationCenter-TWIbV25UCkCs': '小时',
    'operationCenter-NRsNg9OHZ0Og': '天',
    'operationCenter-Sk2szkIGZ6q4': '内分群最新可用的批次结果数据',
    'operationCenter-Lx5RzAIrN581': '分群计算结果校验',
    'operationCenter-YWh3RLwgXLmy': '请选择分群',
    'operationCenter-28rk0AnmdwhM': '无可用批次',
    'operationCenter-vONKx91e4Chu': '无法添加更多分支，请先删除一个分支',
    'operationCenter-IH2Rak6xBfrV': '+ 添加分群分支',
    'operationCenter-y6EjpOsoMe2R': '默认分支：未完成以上任意分支的剩余用户',
    'operationCenter-MVhffSeT599Z': '分支一',
    'operationCenter-KiD9sUpE8YhH': '分支二',
    'operationCenter-Gh4xJ2KGkOHw': '分支三',
    'operationCenter-WujaRMqoDiQy': '分支四',
    'operationCenter-Nl2imvod5vXq': '分支五',
    'operationCenter-MqoATGgAlAZc': '分支六',
    'operationCenter-atPFuGo0ijBR': '分支七',
    'operationCenter-ILI7xnhEPZsc': '分支八',
    'operationCenter-kbmkN4k5mIC4': '分支九',
    'operationCenter-xiNOoiIPccVN': '分支十',
    'operationCenter-QSMhcfnrQBp2': '+ 添加分群分支',
    'operationCenter-06uqwmFL4kAB': '默认分支：未完成以上任意分支的剩余用户',

    // nodeEditor/com/selectTime.jsx
    'operationCenter-CFNEgCB3sHqn': '请选择时间单位',

    // nodeEditor/com/selectTimeV3.jsx
    'operationCenter-yYqphnCSzwa4': '营销日历',
    'operationCenter-NR99C3S2cQKK': '排除该日历的',
    'operationCenter-VxlRR6JqAmK5': '请选择',
    'operationCenter-GFJZse1HtJVe': '之外等待以下时长',
    'operationCenter-JVxd0yJrOpE7': '等待时长:'
  },
  en: {
    'operationCenter-jA5V2MPLr4wV': 'Data Table',
    'operationCenter-ZeXD9AStm1in': 'Table Field',
    'operationCenter-msqdgVndHJ3b': 'Field Value',
    'operationCenter-HekpOyIJdB7F': 'No Limit',
    'operationCenter-crUeA5tB1lDq': 'Are you sure you want to leave this page?',
    'operationCenter-CvsS6aGOlfBt': 'Save successful',
    'operationCenter-hA35HiFvZK6W': 'Process test started',
    'operationCenter-BhTCRWTv3NT6': 'Canvas Version: V{{version}}',
    'operationCenter-tZgvde0i2lmn': 'Data Dependency',
    'operationCenter-j0m8zBo83cCS': 'Data Permission',
    'operationCenter-uf9udRuQQ9oW': 'Field Permission',
    'operationCenter-bpBAl1ABrPij': 'Sensitive Fields',

    // nodeEditor/com/eventFilter.jsx
    'operationCenter-9iE5KqtAk6Pi': 'Property Name',
    'operationCenter-ew29NZLd9jgN': 'Event Property Display Name',
    'operationCenter-3awsyk2Dsq02': 'Data Type',
    'operationCenter-HZEPbclh5Eea': 'Date',
    'operationCenter-ElVNhlRis2im': 'Occurrence Count',
    'operationCenter-7eKgQK9owNv3': 'Created on',
    'operationCenter-VySLhyVALm5n': 'Custom Event',
    'operationCenter-Zkv5lxHEPwq2': 'Tracking Event',
    'operationCenter-w9m3EJTOmmDl': 'Event Statistics (Event Occurrences in Last 7 Days)',
    'operationCenter-kKjERCxJZGBr': 'Please select event',
    'operationCenter-h6rlrR66aYiC': 'Event Details',
    'operationCenter-9NQOgyMRNRZr': 'At least done',
    'operationCenter-waSwZUVTBwgI': 'times',
    'operationCenter-xHLHqnVmVEct': 'Application Variables',
    'operationCenter-y96lTAN9rX': 'Event Type:',

    // nodeEditor/com/eventFilterV2.jsx
    'operationCenter-s8jfKpLaNFPr': 'One',
    'operationCenter-psHggmZrrNy8': 'Two',
    'operationCenter-szNIje6geXC9': 'Three',
    'operationCenter-SPoF9w5w9KQ8': 'Four',
    'operationCenter-7sCWG5Plx6NF': 'Five',
    'operationCenter-t7y7DB8eVLn5': 'Event',
    'operationCenter-Dgc1n9he9N': 'Event Type:',
    'operationCenter-D5rUkt5YcQIH': '+ Add Event',

    // nodeEditor/com/eventFilterV3.jsx
    'operationCenter-u8bUIpZ6zFva': 'Support up to 3 events',
    'operationCenter-7s05fomJYRGC': 'Session Data Filter',
    'operationCenter-t7hIaFn4l1MU': '+ Add Session Data',
    'operationCenter-d4B7J23fXY06': 'Add Done Event',
    'operationCenter-aFNx30uDw2i7': 'Add Undone Event',

    // nodeEditor/com/multiEventFilter.jsx
    'operationCenter-JOnWSVFMMn2x': 'Branch One:',
    'operationCenter-ZoTRpXXYquL5': 'Branch Two:',
    'operationCenter-8nVMMtcztcVB': 'Branch Three:',
    'operationCenter-KUNVzTOdVzb7': 'Branch Four:',
    'operationCenter-9S2glWLTaOob': 'Branch Five:',
    'operationCenter-AAjGK3vR8RJw': 'Branch name, no more than 20 characters',
    'operationCenter-Lb4sisg3cg4l': 'Done this event',
    'operationCenter-Q0V8ivGDu11V': 'Channel Linkage Configuration',
    'operationCenter-9Hkaz0kciVxn': 'Associate message id',
    'operationCenter-v7dIg7fYgy9r': 'Cannot add more branches, please delete one first',
    'operationCenter-G1vPzWO1x6gr': '+ Add Event Branch',
    'operationCenter-ZNFfPoFXfn6S': 'Default branch: remaining users who did not complete any of the above branches',
    'operationCenter-gkFnJwY1Uy': 'MA generates message ID (key is messageld), and pushes it to users when it is triggered. When using this function, the downstream needs to return messageld in the receipt data',
    // nodeEditor/com/multiEventFilterV2.jsx
    'operationCenter-fJvJCIqqu6vD': 'Contains the following done branches',
    'operationCenter-AQ6oFi3Cnxuc': 'Default Branch',
    'operationCenter-6TThINKzDGDU': 'Remaining users who did not complete any of the above branches',

    // nodeEditor/com/saveSegment.jsx
    'operationCenter-Ry6kjJ98swRS': 'Days',
    'operationCenter-ZzSEKXpmiFIZ': 'Hours',
    'operationCenter-vVA4BwBz8hO5': 'Minutes',
    'operationCenter-BCqWj6m0BG': 'Branch',

    // nodeEditor/com/segmentInfo.jsx
    'operationCenter-wF9bpkycPdeF': 'Not Started',
    'operationCenter-46czFZJFGS51': 'Calculating',
    'operationCenter-4DnRAmzIAoAt': 'Calculation Failed',
    'operationCenter-PZgqQ1mli93o': 'Calculation Successful',
    'operationCenter-eFnTWFGeHYNL': 'Equal',
    'operationCenter-hCgrcfXxFwHh': 'Greater Than',
    'operationCenter-1H3PxvhPq1gY': 'Greater Than or Equal',
    'operationCenter-PomFcFnhskGs': 'Less Than',
    'operationCenter-U0Mykncchl5U': 'Less Than or Equal',
    'operationCenter-3MyKqrPu6YVI': 'Segment',
    'operationCenter-1hPN9AZ5EwUQ': 'Segment Rules',
    'operationCenter-tYbU3cQq2Kva': 'Belongs to the following segments',
    'operationCenter-LGqNdkCuUD08': 'Exclude the following segments',
    'operationCenter-2tZSJJqnbkeH': 'Upload or process segments have no upload rules!',
    'operationCenter-r4aDWIrwtRcQ': 'Total clicks:',
    'operationCenter-8BbFAsrW1u5s': 'One-time Update',
    'operationCenter-fn9pqPzoQsqf': 'Regular Update',
    'operationCenter-WnTOeBeF5RiE': 'Daily',
    'operationCenter-pCJYTqmvdXXG': 'Weekly',
    'operationCenter-lU095A4iJ5u9': 'Monthly',
    'operationCenter-zAie4eaIs45B': 'Calculation Status',
    'operationCenter-qWdN4at1S9DF': 'Update Rule',
    'operationCenter-lj3vBXhqWZ2x': 'Calculation Rule',
    'operationCenter-r0bJWzwo3XZY': 'people',
    'operationCenter-cirSFP5k66Yt': 'Please select segment, supports ID and name search',
    'operationCenter-dTC6IGRdwex1': 'No available batch',
    'operationCenter-2LCV0hm439lu': 'Segment calculation result verification',
    'operationCenter-zQTBYVbZPDYQ': 'Latest available batch result data within the segment',
    'operationCenter-NarxeKYwHosL': '+Add Segment',
    'operationCenter-hJVmj1uxzH': 'Use the latest calculated batch of the segment when the process canvas batch runs (determine that the latest calculated batch of the segment is within the valid time and the calculation is successful)',
    // nodeEditor/com/segmentInfoList.jsx
    'operationCenter-1d4Il4UZANQe': 'Segment List',
    'operationCenter-Yhbz3YxGDEZj': 'Segment Name',
    'operationCenter-my2b4bQRChNm': 'Segment ID',

    // nodeEditor/com/segmentSplit.jsx
    'operationCenter-xiHRm5BYrZYL': 'Draft',
    'operationCenter-lmsulLzFriG9': 'Invalid',
    'operationCenter-DeZDQF0vk3ep': 'Normal',
    'operationCenter-xMI609ygMR77': 'Stopped',
    'operationCenter-xPNIDjleWAKc': 'Belongs to the following segments',
    'operationCenter-cZuiwcNm0HN5': 'Exclude the following segments',
    'operationCenter-VoA5Ox4HxtZE': 'Upload or process segments have no upload rules!',
    'operationCenter-ymNluzzmfapq': 'Total clicks:',
    'operationCenter-U1sRH59VvpG8': 'One-time update',
    'operationCenter-cu6fRhzvZbfT': 'Regular update',
    'operationCenter-TJxQyGzfFsuD': 'Daily',
    'operationCenter-MUeT27hFwEEj': 'Weekly',
    'operationCenter-OsHd47wbk9X3': 'Monthly',
    'operationCenter-pswIGgarywk8': 'people',
    'operationCenter-njvJLFWlmIH8': 'Segment rules',
    'operationCenter-sjlezjHjbnlL': 'Canvas uses within',
    'operationCenter-TWIbV25UCkCs': 'hours',
    'operationCenter-NRsNg9OHZ0Og': 'days',
    'operationCenter-Sk2szkIGZ6q4': 'the latest available batch result data of the segment',
    'operationCenter-Lx5RzAIrN581': 'Segment calculation result verification',
    'operationCenter-YWh3RLwgXLmy': 'Please select segment',
    'operationCenter-28rk0AnmdwhM': 'No available batch',
    'operationCenter-vONKx91e4Chu': 'Cannot add more branches, please delete one branch first',
    'operationCenter-IH2Rak6xBfrV': '+ Add segment branch',
    'operationCenter-y6EjpOsoMe2R': 'Default branch: remaining users who have not completed any of the above branches',
    'operationCenter-MVhffSeT599Z': 'Branch One',
    'operationCenter-KiD9sUpE8YhH': 'Branch Two',
    'operationCenter-Gh4xJ2KGkOHw': 'Branch Three',
    'operationCenter-WujaRMqoDiQy': 'Branch Four',
    'operationCenter-Nl2imvod5vXq': 'Branch Five',
    'operationCenter-MqoATGgAlAZc': 'Branch Six',
    'operationCenter-atPFuGo0ijBR': 'Branch Seven',
    'operationCenter-ILI7xnhEPZsc': 'Branch Eight',
    'operationCenter-kbmkN4k5mIC4': 'Branch Nine',
    'operationCenter-xiNOoiIPccVN': 'Branch Ten',
    'operationCenter-QSMhcfnrQBp2': '+ Add Segment Branch',
    'operationCenter-06uqwmFL4kAB': 'Default branch: remaining users who did not complete any of the above branches',

    // nodeEditor/com/selectTime.jsx
    'operationCenter-CFNEgCB3sHqn': 'Please select time unit',

    // nodeEditor/com/selectTimeV3.jsx
    'operationCenter-yYqphnCSzwa4': 'Marketing Calendar',
    'operationCenter-NR99C3S2cQKK': 'Exclude this calendar',
    'operationCenter-VxlRR6JqAmK5': 'Please select',
    'operationCenter-GFJZse1HtJVe': 'Wait for the following duration outside',
    'operationCenter-JVxd0yJrOpE7': 'Wait Duration:'
  }
};
