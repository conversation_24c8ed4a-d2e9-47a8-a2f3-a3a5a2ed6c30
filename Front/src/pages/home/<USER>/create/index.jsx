/* eslint-disable react-hooks/exhaustive-deps */
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Drawer, Input, message, Select } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import BusinessEntity from 'service/businessEntity';
import BusinessVariable from 'service/businessVariable';
import { t } from 'utils/translation';

const entityArrList = [
  'entity_user',
  'entity_employee',
  'entity_item',
  'entity_equity',
  'entity_content',
  'entity_device',
  'entity_department'
];

const CreateBusinessVariable = (props) => {
  const {
    visible,
    action,
    value,
    form: { getFieldDecorator, validateFields }
  } = props;
  const [loading, setLoading] = useState(false);
  const [entityList, setEntityList] = useState([]);

  useEffect(() => {
    (async () => {
      const _entityList = await BusinessEntity.listBy([]);
      setEntityList(_entityList);
    })();
  }, []);

  const okHandle = () => {
    validateFields(async (err, fields) => {
      if (err) return;
      try {
        setLoading(true);
        const data = { ...value, ...fields };
        if (!entityArrList.includes(data.entityCode)) {
          data.entityCode = data.entityCode.match(/\[(.+?)\]/)[1];
        }
        await BusinessVariable.save(data);
        message.success(t('operationCenter-1TtOxU5t3wEo'));
        setLoading(false);
        action({ visible: false, value: {} }, true);
      } catch (err) {
        console.error(err.message);
        setLoading(false);
      }
    });
  };

  const handleValidator = async (rule, v, callback) => {
    if (!v) return callback();
    let bool;
    try {
      if (value?.id) {
        bool = await BusinessVariable.ensureUnique({
          [rule.field]: v.trim(),
          id: value.id
        });
      } else {
        bool = await BusinessVariable.ensureUnique({
          [rule.field]: v.trim()
        });
      }
      if (bool) return callback();
      callback(v);
    } catch (error) {
      callback();
    }
  };

  return (
    <Drawer
      title={value.id ? t('operationCenter-IN5JGkKxoM3C') : t('operationCenter-e9Vs0qsJ1PPe')}
      open={visible}
      // onOk={okHandle}
      // maskClosable={false}
      className="scenarioDrawer"
      onClose={() => action({ visible: false })}
      confirmLoading={loading}
      width="600"
    >
      <Form layout="vertical">
        <Form.Item label={t('operationCenter-hqp5vKPMWO4o')}>
          {getFieldDecorator('entityCode', {
            // getValueFromEvent: (event) => event.target.value.trim(),
            rules: [{ required: true, message: t('operationCenter-Ah5YsDhnUy6u') }],
            initialValue: !_.isEmpty(value) ? `${value.entityName}[${value.entityCode}]` : undefined
          })(
            <Select placeholder={t('operationCenter-Ah5YsDhnUy6u')} optionFilterProp="children" allowClear showSearch disabled={value?.id}>
              {entityList.map((n) => (
                <Select.Option key={n.entityCode} value={n.entityCode}>
                  {n.entityName}[{n.entityCode}]
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
        <Form.Item label={t('operationCenter-zONBL96vunbQ')}>
          {getFieldDecorator('variableName', {
            getValueFromEvent: (event) => event.target.value.trim(),
            rules: [
              { required: true, message: t('operationCenter-WdgZ9YWy1TCc') },
              { pattern: /^[^\s]*$/, message: t('operationCenter-MhYEuiRSh9hu') },
              { max: 64, message: t('operationCenter-eWtjsaOMf1mU') },
              {
                validator: handleValidator,
                message: t('operationCenter-mverfJDzgHg2')
              }
            ],
            initialValue: value.variableName
          })(<Input placeholder={t('operationCenter-WdgZ9YWy1TCc')} autocomplete="off" />)}
        </Form.Item>
        <Form.Item label={t('operationCenter-IfcOYHG4SjG9')}>
          {getFieldDecorator('variableDescribe', {
            rules: [
              { required: true, message: t('operationCenter-WdgZ9YWy1TCc') },
              { pattern: /^[^\s]*$/, message: t('operationCenter-MhYEuiRSh9hu') },
              { max: 64, message: t('operationCenter-eWtjsaOMf1mU') },
              {
                validator: handleValidator,
                message: t('operationCenter-oVLGeIZbFI3I')
              }
            ],
            getValueFromEvent: (event) => event.target.value.trim(),
            initialValue: value.variableDescribe
          })(<Input placeholder={t('operationCenter-WdgZ9YWy1TCc')} autocomplete="off" />)}
        </Form.Item>
      </Form>

      <div style={{ position: 'fixed', bottom: 12, right: 20 }}>
        <Button onClick={() => action({ visible: false })} style={{ marginRight: 8 }}>
          {t('operationCenter-YRZhRLVgGH2G')}
        </Button>
        <Button type="primary" onClick={okHandle} loading={loading}>
          {t('operationCenter-qGCllP6qXHl8')}
        </Button>
      </div>
    </Drawer>
  );
};

export default Form.create()(CreateBusinessVariable);
