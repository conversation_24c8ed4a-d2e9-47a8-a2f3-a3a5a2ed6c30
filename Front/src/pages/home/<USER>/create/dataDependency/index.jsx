import { useStore } from '@/store/canpaignV2';
import { Badge, InputNumber, Spin, Switch, Table } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import CampaignV2Service from 'service/CampaignV2Service';
import { t } from 'utils/translation';

const campaignV2Service = new CampaignV2Service();

const transformData = (data) => {
  const groupedData = {};
  data.forEach((item) => {
    if (!groupedData[item.type]) {
      groupedData[item.type] = {};
    }
    if (!groupedData[item.type][item.nodeId]) {
      groupedData[item.type][item.nodeId] = [];
    }
    groupedData[item.type][item.nodeId].push(item);
  });

  return groupedData;
};

export default function DataDependency({ updateState, needCheck, waitTimes, value, mode = 'edit', flowId }) {
  const [loading, setLoading] = useState(false);
  const [arr, setArr] = useState({});
  const { _flows, campaignInfo } = useStore();
  const [initChecked, setInitChecked] = useState(false);
  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        if (mode === 'detail') {
          initDetail();
        } else {
          initEdit();
        }
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  const initEdit = async () => {
    const { scenario, id } = value;
    const params = {
      scenarioCode: scenario?.code,
      deptId: window.getDeptId(),
      flows: _flows || [],
      id
    };
    const data = await campaignV2Service.listRelySource(params);
    const flag = data.some((item) => item.needCheck);
    if (!flag) {
      updateState && updateState({ needCheck: false, waitTimes: 1 });
      setInitChecked(true);
    } else {
      updateState &&
        updateState({
          needCheck,
          waitTimes: waitTimes || 1
        });
      setInitChecked(false);
    }
    setArr(transformData(data));
  };

  const initDetail = async () => {
    const params = [
      {
        operator: 'EQ',
        propertyName: 'projectId',
        value: localStorage.getItem('projectId')
      },
      {
        operator: 'EQ',
        propertyName: 'flowId',
        value: flowId
      }
    ];
    // TODO 这里是判断从全局还是走的批次 如果是批次就是value.id 如果是全局就是dataList[0]?.id
    const batchId = value.id ? value.id : 0;

    params.push({
      operator: 'EQ',
      propertyName: 'batchId',
      value: batchId
    });
    const data = await campaignV2Service.detailListRelySource(params);
    setArr(transformData(data));
  };
  /**
   *
   * @param {'SEGMENT' | 'USERTAG'} type
   * @returns
   */
  const columnsField = (type) => [
    {
      dataIndex: 'userGroup',
      key: 'userGroup',
      title: type === 'SEGMENT' ? t('operationCenter-sO0MvBUjsiXa') : t('operationCenter-FBUJ2ziCSasp'),
      width: 432,
      render: (_, record) => {
        const { busiId, name, alias, lastCalcTime } = record;
        return (
          <div>{`[${busiId}] ${name} (${alias}${type === 'SEGMENT' ? t('operationCenter-CsAYzVEM7eoz') : ''}) ${
            lastCalcTime ? dayjs(lastCalcTime).format('YYYY-MM-DD HH:mm:ss') : ''
          }`}</div>
        );
      }
    },
    {
      dataIndex: 'needCheck',
      key: 'needCheck',
      title: t('operationCenter-RU5tPeprCs56'),
      width: 120,
      render: (tag) => {
        return (
          <div>
            <Badge
              status={tag ? 'success' : 'error'}
              text={tag ? t('operationCenter-FLUfLOZWUgkK') : t('operationCenter-r0k7iL7ezZXO')}
            />
          </div>
        );
      }
    }
  ];

  const isShowNotes = !(campaignInfo.phase === 'DRAFT' || campaignInfo.phase === 'TESTING');
  const detailColumnsField = (type) => [
    {
      dataIndex: 'userGroup',
      key: 'userGroup',
      title: type === 'SEGMENT' ? t('operationCenter-sO0MvBUjsiXa') : t('operationCenter-FBUJ2ziCSasp'),
      width: 260,
      render: (_, record) => {
        const { busiId, name, alias } = record;
        return (
          <div>{`[${busiId}] ${name} (${alias}${type === 'SEGMENT' ? t('operationCenter-CsAYzVEM7eoz') : ''})`}</div>
        );
      }
    },
    {
      dataIndex: 'needCheck',
      key: 'needCheck',
      title: t('operationCenter-RU5tPeprCs56'),
      width: 120,
      render: (tag) => {
        return isShowNotes ? (
          <div>
            <Badge
              status={tag ? 'success' : 'error'}
              text={tag ? t('operationCenter-FLUfLOZWUgkK') : t('operationCenter-r0k7iL7ezZXO')}
            />
          </div>
        ) : (
          '-'
        );
      }
    },
    {
      dataIndex: 'checkPass',
      key: 'checkPass',
      title: t('operationCenter-KbX2zp5Iqk'),
      width: 120,
      render: (tag) => {
        return isShowNotes ? (
          <div>
            <Badge
              status={tag ? 'success' : 'error'}
              text={tag ? t('operationCenter-FLUfLOZWUgkK') : t('operationCenter-r0k7iL7ezZXO')}
            />
          </div>
        ) : (
          '-'
        );
      }
    },
    {
      dataIndex: 'notes',
      key: 'notes',
      title: t('operationCenter-WM91rNhXUy'),
      width: 120,
      render: (notes) => (isShowNotes ? (!_.isEmpty(notes) ? notes : '') : '-')
    },
    {
      dataIndex: 'lastCalcTime',
      key: 'lastCalcTime',
      title: t('operationCenter-VYbFygjzdL2O'),
      width: 186,
      render: (_, record) => {
        const { checkPass, lastCalcTime } = record;
        return isShowNotes ? (checkPass && lastCalcTime ? dayjs(lastCalcTime).format('YYYY-MM-DD HH:mm:ss') : '') : '-';
      }
    }
  ];

  return (
    <>
      <div className="flex">
        <div>
          <div className="w-508 mt-4">
            {t('operationCenter-PUnFrFka10VU')}
            <br />
            {t('operationCenter-rfMWQyPi0DNT')}
          </div>
        </div>
        <div className="flex items-center">
          <Switch
            size="small"
            checked={needCheck}
            onChange={(checked) => {
              updateState({ needCheck: checked });
            }}
            disabled={mode === 'detail' || initChecked}
          />
        </div>
      </div>
      <div hidden={!needCheck} className="flex gap-8 leading-[32px] mt-16">
        {t('operationCenter-vPl4ORN8PJv9')}
        <InputNumber
          min={1}
          max={24}
          value={waitTimes}
          onChange={(e) => {
            updateState({ waitTimes: e });
          }}
          style={{ width: 90, borderRadius: 4 }}
          precision={0}
          disabled={mode === 'detail'}
        />
        {t('operationCenter-xAhE6S4KENMU')}
      </div>
      <Spin spinning={loading}>
        <>
          {!_.isEmpty(arr?.SEGMENT) && (
            <>
              <div className="font-bold mt-24 text-16">{t('operationCenter-8YOc3xU4yF3U')}</div>
              {_.map(arr.SEGMENT, (item, index) => {
                return (
                  <div key={index}>
                    <div className="h-48 leading-[48px] font-bold">
                      {t('operationCenter-vk5VMumrsIVB')}
                      {index}
                    </div>
                    <Table
                      dataSource={item}
                      columns={mode === 'detail' ? detailColumnsField('SEGMENT') : columnsField('SEGMENT')}
                      style={{ width: '100%' }}
                      pagination={false}
                      size="middle"
                    />
                  </div>
                );
              })}
            </>
          )}

          {!_.isEmpty(arr?.USERTAG) && (
            <>
              <div className="font-bold mt-24 text-16">{t('operationCenter-wmVQ881QDXmv')}</div>
              {_.map(arr.USERTAG, (item, index) => {
                return (
                  <div key={index}>
                    <div className="h-48 leading-[48px] font-bold">
                      {t('operationCenter-vk5VMumrsIVB')}
                      {index}
                    </div>
                    <Table
                      dataSource={item}
                      columns={mode === 'detail' ? detailColumnsField('USERTAG') : columnsField('USERTAG')}
                      style={{ width: '100%' }}
                      pagination={false}
                      size="middle"
                    />
                  </div>
                );
              })}
            </>
          )}
        </>
      </Spin>
    </>
  );
}
