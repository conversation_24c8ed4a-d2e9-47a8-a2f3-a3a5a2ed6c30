export default {
  cn: {
    // nodeEditor/nodeEditor.jsx
    'operationCenter-536WeAfm04JB': '等待时间',
    'operationCenter-2GARVAb7FCHP': '等待时长最高为60天',
    'operationCenter-33tVgqagnzOi': '等待时间V2',
    'operationCenter-Axllm1XbTTUf': '等待时长最高为60天，至少任选一个时间单位作为等待时长',
    'operationCenter-opyLV1jafrrH': '等待时间V3',
    'operationCenter-3PFSzmUCgBgu': '会话数据分支',
    'operationCenter-JXPbO5piXqmh': '配置流程引擎会话分支',
    'operationCenter-WsjUiedYjlBV': '用户分群',
    'operationCenter-YfkxeO4Brga1': '可选择多个分群发送活动，合并后系统会对用户进行去重。最多选择5个分群。',
    'operationCenter-ulJlxGC6F7Pm': '触发事件分支',
    'operationCenter-QPMaQaxZZhoC': '最多可添加5个触发事件。完成任意事件即满足触发。',
    'operationCenter-FKkYXqJTV4Hx': '触发事件分支V2',
    'operationCenter-7WyQnKlBlX7C': '触发事件分支V3',
    'operationCenter-yGgYwqvcW0vg': '人群条件分支',
    'operationCenter-98Xy0eXRCSdc': '请按条件配置不同的人群分支，最多5分支。优先满足前一个分支的用户，将不参与后面分支的判断。',
    'operationCenter-EHtK3zRIvLNe': '开始测试',
    'operationCenter-kLm7P8HczaBc': '流程规则在开始测试中的调整',
    'operationCenter-NiFzUZKSDqLQ': 'A/B 测试',
    'operationCenter-gFxqaiKSWtiX': '可将人群随机拆分成2～5个分组，请保证百分比相加等于100',
    'operationCenter-3N6C8iq2pdj7': 'A分组',
    'operationCenter-RscUOG1q43KJ': 'B分组',
    'operationCenter-qLdM61m5iMhM': '起始事件',
    'operationCenter-hTL6e2KAe1IO': '至多设置5个起始事件，用户满足以下任意事件即触发该活动。',
    'operationCenter-knm8W3EI7PFU': '嵌入型组件',
    'operationCenter-N59TSeume63a': '嵌入组件',
    'operationCenter-Qis8QPp5PPUO': '配置指定时刻',
    'operationCenter-At6zVsMhbqTR': '多事件分支',
    'operationCenter-zJKkq2czbT0J': '请配置不同的事件分支，最多5分支。',
    'operationCenter-BLpR0PJxvwx8': '多事件分支V2',
    'operationCenter-hXm5rnNwpe2S': '配置标记分支',
    'operationCenter-EY6moC1nyb3C': '节点标签',
    'operationCenter-5NybqKV447RK': '可对经过该节点的用户打标签，最多5个',
    'operationCenter-WCFdNAbotANU': '活动用户去重分支',
    'operationCenter-26729vVvNAal': '通过',
    'operationCenter-cUYMzOImEk9r': '被排除',
    'operationCenter-MFqi5gyi0nPw': '用户标签分支',
    'operationCenter-5WC88f7Yil96': '分群分支',
    'operationCenter-l7d8riU7X4db': '其它分支',
    'operationCenter-z1tHxHZT2G3t': '存入客群',
    'operationCenter-N8pGaw8QFAOQ': '请填写完整的信息',

    // nodeEditor/nodeValidator.jsx
    'operationCenter-Op5cov9Zg7SP': '请选择等待时长',
    'operationCenter-nGCfmup4L8Qq': '请填写完整',
    'operationCenter-AyX8ug9fgYu9': '请选择至少一个分群',
    'operationCenter-jTthMVg91nA6': '内容填写不完整，请检查分群',
    'operationCenter-jaiI38qNPNEP': '请选择至少一个事件',
    'operationCenter-eLiTi3XtykZa': '请选择事件',
    'operationCenter-msr98Iy7689E': '内容填写不完整，请检查',
    'operationCenter-VS5nfJdfSoB9': '请完善分组信息',
    'operationCenter-2zQWukg8cGem': '分组名称不能为空',
    'operationCenter-gEjKQGwWxYTJ': '分组名称不能超过20字',
    'operationCenter-Sjcu9Gl43CVN': '分组名称不可重复',
    'operationCenter-JmgC8KwHSyLJ': '过滤条件不完整，请完善',
    'operationCenter-x6vzrM8Gfuun': '开始时间应大于结束时间',
    'operationCenter-1WKpfawptEir': '结束时间应大于现在时间',
    'operationCenter-bSDwfRl9GpIm': '请保证百分比相加不大于100%',
    'operationCenter-Ec4hzhMzizGH': '请保证百分比相加等于100%',
    'operationCenter-HbxnrjttfyOt': '事件名称或事件未填写完整',
    'operationCenter-UaBTzSYsrznk': '事件名称最多20个字',
    'operationCenter-OVWYxE5UlOzP': '事件名称未填写完整',
    'operationCenter-fOimQSWbyU8v': '至少填写一个事件',
    'operationCenter-HqcNqYVDk43p': '分支名称或事件未填写完整',
    'operationCenter-o3cXYs1j9Uql': '事件未填写完整',
    'operationCenter-UCsr5lhucoWj': '分群分支名称或事件未填写完整',
    'operationCenter-nnMYQ0ELCRHI': '分群分支名称最多20个字',
    'operationCenter-sRl7kehuu3HI': '分群分支名称未填写完整',
    'operationCenter-86naH8W3v07m': '标签名称或者标签值填写不完整',
    'operationCenter-cwsGKts3T8Wm': '标签名称或者标签值最多20个字',
    'operationCenter-0LY4d2ZbLmxN': '标签名称不能重复'
  },
  en: {
    // nodeEditor/nodeEditor.jsx
    'operationCenter-536WeAfm04JB': 'Wait Time',
    'operationCenter-2GARVAb7FCHP': 'Maximum wait time is 60 days',
    'operationCenter-33tVgqagnzOi': 'Wait Time V2',
    'operationCenter-Axllm1XbTTUf': 'Maximum wait time is 60 days, at least one time unit must be selected as wait duration',
    'operationCenter-opyLV1jafrrH': 'Wait Time V3',
    'operationCenter-3PFSzmUCgBgu': 'Session Data Branch',
    'operationCenter-JXPbO5piXqmh': 'Configure process engine session branch',
    'operationCenter-WsjUiedYjlBV': 'User Segment',
    'operationCenter-YfkxeO4Brga1': 'Multiple segments can be selected for activity delivery, and the system will deduplicate users after merging. Maximum 5 segments.',
    'operationCenter-ulJlxGC6F7Pm': 'Trigger Event Branch',
    'operationCenter-QPMaQaxZZhoC': 'Maximum 5 trigger events can be added. Completing any event satisfies the trigger.',
    'operationCenter-FKkYXqJTV4Hx': 'Trigger Event Branch V2',
    'operationCenter-7WyQnKlBlX7C': 'Trigger Event Branch V3',
    'operationCenter-yGgYwqvcW0vg': 'User Condition Branch',
    'operationCenter-98Xy0eXRCSdc': 'Please configure different user branches by conditions, maximum 5 branches. Users who satisfy the previous branch will not participate in subsequent branch judgments.',
    'operationCenter-EHtK3zRIvLNe': 'Start Test',
    'operationCenter-kLm7P8HczaBc': 'Process rule adjustments in start test',
    'operationCenter-NiFzUZKSDqLQ': 'A/B Test',
    'operationCenter-gFxqaiKSWtiX': 'Users can be randomly split into 2-5 groups, please ensure percentages add up to 100',
    'operationCenter-3N6C8iq2pdj7': 'Group A',
    'operationCenter-RscUOG1q43KJ': 'Group B',
    'operationCenter-qLdM61m5iMhM': 'Start Event',
    'operationCenter-hTL6e2KAe1IO': 'Maximum 5 start events can be set, users who meet any of the following events will trigger this activity.',
    'operationCenter-knm8W3EI7PFU': 'Embedded Component',
    'operationCenter-N59TSeume63a': 'Embed Component',
    'operationCenter-Qis8QPp5PPUO': 'Configure Specific Time',
    'operationCenter-At6zVsMhbqTR': 'Multi-Event Branch',
    'operationCenter-zJKkq2czbT0J': 'Please configure different event branches, maximum 5 branches.',
    'operationCenter-BLpR0PJxvwx8': 'Multi-Event Branch V2',
    'operationCenter-hXm5rnNwpe2S': 'Configure Tag Branch',
    'operationCenter-EY6moC1nyb3C': 'Node Tag',
    'operationCenter-5NybqKV447RK': 'Users passing through this node can be tagged, maximum 5',
    'operationCenter-WCFdNAbotANU': 'Activity User Deduplication Branch',
    'operationCenter-26729vVvNAal': 'Pass',
    'operationCenter-cUYMzOImEk9r': 'Excluded',
    'operationCenter-MFqi5gyi0nPw': 'User Tag Branch',
    'operationCenter-5WC88f7Yil96': 'Segment Branch',
    'operationCenter-l7d8riU7X4db': 'Other Branch',
    'operationCenter-z1tHxHZT2G3t': 'Save to Segment',
    'operationCenter-N8pGaw8QFAOQ': 'Please fill in complete information',

    // nodeEditor/nodeValidator.jsx
    'operationCenter-Op5cov9Zg7SP': 'Please select wait duration',
    'operationCenter-nGCfmup4L8Qq': 'Please fill in completely',
    'operationCenter-AyX8ug9fgYu9': 'Please select at least one segment',
    'operationCenter-jTthMVg91nA6': 'Content is incomplete, please check segment',
    'operationCenter-jaiI38qNPNEP': 'Please select at least one event',
    'operationCenter-eLiTi3XtykZa': 'Please select event',
    'operationCenter-msr98Iy7689E': 'Content is incomplete, please check',
    'operationCenter-VS5nfJdfSoB9': 'Please complete group information',
    'operationCenter-2zQWukg8cGem': 'Group name cannot be empty',
    'operationCenter-gEjKQGwWxYTJ': 'Group name cannot exceed 20 characters',
    'operationCenter-Sjcu9Gl43CVN': 'Group names cannot be duplicated',
    'operationCenter-JmgC8KwHSyLJ': 'Filter conditions are incomplete, please complete',
    'operationCenter-x6vzrM8Gfuun': 'Start time should be greater than end time',
    'operationCenter-1WKpfawptEir': 'End time should be greater than current time',
    'operationCenter-bSDwfRl9GpIm': 'Please ensure percentages do not exceed 100%',
    'operationCenter-Ec4hzhMzizGH': 'Please ensure percentages add up to 100%',
    'operationCenter-HbxnrjttfyOt': 'Event name or event is not filled completely',
    'operationCenter-UaBTzSYsrznk': 'Event name can be at most 20 characters',
    'operationCenter-OVWYxE5UlOzP': 'Event name is not filled completely',
    'operationCenter-fOimQSWbyU8v': 'At least fill in one event',
    'operationCenter-HqcNqYVDk43p': 'Branch name or event is not filled completely',
    'operationCenter-o3cXYs1j9Uql': 'Event is not filled completely',
    'operationCenter-UCsr5lhucoWj': 'Segment branch name or event is not filled completely',
    'operationCenter-nnMYQ0ELCRHI': 'Segment branch name can be at most 20 characters',
    'operationCenter-sRl7kehuu3HI': 'Segment branch name is not filled completely',
    'operationCenter-86naH8W3v07m': 'Tag name or tag value is not filled completely',
    'operationCenter-cwsGKts3T8Wm': 'Tag name or tag value can be at most 20 characters',
    'operationCenter-0LY4d2ZbLmxN': 'Tag names cannot be duplicated'
  }
};
