export default {
  cn: {
    'operationCenter-NNNYLVr9yneF': '请选择时间',
    'operationCenter-4IpKKizg9YHa': '周期时间',
    'operationCenter-fhzczGCe6A1C': '周一',
    'operationCenter-JpWEGOWv3tkW': '周二',
    'operationCenter-IvDDflhLT7Kc': '周三',
    'operationCenter-tYspcL2T32Ct': '周四',
    'operationCenter-iwRM0hpwtLEB': '周五',
    'operationCenter-Lrr52eWEsQAP': '周六',
    'operationCenter-Y8sEZ5EmF6BR': '周日',
    'operationCenter-VlzooSyzXQtM': '固定日期',
    'operationCenter-rb2mQl5WcLjX': '号',
    'operationCenter-r7AigAumB5G1': '相对月末',
    'operationCenter-R2wIWxSRQ3Dk': '倒数天数',
    'operationCenter-T8CnfYCe1mzv': '最后一天',
    'operationCenter-fx9z0OCxtp0l': '重复频率：',
    'operationCenter-hq55bcQO66vU': '每日',
    'operationCenter-MICdSLeHd9zg': '每周',
    'operationCenter-0zEySh0JGfVk': '每月',
    'operationCenter-3gnixWGCI9NQ': '选择时间：',
    'operationCenter-s3QXRNc3oXnN': '添加事件',
    'operationCenter-rOhUX1avcfP7': '黑名单：',
    'operationCenter-Jjdkz78d45u4': '包括过滤全局黑名单用户',
    'operationCenter-qczfSwhaKM7I': '做过：',
    'operationCenter-T0Qc0O8SNj0o': '未做过：',
    'operationCenter-rkEOhbmySMs9': '渠道联动配置',
    'operationCenter-PPA51xnsbv5o': 'MA生成消息ID (key为messageld)，触达用户时推送下游。使用该功能时，下游需要将messageld在回执数据中回传',
    'operationCenter-MWe9qCmdkFis': '关联消息id',
    'operationCenter-epMZIJTdZAQY': '做过以下事件：',
    'operationCenter-UhFwEppmILuJ': '且 未做过：',
    'operationCenter-0TVDhNnaQPT7': '默认分支',
    'operationCenter-vqajobHzzCCq': '做过',
    'operationCenter-JyRbmPeRIMlr': '未做过',
    'operationCenter-pIBQZNHuwo9W': '配置事件：',
    'operationCenter-mZerTr98MU4T': '做过事件',
    'operationCenter-qFEDaSypQQOE': '未做过事件',
    'operationCenter-wO9wOeOApqDV': '不符合主分支的剩余用户',
    'operationCenter-UXnITul648NY': '测试时间:',
    'operationCenter-iYjAYbZnHRkZ': '测试用户分群',
    'operationCenter-KsXxwgVsYgeK': '运营活动的测试流程中，仅可选择状态为正常，标记为测试的分群',
    'operationCenter-iDe9228pmaJb': '等待时长:',
    'operationCenter-lyUbZ6St56F0': '标签一：',
    'operationCenter-ahipfdm1I0Yj': '标签二：',
    'operationCenter-pH4x4MyPm12N': '标签三：',
    'operationCenter-en9rdoOBr3Rz': '标签四：',
    'operationCenter-tq7y6Ab6W714': '标签五：',
    'operationCenter-WeXIOsUgU6pu': '标签名称',
    'operationCenter-DFj5bjc1G0aq': '标签值',
    'operationCenter-yZtGLhGioFTo': '标签名称不可重复，不超过20个字',
    'operationCenter-9atn7I1dKiBO': '标签值，不超过20个字',
    'operationCenter-x50hZ97ZNypH': '无法添加更多标签，请先删除一个标签',
    'operationCenter-tFSMxDsmjXuw': '+ 添加节点标签',
    'operationCenter-dm80pOhtJTk9': '分组名称',
    'operationCenter-Sau3V8YT2WOJ': '平均分配',
    'operationCenter-PYZLTlxC9eyL': '分组',
    'operationCenter-TfrEl7fL28nX': '分组名称不可重复，不超过20字',
    'operationCenter-pWw8NjrztVIk': '活动批次结束计算',
    'operationCenter-zNxkL6dEJ2o4': '请填写失效时间',
    'operationCenter-dajr7b12lheG': '写入规则',
    'operationCenter-Kktv9PH678l3': '客群写入规则是控制流经该节点的的用户在批次结束时写入分群',
    'operationCenter-ii15JM3OubCN': '：活动批次结束计算',
    'operationCenter-8RmFqDI4erql': '用户ID类型：',
    'operationCenter-wInaNEd73r0a': '实体类型：',
    'operationCenter-k46NEdVjhAKd': '有效时间：',
    'operationCenter-eylkLuBOwYt1': '永久有效',
    'operationCenter-2rhIW6qnFEnS': '失效时间',
    'operationCenter-YS6ld98vQkf3': '用户分群名称',
    'operationCenter-ZB7sRXDsSGtA': '用户分群规则：活动ID - 批次ID - 节点ID - 自定义名称',
    'operationCenter-GcDMgw9IFr0d': '例如：213 - 19826 - 129866 - 高消费人群',
    'operationCenter-0IOQuBhEAGCm': '请输入分群名称',
    'operationCenter-DzNONN1EMrp4': '最多输入30个字符',
    'operationCenter-Qw8xT1dkPSFK': '请输入字母、数字、字符(._-)或者汉字',
    'operationCenter-DaFxgg7CTZtu': '活动ID - 批次ID - 节点ID',
    'operationCenter-IGgbE1Z2h0Qp': '请输入',
    'operationCenter-jOEAhfwGWFbm': '是否包含测试流程',
    'operationCenter-uV18iOMAt72z': '备注',
    'operationCenter-rGbAOMRl17rm': '最多输入150个字符',
    'operationCenter-7J2M391lDatG': '分支',
    'operationCenter-XDtrfIoDt5bb': '分支名称必填',
    'operationCenter-mrkmxgPVI16D': '不能超过20个字符',
    'operationCenter-Q6DjN0ln6pZq': '分支名称，不超过20字',
    'operationCenter-waaKrBrZIESw': '添加分支',
    'operationCenter-PZ0R4GPR2ZBd': '其他分支：不属于以上任意分支的剩余用户',
    'operationCenter-NCMom4yPw6Ag': '且',
    'operationCenter-KoLoHgiiPHmo': '或',
    'operationCenter-YfKYk6Kiyvyw': '请输入节点标签名称',
    'operationCenter-IWhebdioH5iJ': '等于',
    'operationCenter-yfKpjh88mVys': '不等于',
    'operationCenter-zJKG4rHbAVs4': '请输入值',
    'operationCenter-QTAIN6jq6Xon': '添加标签',
    'operationCenter-STznajNtqasr': '写入规则：活动批次结束计算',
    'operationCenter-wrwVyrzPuZCh': '标记为测试分群',
    'operationCenter-33147J4BC8vc': '测试分群可以作为普通分群参与所有活动。运营活动的“测试流程”中，仅可选择测试分群',
    'operationCenter-LfoYPYWy9uVE': '标记为黑（白）名单分群',
    'operationCenter-BISvX2PEVNDk': '不标记',
    'operationCenter-thOuiXIJYn7X': '黑名单',
    'operationCenter-g50RWwRzmBkW': '白名单',
    'operationCenter-UsRDIe3EWYOO': '默认',
    'operationCenter-UHF2ySJmq5GN': '黑',
    'operationCenter-GxhTbOmkqYI7': '白',
    'operationCenter-9QoHb2rCjwMI': '名单分群',
    'operationCenter-E2KaCSRoWQnQ': '请先填写完成内容',
    'operationCenter-NE0blFx115uF': '仅可选择符合该流程的ID类型的分群',
    'operationCenter-UyYwptBcEohP': '选择分群',
    'operationCenter-e0faEhk4aI7a': '去重后合计',
    'operationCenter-YhIYR0V36dNS': '计算人数',
    'operationCenter-8MG4LhuzmvLr': '预估人数',
    'operationCenter-vkvLuetyzTyz': '包括过滤全局黑名单用户',
    'operationCenter-vwiP2jby0Shz': '排除符合以下规则的用户',
    'operationCenter-Tp3ZhgLtISBx': '符合排除规则的用户将直接结束流程',
    'operationCenter-KXRzot96aJUR': '按活动排除用户:',
    'operationCenter-liosdwZd9cxy': '输入搜索活动',
    'operationCenter-hocImnQ6lV3r': '最大支持30个活动',
    'operationCenter-R14aSTQZb8qH': '按时间排除用户：',
    'operationCenter-E4VA0Zs7lSsS': '选择排除活动',
    'operationCenter-lvEvtaaXKFjG': '不排除',
    'operationCenter-mCyREy7WdcIw': '最近N天',
    'operationCenter-NJqo6gDD7TN1': '天',
    'operationCenter-Eto6ros832ex': '仅排除我创建的活动',
    'operationCenter-KVSbB9veUA9t': '排除当前活动历史批次',
    'operationCenter-tqfkKGiPkEEu': '当前批次排除以下用户',
    'operationCenter-wMvaIrwmeR4X': '添加人群分支',
    'operationCenter-TgVJ1KqxqtTK': '单次时间',
    'operationCenter-i2N9AF7pFvlk': '人'
  },
  en: {
    'operationCenter-NNNYLVr9yneF': 'Please select time',
    'operationCenter-4IpKKizg9YHa': 'Cycle time',
    'operationCenter-fhzczGCe6A1C': 'Monday',
    'operationCenter-JpWEGOWv3tkW': 'Tuesday',
    'operationCenter-IvDDflhLT7Kc': 'Wednesday',
    'operationCenter-tYspcL2T32Ct': 'Thursday',
    'operationCenter-iwRM0hpwtLEB': 'Friday',
    'operationCenter-Lrr52eWEsQAP': 'Saturday',
    'operationCenter-Y8sEZ5EmF6BR': 'Sunday',
    'operationCenter-VlzooSyzXQtM': 'Fixed date',
    'operationCenter-rb2mQl5WcLjX': 'Day',
    'operationCenter-r7AigAumB5G1': 'Relative to the end of the month',
    'operationCenter-R2wIWxSRQ3Dk': 'Last N days',
    'operationCenter-T8CnfYCe1mzv': 'Last day',
    'operationCenter-fx9z0OCxtp0l': 'Repeat frequency:',
    'operationCenter-hq55bcQO66vU': 'Daily',
    'operationCenter-MICdSLeHd9zg': 'Weekly',
    'operationCenter-0zEySh0JGfVk': 'Monthly',
    'operationCenter-3gnixWGCI9NQ': 'Select time:',
    'operationCenter-s3QXRNc3oXnN': 'Add event',
    'operationCenter-rOhUX1avcfP7': 'Blacklist:',
    'operationCenter-Jjdkz78d45u4': 'Include filtering global blacklist users',
    'operationCenter-qczfSwhaKM7I': 'Done:',
    'operationCenter-T0Qc0O8SNj0o': 'Not done:',
    'operationCenter-rkEOhbmySMs9': 'Channel linkage configuration',
    'operationCenter-PPA51xnsbv5o': 'MA generate message ID (key is messageld), push to downstream when reaching users. When using this function, the downstream needs to return messageld in the return receipt data',
    'operationCenter-MWe9qCmdkFis': 'Associated message ID',
    'operationCenter-epMZIJTdZAQY': 'Done events:',
    'operationCenter-UhFwEppmILuJ': 'And not done:',
    'operationCenter-0TVDhNnaQPT7': 'Default branch',
    'operationCenter-vqajobHzzCCq': 'Done',
    'operationCenter-JyRbmPeRIMlr': 'Not done',
    'operationCenter-pIBQZNHuwo9W': 'Configure event:',
    'operationCenter-mZerTr98MU4T': 'Done event',
    'operationCenter-qFEDaSypQQOE': 'Not done event',
    'operationCenter-wO9wOeOApqDV': 'Users not in the main branch',
    'operationCenter-UXnITul648NY': 'Test time:',
    'operationCenter-iYjAYbZnHRkZ': 'Test user group',
    'operationCenter-KsXxwgVsYgeK': 'In the test process of the operation activity, only the status can be selected as normal and the group can be selected as test',
    'operationCenter-iDe9228pmaJb': 'Wait time:',
    'operationCenter-lyUbZ6St56F0': 'Tag 1:',
    'operationCenter-ahipfdm1I0Yj': 'Tag 2:',
    'operationCenter-pH4x4MyPm12N': 'Tag 3:',
    'operationCenter-en9rdoOBr3Rz': 'Tag 4:',
    'operationCenter-tq7y6Ab6W714': 'Tag 5:',
    'operationCenter-WeXIOsUgU6pu': 'Tag name',
    'operationCenter-DFj5bjc1G0aq': 'Tag value',
    'operationCenter-yZtGLhGioFTo': 'Tag name cannot be repeated, up to 20 characters',
    'operationCenter-9atn7I1dKiBO': 'Tag value, up to 20 characters',
    'operationCenter-x50hZ97ZNypH': 'Cannot add more tags, please delete a tag first',
    'operationCenter-tFSMxDsmjXuw': '+ Add node tag',
    'operationCenter-dm80pOhtJTk9': 'Group name',
    'operationCenter-Sau3V8YT2WOJ': 'Average allocation',
    'operationCenter-PYZLTlxC9eyL': 'Group',
    'operationCenter-TfrEl7fL28nX': 'Group name cannot be repeated, up to 20 characters',
    'operationCenter-pWw8NjrztVIk': 'Activity batch end calculation',
    'operationCenter-zNxkL6dEJ2o4': 'Please fill in the expiration time',
    'operationCenter-dajr7b12lheG': 'Write rule',
    'operationCenter-Kktv9PH678l3': 'The group write rule is to control the users who flow through this node at the end of the batch',
    'operationCenter-ii15JM3OubCN': ': Activity batch end calculation',
    'operationCenter-8RmFqDI4erql': 'User ID type:',
    'operationCenter-wInaNEd73r0a': 'Entity type:',
    'operationCenter-k46NEdVjhAKd': 'Effective time:',
    'operationCenter-eylkLuBOwYt1': 'Permanent valid',
    'operationCenter-2rhIW6qnFEnS': 'Expiration time',
    'operationCenter-YS6ld98vQkf3': 'User group name',
    'operationCenter-ZB7sRXDsSGtA': 'User group rule: activity ID - batch ID - node ID - custom name',
    'operationCenter-GcDMgw9IFr0d': 'For example: 213 - 19826 - 129866 - High-consuming population',
    'operationCenter-0IOQuBhEAGCm': 'Please enter the group name',
    'operationCenter-DzNONN1EMrp4': 'Up to 30 characters',
    'operationCenter-Qw8xT1dkPSFK': 'Please enter letters, numbers, characters (._-) or Chinese',
    'operationCenter-DaFxgg7CTZtu': 'Activity ID - batch ID - node ID',
    'operationCenter-IGgbE1Z2h0Qp': 'Please enter',
    'operationCenter-jOEAhfwGWFbm': 'Is it included in the test process',
    'operationCenter-uV18iOMAt72z': 'Note',
    'operationCenter-rGbAOMRl17rm': 'Up to 150 characters',
    'operationCenter-7J2M391lDatG': 'Branch',
    'operationCenter-XDtrfIoDt5bb': 'Branch name is required',
    'operationCenter-mrkmxgPVI16D': 'Cannot exceed 20 characters',
    'operationCenter-Q6DjN0ln6pZq': 'Branch name, up to 20 characters',
    'operationCenter-waaKrBrZIESw': 'Add branch',
    'operationCenter-PZ0R4GPR2ZBd': 'Other branches: users not in any of the above branches',
    'operationCenter-NCMom4yPw6Ag': 'AND',
    'operationCenter-KoLoHgiiPHmo': 'OR',
    'operationCenter-YfKYk6Kiyvyw': 'Please enter the node tag name',
    'operationCenter-IWhebdioH5iJ': 'Equal to',
    'operationCenter-yfKpjh88mVys': 'Not equal to',
    'operationCenter-zJKG4rHbAVs4': 'Please enter the value',
    'operationCenter-QTAIN6jq6Xon': 'Add tag',
    'operationCenter-STznajNtqasr': 'Write rule: Activity batch end calculation',
    'operationCenter-wrwVyrzPuZCh': 'Mark as test group',
    'operationCenter-33147J4BC8vc': 'Test groups can participate in all activities as normal groups. In the test process of the operation activity, only the test groups can be selected',
    'operationCenter-LfoYPYWy9uVE': 'Mark as black (white) list group',
    'operationCenter-BISvX2PEVNDk': 'Not marked',
    'operationCenter-thOuiXIJYn7X': 'Black list',
    'operationCenter-g50RWwRzmBkW': 'White list',
    'operationCenter-UsRDIe3EWYOO': 'Default',
    'operationCenter-UHF2ySJmq5GN': 'Black',
    'operationCenter-GxhTbOmkqYI7': 'White',
    'operationCenter-9QoHb2rCjwMI': 'List group',
    'operationCenter-E2KaCSRoWQnQ': 'Please fill in the content first',
    'operationCenter-NE0blFx115uF': 'Only groups of the ID type that match the flow can be selected',
    'operationCenter-UyYwptBcEohP': 'Select group',
    'operationCenter-e0faEhk4aI7a': 'After deduplication',
    'operationCenter-YhIYR0V36dNS': 'Calculate number',
    'operationCenter-8MG4LhuzmvLr': 'Estimated number',
    'operationCenter-vkvLuetyzTyz': 'Include filtering global blacklist users',
    'operationCenter-vwiP2jby0Shz': 'Exclude users who meet the following rules',
    'operationCenter-Tp3ZhgLtISBx': 'Users who meet the exclusion rule will directly end the flow',
    'operationCenter-KXRzot96aJUR': 'Exclude users by activity:',
    'operationCenter-liosdwZd9cxy': 'Enter search activity',
    'operationCenter-hocImnQ6lV3r': 'Maximum support 30 activities',
    'operationCenter-R14aSTQZb8qH': 'Exclude users by time:',
    'operationCenter-E4VA0Zs7lSsS': 'Select excluded activity',
    'operationCenter-lvEvtaaXKFjG': 'Not excluded',
    'operationCenter-mCyREy7WdcIw': 'Last N days',
    'operationCenter-NJqo6gDD7TN1': 'Days',
    'operationCenter-Eto6ros832ex': 'Only exclude activities I created',
    'operationCenter-KVSbB9veUA9t': 'Exclude current activity history batch',
    'operationCenter-tqfkKGiPkEEu': 'Current batch excludes the following users',
    'operationCenter-wMvaIrwmeR4X': 'Add user group branch',
    'operationCenter-TgVJ1KqxqtTK': 'Single time',
    'operationCenter-i2N9AF7pFvlk': 'people'
  }
};
