import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';
import CampaignOverviewV2Service from 'service/campaignOverviewV2Service';
import CampaignOverviewChart from './components/chart';
import { ContentSection } from './components/ContentSection';
import { Item } from './components/Item';
import { ChartDataType, dayMap, DayMapKey } from './config';
import { t } from 'utils/translation';

const commonClasses = {
  item: 'item w-160 h-74 rounded-[6px]',
  itemTitle: 'item-title text-14 leading-[24px] my-text-45',
  itemContent: 'item-content text-24',
  section: 'flex bg-[#fff] rounded-[6px] p-24 gap-24',
  left: 'w-512',
  title: 'title text-16 font-bold mb-16',
  content: 'content flex gap-16 mb-16 flex-wrap w-[inherit]'
};

export const CampaignOverviewV2: React.FC = () => {
  const [data, setData] = useState(undefined);
  const [chatSelete, setChatSelete] = useState<DayMapKey>(t('operationCenter-vMi8fDCPDuaf'));
  const [chartData, setChartData] = useState<ChartDataType>({
    avgPassedCount: 0,
    totalPassedCount: 0,
    passedCountTimeList: []
  });

  useEffect(() => {
    (async () => {
      const res = await CampaignOverviewV2Service.listBy([
        {
          propertyName: 'statTime',
          operator: 'DATE_BETWEEN',
          value: `${dayjs().startOf('day').valueOf()},${dayjs().endOf('day').valueOf()}`
        }
      ]);
      setData(res);
    })();
  }, []);

  useEffect(() => {
    (async () => {
      const [begin, end] = dayMap[chatSelete] as [Dayjs, Dayjs];
      const res = await CampaignOverviewV2Service.calcCampaignV2PassedCount({
        beginTime: begin.valueOf(),
        endTime: end.valueOf()
      });
      setChartData(res);
    })();
  }, [chatSelete]);

  return (
    <div className="flex gap-24 flex-col">
      <div className="flex gap-16 mt-16 items-center">
        <div className="text-2xl font-bold">{t('operationCenter-MZtjlKevIUmI')}</div>
        <div className="my-text-45">{t('operationCenter-Bjf9QrZ4TI8k')} {dayjs().format('YYYY-MM-DD HH:mm:ss')}</div>
      </div>

      <div>
        <div className={commonClasses.section}>
          <div className={commonClasses.left}>
            <div className={commonClasses.title}>{t('operationCenter-iViVHUpq8KVF')}</div>
            <ContentSection
              titles={[t('operationCenter-EW2KVQnwz4a3'), t('operationCenter-l7sdM6R7oMjH'), t('operationCenter-l7sdM6R7oMjH')]}
              moduleCode="CAMPAIGN_V2"
              isLeft
              data={data}
            />
          </div>
          <div className="flex flex-col gap-16">
            <div>
              <div className={commonClasses.title}>{t('operationCenter-8WBLhLx2s7qr')}</div>
              <ContentSection
                titles={[t('operationCenter-kWJCcoiNKuNR'), t('operationCenter-BSEgSlE4JclI'), t('operationCenter-uJYyC1RjV8AP'), t('operationCenter-xflTznlGwPqJ'), t('operationCenter-LeHpQJOR1teT')]}
                moduleCode="CAMPAIGN_V2"
                isLeft={false}
                data={data}
              />
            </div>
            <div>
              <div className={commonClasses.title}>{t('operationCenter-C6uMyeTlUn5B')}</div>
              <ContentSection
                titles={[t('operationCenter-x4OUEYxXSr4h'), t('operationCenter-HpvmBKTGIqnS'), t('operationCenter-bAqePfRQ2uJn'), t('operationCenter-XPj4WDRtsMsq'), t('operationCenter-RjSEp8bp4q7r'), t('operationCenter-pYZEWT1xe6il')]}
                moduleCode="CAMPAIGN_V2"
                isLeft={false}
                data={data}
              />
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className={commonClasses.section}>
          <div className={commonClasses.left}>
            <div className={commonClasses.title}>{t('operationCenter-BZWNuCpHg0EK')}</div>
            <ContentSection
              titles={[
                t('operationCenter-NYJAeAoLoFlP'),
                t('operationCenter-cRxd2We9GgqB'),
                t('operationCenter-F1ZUhvxiRMuy'),
                t('operationCenter-ywVNFQ0rzr1I'),
                t('operationCenter-msJtsqpepEOb'),
                t('operationCenter-lIp16hhdVJbu')
              ]}
              moduleCode="CAMPAIGN_V2_CALC_LOG"
              isLeft
              data={data}
            />
          </div>
          <div className="flex flex-col gap-16">
            <div>
              <div className={commonClasses.title}>{t('operationCenter-4PRcATQMDIre')}</div>
              <ContentSection
                titles={[t('operationCenter-250UdDDqtcwa'), t('operationCenter-cQxMb489jeUm'), t('operationCenter-FLrEnNva69zy'), t('operationCenter-dupagM3vHeB5'), t('operationCenter-nsbbw4EyeaAK')]}
                moduleCode="CAMPAIGN_V2_CALC_LOG"
                isLeft={false}
                data={data}
              />

              <ContentSection
                titles={[t('operationCenter-DZpfaNw8DjFr'), t('operationCenter-qPFW0hkh2cLb'), t('operationCenter-Qp5MvFD97bpj')]}
                moduleCode="CAMPAIGN_V2_CALC_LOG"
                isLeft={false}
                data={data}
              />

              <ContentSection
                titles={[t('operationCenter-Vg8QCnw8pdEe'), t('operationCenter-w0XN3YBxNATU'), t('operationCenter-4fEh6Hs0fbht')]}
                moduleCode="CAMPAIGN_V2_CALC_LOG"
                isLeft={false}
                data={data}
              />

              <ContentSection
                titles={[t('operationCenter-RU4KA41SyZ5f'), t('operationCenter-YTomt3CWAqLM'), t('operationCenter-pbZJyv75QRwp')]}
                moduleCode="CAMPAIGN_V2_CALC_LOG"
                isLeft={false}
                data={data}
              />
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className={commonClasses.section}>
          <div className={commonClasses.left}>
            <div className={commonClasses.title}>{t('operationCenter-IbZToIZ1XCvA')}</div>
            <ContentSection
              titles={[t('operationCenter-nqozRzTBM1OR'), t('operationCenter-oU4CKmaWqxK0'), t('operationCenter-LdLyE93luxE7')]}
              moduleCode="SEGMENT"
              isLeft
              data={data}
            />
          </div>
          <div className="flex flex-col gap-16">
            <div>
              <div className={commonClasses.title}>{t('operationCenter-7CeakWS6dIDM')}</div>
              <ContentSection
                titles={[t('operationCenter-hRiVWjSVdGb8'), t('operationCenter-DGjKpBfMB8xh'), t('operationCenter-8RIUgybNkdyk'), t('operationCenter-uJMHWNGDxxhS'), t('operationCenter-gUpAWpynjBDu')]}
                moduleCode="SEGMENT"
                isLeft={false}
                data={data}
              />
            </div>
            <div>
              <div className={commonClasses.title}>{t('operationCenter-9GGUXdhRoQJE')}</div>
              <ContentSection
                titles={[t('operationCenter-x4OUEYxXSr4h'), t('operationCenter-HpvmBKTGIqnS'), t('operationCenter-bAqePfRQ2uJn'), t('operationCenter-XPj4WDRtsMsq'), t('operationCenter-RjSEp8bp4q7r'), t('operationCenter-pYZEWT1xe6il')]}
                moduleCode="SEGMENT"
                isLeft={false}
                data={data}
              />
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className={commonClasses.section}>
          <div className={commonClasses.left}>
            <div className={commonClasses.title}>{t('operationCenter-T91teANpCljK')}</div>
            <ContentSection
              titles={[t('operationCenter-ZZJ4WpChvuSV'), t('operationCenter-zHuGgWLcpkoJ'), t('operationCenter-xqtWfU6fY4Ni'), t('operationCenter-BCnKUATv9Efv')]}
              moduleCode="SEGMENT_CALC_LOG"
              isLeft
              data={data}
            />
          </div>
          <div className="flex flex-col gap-16">
            <div>
              <div className={commonClasses.title}>{t('operationCenter-lKXuxSUkUuMg')}</div>
              <ContentSection
                titles={[t('operationCenter-8DZsDkmLpsNX'), t('operationCenter-Eja9lpKVztqz'), t('operationCenter-iUrKGvIQiWct'), t('operationCenter-uvBSEdW7rYj6')]}
                moduleCode="SEGMENT_CALC_LOG"
                isLeft={false}
                data={data}
              />
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className={commonClasses.section}>
          <div className="w-200">
            <div className={commonClasses.title}>{t('operationCenter-2KiMyj1dan88')}</div>
            <div className={commonClasses.content}>
              <Item title={t('operationCenter-90HrRltPphab')} isLeft={false} number={chartData.totalPassedCount} data={data} />
            </div>
            <div className={commonClasses.content}>
              <Item title={t('operationCenter-KLP6keb1rIpm')} isLeft={false} number={chartData.avgPassedCount} data={data} />
            </div>
          </div>
          <div className="w-[calc(98%-200px)] flex flex-col gap-16">
            <CampaignOverviewChart
              chartData={chartData}
              chatSelete={chatSelete}
              setChatSelete={setChatSelete}
              dayMap={dayMap}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
