import { Line } from '@ant-design/charts';
import { Select } from 'antd';
import { Dayjs } from 'dayjs';
import _ from 'lodash';
import React from 'react';
import { chartConfig, ChartDataType, chartField, DayMapKey } from '../config/index';
import { t } from 'utils/translation';

interface ChartComponentProps {
  chartData: ChartDataType;
  chatSelete: DayMapKey;
  setChatSelete: (value: DayMapKey) => void;
  dayMap: Record<DayMapKey, [Dayjs, Dayjs]>;
  className?: string;
}

const campaignOverviewChart: React.FC<ChartComponentProps> = ({
  chartData,
  chatSelete,
  setChatSelete,
  dayMap,
  className = 'w-[98%]'
}) => {
  return (
    <div className={`${className} flex flex-col gap-16`}>
      <div className="ml-[auto] flex items-center gap-16">
        <span className="my-text-45">
          {t('operationCenter-r5AfgNuWMLOH')} {`${dayMap[chatSelete][0].format('YYYY-MM-DD')} - ${dayMap[chatSelete][1].format('YYYY-MM-DD')}`}
        </span>
        <Select
          value={chatSelete}
          style={{ width: 200 }}
          options={_.map(dayMap, (_, key) => ({ label: key, value: key }))}
          onChange={setChatSelete}
        />
      </div>
      <Line {...chartConfig} data={chartData.passedCountTimeList} meta={chartField} />
    </div>
  );
};

export default campaignOverviewChart;
