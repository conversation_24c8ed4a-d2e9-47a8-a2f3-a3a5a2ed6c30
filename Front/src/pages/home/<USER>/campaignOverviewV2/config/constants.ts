import dayjs, { Dayjs } from 'dayjs';
import { DayMapKey, JumpConfig, QuarterMap } from './types';
import { getQuarter } from './utils';
import { t } from 'utils/translation';

export const color = {
  warn: '#FAAD14',
  error: '#FF4D4F',
  success: '#52C41A',
  info: '#1677FF',
  disabled: '#D9D9D9'
};

export const descMap = {
  [t('operationCenter-NYJAeAoLoFlP')]: t('operationCenter-uDPNRNMHrbcr'),
  [t('operationCenter-ywVNFQ0rzr1I')]: t('operationCenter-Gy5H8oA6soHw'),
  [t('operationCenter-msJtsqpepEOb')]: t('operationCenter-pwfeB1zQogDh'),
  [t('operationCenter-lIp16hhdVJbu')]: t('operationCenter-dVLVxIoMTM36'),
  [t('operationCenter-uJMHWNGDxxhS')]: t('operationCenter-NsRD5KWQ9tdf'),
  [t('operationCenter-gUpAWpynjBDu')]: t('operationCenter-ZopIEx69G0DZ'),
  [t('operationCenter-ZZJ4WpChvuSV')]: t('operationCenter-myFkiZLswRHW'),
  [t('operationCenter-zHuGgWLcpkoJ')]: t('operationCenter-TKcgz5kzTctN'),
  [t('operationCenter-xqtWfU6fY4Ni')]: t('operationCenter-4BgbjYDsYivs'),
  [t('operationCenter-BCnKUATv9Efv')]: t('operationCenter-aRrQYObRY1Xj')
};

export const statusColor = {
  [t('operationCenter-kWJCcoiNKuNR')]: color.disabled,
  [t('operationCenter-BSEgSlE4JclI')]: color.info,
  [t('operationCenter-uJYyC1RjV8AP')]: color.success,
  [t('operationCenter-xflTznlGwPqJ')]: color.success,
  [t('operationCenter-8RIUgybNkdyk')]: color.error,
  [t('operationCenter-LeHpQJOR1teT')]: color.success,
  [t('operationCenter-250UdDDqtcwa')]: color.success,
  [t('operationCenter-PMx4CGhf477n')]: color.success,
  [t('operationCenter-cQxMb489jeUm')]: color.warn,
  [t('operationCenter-FLrEnNva69zy')]: color.error,
  [t('operationCenter-Eja9lpKVztqz')]: color.success,
  [t('operationCenter-iUrKGvIQiWct')]: color.error,
  [t('operationCenter-uvBSEdW7rYj6')]: color.info,
};

export const chartConfig = {
  xField: 'date',
  yField: 'passedCount',
  xAxis: {
    label: {
      autoRotate: false
    }
  },
  maxColumnWidth: 30,
  slider: {
    start: 0,
    end: 1
  }
};

export const chartField = {
  date: {
    alias: t('operationCenter-52FBI6aWsZpI')
  },
  passedCount: {
    alias: t('operationCenter-rJVQi6fZ43Q0')
  }
};

export const quarterMap: QuarterMap = {
  1: [dayjs().startOf('year'), dayjs().startOf('year').add(2, 'month').endOf('month')],
  2: [dayjs().startOf('year').add(3, 'month'), dayjs().startOf('year').add(5, 'month').endOf('month')],
  3: [dayjs().startOf('year').add(6, 'month'), dayjs().startOf('year').add(8, 'month').endOf('month')],
  4: [dayjs().startOf('year').add(9, 'month'), dayjs().endOf('year')]
} as const;

// @ts-ignore
export const dayMap: Record<DayMapKey, [Dayjs, Dayjs]> = {
  [t('operationCenter-IH2TJi2DII6p')]: [dayjs().startOf('year'), dayjs().subtract(1, 'day')],
  [t('operationCenter-Yuam4KALm6le')]: [dayjs().startOf('month'), dayjs().subtract(1, 'day')],
  [t('operationCenter-vMi8fDCPDuaf')]: [quarterMap[getQuarter()][0], dayjs().subtract(1, 'day')],
  [t('operationCenter-Ks9LbkfYKIOH')]: quarterMap[1],
  [t('operationCenter-X1a0mdFWge3J')]: quarterMap[2],
  [t('operationCenter-ts7GYU7e1x8T')]: quarterMap[3],
  [t('operationCenter-fuzemAoBAeL8')]: quarterMap[4]
} as const;

export const jumpTo: JumpConfig = {
  // @ts-ignore
  campaignV2: {
    [t('operationCenter-kWJCcoiNKuNR')]: {
      propertyName: 'phase',
      operator: 'EQ',
      value: 'DRAFT'
    },
    [t('operationCenter-BSEgSlE4JclI')]: {
      propertyName: 'phase',
      operator: 'EQ',
      value: 'TESTING'
    },
    [t('operationCenter-uJYyC1RjV8AP')]: {
      propertyName: 'phase',
      operator: 'EQ',
      value: 'TEST_SUC'
    },
    [t('operationCenter-xflTznlGwPqJ')]: {
      propertyName: 'phase',
      operator: 'EQ',
      value: 'ENABLE'
    },
    [t('operationCenter-LeHpQJOR1teT')]: {
      propertyName: 'phase',
      operator: 'EQ',
      value: 'STOPPED'
    }
  },
  // @ts-ignore
  segment: {
    [t('operationCenter-kWJCcoiNKuNR')]: {
      propertyName: 'status',
      operator: 'EQ',
      value: 'DRAFT'
    },
    [t('operationCenter-xflTznlGwPqJ')]: {
      propertyName: 'status',
      operator: 'EQ',
      value: 'NORMAL'
    },
    [t('operationCenter-8RIUgybNkdyk')]: {
      propertyName: 'status',
      operator: 'EQ',
      value: 'DISABLE'
    },
    [t('operationCenter-yPLQUHBXqv7f')]: {
      propertyName: 'calcStatus',
      operator: 'EQ',
      value: 'NOTRUN',
      connector: 'AND'
    },
    [t('operationCenter-Eja9lpKVztqz')]: {
      propertyName: 'calcStatus',
      operator: 'EQ',
      value: 'SUC',
      connector: 'AND'
    },
    [t('operationCenter-iUrKGvIQiWct')]: {
      propertyName: 'calcStatus',
      operator: 'EQ',
      value: 'FAIL',
      connector: 'AND'
    },
    [t('operationCenter-uvBSEdW7rYj6')]: {
      propertyName: 'calcStatus',
      operator: 'EQ',
      value: 'CALCING',
      connector: 'AND'
    }
  }
};
