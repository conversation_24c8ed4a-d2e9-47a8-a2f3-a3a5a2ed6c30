import { t } from 'utils/translation';

export default {
  columns: [
    {
      title: t('portraitCenter-hsXzjmUcYOqE'),
      dataIndex: 'name',
      width: 200
    },
    {
      title: t('portraitCenter-7o2s6XQoZPkc'),
      dataIndex: ['scenario', 'name'],
      width: 200
    },
    {
      title: t('portraitCenter-pxwfZI8jIXma'),
      dataIndex: 'remark',
      width: 200
    },
    {
      title: t('portraitCenter-QVnyDy0fcNNF'),
      dataIndex: 'convertUnit',
      width: 200,
      render: (text) => {
        let returnText = '';
        switch (text) {
          case 'WEEK':
            returnText = t('portraitCenter-TlAqgHbbhXTK');
            break;
          case 'DAY':
            returnText = t('portraitCenter-3OPcgfnKOAVW');
            break;
          case 'MONTH':
            returnText = t('portraitCenter-6Uncp6RR9WZR');
            break;
          default:
            break;
        }
        return returnText;
      }
    }
  ]
};
