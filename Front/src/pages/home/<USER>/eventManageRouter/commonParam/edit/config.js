import { t } from 'utils/translation';

export default {
  // 表单配置
  elements: {
    'tableSchema.id': {
      type: 'select',
      label: t('dataCenter-WFEsbwzVYxmq'),
      width: 24,
      labelWidth: 7,
      wrapperWidth: 24,
      rules: [{ required: true, message: t('dataCenter-8tk2btRXz2UV') }],
      componentOptions: {
        allowClear: true,
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        showSearch: true,
        placeholder: t('dataCenter-8tk2btRXz2UV'),
        options: []
      }
    },
    'tableSchema.dataType': {
      type: 'input',
      label: t('dataCenter-T5VXeeVXjrtF'),
      width: 24,
      className: 'tableSchema-dataType',
      labelWidth: 7,
      wrapperWidth: 24,
      componentOptions: {
        disabled: true
      }
    },
    displayName: {
      type: 'input',
      label: t('dataCenter-nCj43wMeXIf0'),
      operator: 'LIKE',
      width: 24,
      // labelWidth: 7,
      wrapperWidth: 24,
      rules: [
        { required: true, message: t('dataCenter-MJDNCRejxcjq') },
        { max: 32, message: t('dataCenter-7wcmhEqxkwdg') },
        { pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]*$/, message: t('dataCenter-3EHiLJD7kSRF') }
      ],
      componentOptions: {
        allowClear: true,
        placeholder: t('dataCenter-MJDNCRejxcjq')
      }
    }
  },
  // 表单数据
  formData: {}
};
