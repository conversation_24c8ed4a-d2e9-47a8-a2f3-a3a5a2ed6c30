import { Button, Modal, message } from 'antd';
import FormCom from 'components/featurecoms/formcom/index';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import CommonEventPropManageService from 'service/commonEventPropManage';
import { t } from 'utils/translation';
import config from './config';

import './index.scss';

const commonEventPropManageService = new CommonEventPropManageService();

function Edit(props) {
  const eventId = '0';

  const [elements, setElements] = useState(config.elements);
  const [formData, setFormData] = useState({});
  const [currentTable, setCurrentTable] = useState(null);
  const [detailInfo, setDetailInfo] = useState({});
  const [loading, setLoading] = useState(false);

  const formNode = useRef(null);

  useEffect(() => {
    async function init() {
      const _elements = _.cloneDeep(elements);
      if (props?.val?.id) {
        const detailData = await commonEventPropManageService.get(props.val.id);
        setDetailInfo(detailData);
        const { tableSchema, displayName } = detailData || {};
        const projectId = detailData.projectId;
        setFormData({
          'tableSchema.id': tableSchema.id,
          'tableSchema.dataType': tableSchema.dataType,
          displayName
        });
        setCurrentTable(tableSchema);
        const propertyList = await commonEventPropManageService.findPropertyList({ projectId });
        _elements['tableSchema.id'].componentOptions.options = _.map(propertyList, (item) => ({
          dataSource: item,
          key: item?.tableSchema?.id,
          value: item?.tableSchema?.id,
          text: `${item?.tableSchema?.displayName}[${item?.tableSchema?.name}]`
        }));
      }

      setElements(_elements);
    }
    init();
    findPropertyList();
  }, []);

  async function findPropertyList() {
    const _elements = _.cloneDeep(elements);
    try {
      const propertyList = await commonEventPropManageService.findPropertyList({
        projectId: localStorage.getItem('projectId')
      });
      _elements['tableSchema.id'].componentOptions.options = _.map(propertyList, (item) => ({
        dataSource: item,
        key: item?.tableSchema?.id,
        value: item?.tableSchema?.id,
        text: `${item?.tableSchema?.displayName}[${item?.tableSchema?.name}]`
      }));
      setElements(_elements);
    } catch (error) {
      console.error(error);
    }
  }

  const onFormDataChange = (currentValue, allValues, realKey, realValue) => {
    const _elements = _.cloneDeep(elements);
    if (realKey === 'tableSchema.id') {
      const option = _.find(_elements['tableSchema.id'].componentOptions.options, (item) => realValue === item.value);
      setCurrentTable(option?.dataSource?.tableSchema);
      setFormData({
        ...formData,
        'tableSchema.id': realValue,
        'tableSchema.dataType': option?.dataSource?.tableSchema?.dataType
      });
    }
  };

  function onClose() {
    props.onClose && props.onClose();
  }

  function renderChildren() {
    return (
      <FormCom
        elements={elements}
        ref={formNode}
        defaultFormData={formData}
        onChange={onFormDataChange}
        layout="vertical"
      />
    );
  }

  function renderDetailInfo() {
    if (!props?.val?.id) {
      return null;
    } else {
      return (
        <div className="detail-info">
          <div>{`${t('dataCenter-x377aroYOuMw')} ${detailInfo?.createUserName || ''}`}</div>
          <div>{`${t('dataCenter-g46QrKYEk5S4')} ${detailInfo?.createTime && dayjs(detailInfo.createTime).format('YYYY-MM-DD HH:mm:ss')}`}</div>
          <div>{`${t('dataCenter-jjMvHnGs35JD')}${detailInfo?.updateUserName || ''}`}</div>
          <div>{`${t('dataCenter-3cYFBP4Ma8Oz')}${detailInfo?.updateTime && dayjs(detailInfo.updateTime).format('YYYY-MM-DD HH:mm:ss')}`}</div>
        </div>
      );
    }
  }

  function onSubmit() {
    formNode.current.validateFields(async (err, values) => {
      if (!err) {
        setLoading(true);
        const _params = {
          ...values,
          id: props?.val?.id || '',
          eventId,
          tableSchema: currentTable
        };
        try {
          await commonEventPropManageService.save(_params);
          setLoading(false);
          onClose();
          message.success(t('dataCenter-cYzOmqTBV1st'), 1);
          props.successCallBack && props.successCallBack();
        } catch (error) {
          setLoading(false);
        }
      }
    });
  }

  return (
    <Modal
      title={props?.val?.id ? t('dataCenter-Ej9NSJHsSiOR') : t('dataCenter-KcOUdHvKxssq')}
      width={620}
      open
      destroyOnClose
      footer={null}
      className="common-event-prop-manage"
      onCancel={onClose}
    >
      {renderChildren()}
      {renderDetailInfo()}
      <footer>
        <Button className="cancle" onClick={onClose}>
          {t('dataCenter-eN1b0GCavuJL')}
        </Button>
        <Button type="primary" loading={loading} onClick={onSubmit}>
          {t('dataCenter-b5ksRoDL5EeH')}
        </Button>
      </footer>
    </Modal>
  );
}

export default Edit;
