import { InboxOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, But<PERSON>, Drawer, Form, message, Progress, Radio, Upload } from 'antd';
import React, { useRef, useState } from 'react';
import DataEngineService from 'service/dataEngineService';
import { t } from 'utils/translation';

import './index.scss';

const dataEngineService = new DataEngineService();

function UploadComponent({ onClose, val }) {
  const [filePath, setFilePath] = useState('');
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [form] = Form.useForm();
  const timer = useRef(null);
  // 设置filelist
  const [fileList, setFileList] = useState([]);

  const onFormLayoutChange = (changedFields, allFields) => {
    console.log(changedFields, allFields);
  };

  const beforeUpload = (file) => {
    const isLt200M = (file.size / 1024 / 1024).toFixed(0) <= 200;
    if (!isLt200M) {
      message.error(t('dataCenter-Tk5aDc7FMmVw'));
    }
    return isLt200M;
  };

  const customRequest = async (infos) => {
    try {
      setFilePath('');
      setUploading(true);
      const formData = new FormData();
      formData.append('file', infos.file);
      formData.append('type', 'IMPORT_TABLE');
      const onUploadProgress = (progressEvent) => {
        const complete = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(2);
        setProgress(parseInt(complete));
      };
      const resultData = await dataEngineService.upload(formData, onUploadProgress);
      if (resultData.code !== 1) {
        throw new Error(t('dataCenter-L7L72XhHdxc7'));
      }
      // debugger;
      // const labelList = await Userlabelservice.importUserLabelValueMapping({
      //   filePath: resultData.result,
      //   scenario: scenarioList.find((item) => item.id === userIdType),
      //   uploadConfig: { character: 'UTF8' }
      // });
      // setMatchList(labelList);
      // setLogDataSource(labelList.successDataList);
      setFilePath(resultData.result);
      setFileList([
        {
          uid: '-1',
          name: infos.file.name,
          status: 'done',
          url: resultData.result
        }
      ]);
      form.setFieldsValue({ uploadFile: resultData.result });
      setUploading(false);
      message.success(t('dataCenter-m46F89GZ8RHX'));
    } catch (e) {
      setUploading(false);
    }
  };

  const save = async () => {
    const params = {
      filePath,
      importType: form.getFieldsValue().importType,
      eventPropertyType: 'PRIVATE'
    };

    const doRequest = async (data) => {
      try {
        const res = await dataEngineService.queryEventPropertyEnumSave({
          ...params,
          eventPropertyId: val?.id
        });
        if (res.header.code === 0) {
          message.success('保存成功');
          onClose(true);
        } else if (res.header.code === 210) {
          timer.current = setTimeout(async () => {
            data.force = false;
            await doRequest(data);
          }, 3000);
        } else {
          message.error('保存失败');
        }
      } catch (error) {
        console.error('🚀 ~ doRequest ~ error:', error);
      }
    };
    await doRequest({
      force: true
    });
  };

  return (
    <Drawer
      className="editAndDetail"
      title="上传字典文件"
      placement="right"
      open
      closable
      destroyOnClose
      width={600}
      onClose={() => onClose(false)}
      footer={
        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
          <Button onClick={() => onClose(false)}>取消</Button>
          <Button type="primary" onClick={save}>
            确定
          </Button>
        </div>
      }
    >
      <Alert
        message="csv 后缀每行1个对应关系，第一列为事件名、第二列为属性名、第三列为枚举值、第四列为枚举值显示名，内容应小于“1000行°"
        type="info"
        showIcon
        style={{ marginBottom: '16px' }}
      />

      <Form layout="vertical" form={form} onValuesChange={onFormLayoutChange} initialValues={{ importType: 'APPEND' }}>
        <Form.Item label="上传模式" name="importType" rules={[{ required: true, message: '请选择上传模式' }]}>
          <Radio.Group>
            <Radio value="APPEND">追加</Radio>
            <Radio value="OVERWRITE">覆盖</Radio>
          </Radio.Group>
        </Form.Item>
        <div className="flex justify-between">
          <h3>上传文件</h3>
          <Button>下载数据模板</Button>
        </div>
        <Form.Item label="上传数据文件" name="uploadFile" rules={[{ required: true, message: '请上传文件' }]}>
          <Upload.Dragger
            name="files"
            accept=".csv"
            maxCount={1}
            customRequest={customRequest}
            fileList={fileList}
            beforeUpload={beforeUpload}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖动文件到这里</p>
            <p className="ant-upload-hint">支持扩展名：.csv（csv默认逗号分隔）</p>
            {uploading && (
              <Progress percent={progress} size="small" strokeColor="var(--ant-primary-color)" showInfo={false} />
            )}
          </Upload.Dragger>
        </Form.Item>
      </Form>
    </Drawer>
  );
}

export default UploadComponent;
