import { Button, message, Modal, Space, Switch, Table } from 'antd';
import TableSearch from 'components/bussinesscoms/tableSearch/index';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import UserService from 'service/UserService';
import CommonEventPropManageService from 'service/commonEventPropManage';
import DataEngineService from 'service/dataEngineService';
import CheckAuth from 'utils/checkAuth';
// import EditScenario from './editScenario';
import { t } from 'utils/translation';
import { elements, initParam } from './config';
import './index.scss';
import Upload from './upload/index';

const userService = new UserService();
const dataEngineService = new DataEngineService();
const commonEventPropManageService = new CommonEventPropManageService();

const pagination = {
  showTotal: (totals) => t('dataCenter-V8cVK860JmU0') + totals + t('dataCenter-V0PbMUgm73RO'),
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const TagList = () => {
  const [param, setParam] = useState(_.cloneDeep(initParam));
  const [list, setList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [userList, setUserList] = useState([]);
  const [visibleValue, setVisibleValue] = useState({
    visible: false,
    val: undefined
  });

  useEffect(() => {
    (async () => {
      const newuserList = await userService.listBy([]);
      setUserList(newuserList);
    })();
  }, []);

  useEffect(() => {
    const getList = async () => {
      try {
        setLoading(true);
        const result = await dataEngineService.queryEventPropertyEnum(param);
        pagination.total = result.totalElements;
        pagination.current = param.page;
        pagination.pageSize = param.size;
        setList(result.content);
        setLoading(false);
      } catch {
        setLoading(false);
      }
    };
    getList();
  }, [param]);

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const onColumnActionClick = async (type, record, value) => {
    console.log(type, record, value, 'type, record, value');
    if (type === 'isShowEnumValue') {
      // await commonEventPropManageService.updateIsShowEnumValue(record.id, record.isShowEnumValue);
    } else if (type === 'edit') {
      setVisibleValue({
        visible: true,
        val: record
      });
    } else if (type === 'delete') {
      Modal.confirm({
        title: t('dataCenter-42a3hT9VB6rR'),
        content: (
          <p className="confirmDelete">
            {t('dataCenter-fNu9bCDTz6Iv')}
            <span>{record.displayName}</span>
            <br />
            {t('dataCenter-Hvu036ULFJcg')}
            <br />
            {t('dataCenter-YlqyO6notORt')}
          </p>
        ),
        okText: t('dataCenter-8RiRWkF3h2VZ'),
        okType: 'danger',
        cancelText: t('dataCenter-ZSLhnOfV7qV5'),
        async onOk() {
          await commonEventPropManageService.delById(record.id);
          setParam((prev) => {
            return {
              ...prev,
              page: 1,
              size: 10
            };
          });
          message.success(t('dataCenter-48NYG9UkIQWq'));
        },
        onCancel() {}
      });
    }
  };

  const columns = [
    {
      title: '属性名称',
      dataIndex: 'propertyName'
    },
    {
      title: '属性显示名',
      dataIndex: 'propertyName'
    },
    {
      title: t('dataCenter-0etIOZBZU5cP'),
      dataIndex: 'dataType'
    },
    {
      title: '通用/专有属性',
      dataIndex: 'propertyType',
      render: (text) => (text === 'PRIVATE' ? '专有属性' : text === 'PUBLIC' ? '通用属性' : '-')
    },
    {
      title: '事件名称',
      dataIndex: 'eventName'
    },
    {
      title: '事件显示名',
      dataIndex: ['tableSchema', 'displayName']
    },
    {
      title: '是否显示枚举值',
      dataIndex: 'isEnum',
      render: (text, record) => (
        <Switch checked={text} onChange={(value) => onColumnActionClick('isShowEnumValue', record, value)} />
      )
    },
    {
      title: '字典',
      dataIndex: 'dictName',
      render: (text) => text || '白领融A，惠您贷B，惠您贷C...'
    },
    {
      title: t('dataCenter-GwppZ0H9mCXE'),
      width: 180,
      fixed: 'right',
      onCell: (render) => {
        return { rowSpan: render.rowSpan0 };
      },
      render: (text, record) => (
        <Space>
          <CheckAuth code="aim_event_edit">
            <a
              style={{ marginRight: '8px' }}
              onClick={() => {
                setVisibleValue({
                  visible: true,
                  val: record
                });
              }}
            >
              上传
            </a>
          </CheckAuth>
          <CheckAuth code="aim_event_edit">
            <a style={{ marginRight: '8px' }} onClick={() => onColumnActionClick('refresh', record)}>
              刷新
            </a>
          </CheckAuth>
          <CheckAuth code="aim_event_edit">
            <a style={{ marginRight: '8px' }} onClick={() => onColumnActionClick('delete', record)}>
              删除
            </a>
          </CheckAuth>
        </Space>
      )
    }
  ];

  const onClose = () => {
    setVisibleValue({
      visible: false,
      val: undefined
    });
    setParam(_.cloneDeep(param));
  };

  return (
    <div className="eventManageStyle">
      <div className="search">
        <TableSearch elements={elements({ userList })} span={8} onChange={(data) => setParam({ ...param, ...data })} />
      </div>
      <div className="tableList">
        <div className="toolbar">
          <div className="title">事件专有属性列表</div>
          <CheckAuth code="aim_event_edit">
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  setVisibleValue({
                    visible: true,
                    val: undefined
                  });
                }}
              >
                刷新字典
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  setVisibleValue({
                    visible: true,
                    val: undefined
                  });
                }}
              >
                上传
              </Button>
            </Space>
          </CheckAuth>
        </div>
        <Table
          columns={columns}
          dataSource={list}
          bordered={false}
          loading={loading}
          onChange={handleTableChange}
          pagination={pagination}
          rowKey="id"
          scroll={{ x: 800 }}
        />
      </div>
      {visibleValue.visible && <Upload onClose={onClose} val={visibleValue.val} />}
    </div>
  );
};

export default TagList;
