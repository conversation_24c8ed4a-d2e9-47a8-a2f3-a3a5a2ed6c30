export const initParam = {
  page: 1,
  search: [],
  size: 10,
  sorts: [{ propertyName: 'updateTime', direction: 'desc' }]
};

export const elements = ({ userList }) => {
  return [
    {
      type: 'input',
      name: ['tableSchema', 'name'],
      label: '事件名',
      operator: 'LIKE'
    },
    {
      type: 'input',
      name: 'displayName',
      label: '属性名',
      operator: 'LIKE'
    }
  ];
};

export const DataTypeOptions = [
  { text: 'STRING', value: 'STRING' },
  { text: 'LONG', value: 'LONG' },
  { text: 'INT', value: 'INT' },
  { text: 'DOUBLE', value: 'DOUBLE' },
  { text: 'TIMESTAMP', value: 'TIMESTAMP' }
];
