import { Input, InputNumber } from 'antd';
import React from 'react';
import { t } from 'utils/translation';

interface BetweenInputProps {
  value?: string;
  onChange?: (value: string) => void;
}

const BetweenInput: React.FC<BetweenInputProps> = ({ value, onChange }) => {
  console.log('🚀 ~ value:', value);
  // 解析初始值
  const [start, end] = value?.split(',').map((v) => Number(v)) || [undefined, undefined];

  // 处理值变化
  const handleChange = (index: number, newValue: number | null) => {
    const values = value?.split(',')?.map((v) => Number(v)) || [undefined, undefined];
    values[index] = newValue || undefined;

    // 只有当两个值都存在时才触发onChange
    if (onChange) {
      const result = values.filter((v) => v !== undefined).join(',');
      onChange(result);
    }
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <InputNumber
        style={{ width: '45%' }}
        value={start}
        onChange={(value) => handleChange(0, value)}
        placeholder={t('dataCenter-nrrIDoQqkco4')}
      />
      <Input
        style={{
          width: 30,
          borderLeft: 0,
          borderRight: 0,
          pointerEvents: 'none',
          background: '#fff'
        }}
        value="~"
        disabled
      />
      <InputNumber
        style={{ width: '45%' }}
        value={end}
        onChange={(value) => handleChange(1, value)}
        placeholder={t('dataCenter-PdDR442Vqgaa')}
      />
    </div>
  );
};

export { BetweenInput };
