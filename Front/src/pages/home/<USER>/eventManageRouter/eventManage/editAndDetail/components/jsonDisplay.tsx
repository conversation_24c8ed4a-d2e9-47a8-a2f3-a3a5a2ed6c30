import { ExclamationCircleFilled } from '@ant-design/icons';
import { Tooltip } from 'antd';
import { isArray, isObject } from 'lodash';
import React from 'react';

interface JsonDisplayProps {
  data: {
    [key: string]: string | number | boolean | null | undefined | object;
  };
  errors?: {
    [key: string]: string;
  };
}

const JsonDisplay: React.FC<JsonDisplayProps> = ({ data, errors = {} }) => {
  // 检查key是否存在错误，同时处理event_name和eventName的情况
  const checkHasError = (key: string) => {
    if (Object.prototype.hasOwnProperty.call(errors, key)) {
      return true;
    }
    // 特殊处理event_name和eventName的对应关系
    if (key === 'event_name' && Object.prototype.hasOwnProperty.call(errors, 'eventName')) {
      return true;
    }
    if (key === 'eventName' && Object.prototype.hasOwnProperty.call(errors, 'event_name')) {
      return true;
    }
    return false;
  };

  // 获取错误信息
  const getErrorMessage = (key: string) => {
    if (Object.prototype.hasOwnProperty.call(errors, key)) {
      return errors[key];
    }
    // 特殊处理event_name和eventName的对应关系
    if (key === 'event_name' && Object.prototype.hasOwnProperty.call(errors, 'eventName')) {
      return errors.eventName;
    }
    if (key === 'eventName' && Object.prototype.hasOwnProperty.call(errors, 'event_name')) {
      return errors.event_name;
    }
    return '';
  };

  const renderValue = (value: any, depth: number = 0, isLast: boolean = true): React.ReactNode => {
    if (value === null) {
      return <span style={{ color: '#005cc5' }}>null</span>;
    }

    if (value === undefined) {
      return <span style={{ color: '#005cc5' }}>undefined</span>;
    }

    if (isArray(value)) {
      if (value.length === 0) {
        return (
          <span style={{ color: '#24292e' }}>
            []
            {!isLast && ','}
          </span>
        );
      }

      const isSimpleArray = value.every(
        (item) =>
          item === null ||
          item === undefined ||
          typeof item !== 'object' ||
          (isObject(item) && Object.keys(item).length === 0) ||
          (isArray(item) && item.length === 0)
      );

      if (isSimpleArray) {
        return (
          <span style={{ color: '#24292e', display: 'inline-flex', alignItems: 'center' }}>
            [
            {value.map((item, index) => (
              <React.Fragment key={index}>
                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                  {isObject(item) && Object.keys(item).length === 0 ? (
                    <span>{'{}'}</span>
                  ) : (
                    renderValue(item, depth + 1, true)
                  )}
                  {index !== value.length - 1 && <span style={{ color: '#24292e' }}>, </span>}
                </span>
              </React.Fragment>
            ))}
            ]{!isLast && ','}
          </span>
        );
      }

      return (
        <>
          {'['}
          <div style={{ marginLeft: 20 }}>
            {value.map((item, index) => (
              <div key={index}>
                {renderValue(item, depth + 1, index === value.length - 1)}
                {index !== value.length - 1 && <span style={{ color: '#24292e' }}>,</span>}
              </div>
            ))}
          </div>
          <div>
            {']'}
            {!isLast && <span style={{ color: '#24292e' }}>,</span>}
          </div>
        </>
      );
    }

    if (isObject(value)) {
      if (Object.keys(value).length === 0) {
        return (
          <span style={{ color: '#24292e' }}>
            {}
            {!isLast && ','}
          </span>
        );
      }

      return (
        <>
          {'{'}
          <div style={{ marginLeft: 20 }}>
            {Object.entries(value).map(([key, val], index, arr) => {
              const hasError = checkHasError(key);
              const lineContent = (
                <div
                  key={key}
                  style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    color: hasError ? '#ff4d4f' : 'inherit',
                    cursor: hasError ? 'pointer' : 'inherit',
                    backgroundColor: hasError ? '#e9c0c1' : 'inherit',
                    margin: hasError ? '2px 0 0 -20px' : '0',
                    paddingLeft: hasError ? '20px' : '0'
                  }}
                >
                  {hasError && (
                    <ExclamationCircleFilled
                      style={{
                        marginTop: 2,
                        marginRight: 8,
                        fontSize: 14,
                        color: '#ff4d4f'
                      }}
                    />
                  )}
                  <div>
                    <span style={{ color: hasError ? '#ff4d4f' : '#24292e' }}>{`"${key}"`}</span>
                    <span style={{ color: hasError ? '#ff4d4f' : '#24292e' }}>: </span>
                    {renderValue(val, depth + 1, index === arr.length - 1)}
                  </div>
                </div>
              );

              return hasError ? (
                <Tooltip key={key} title={getErrorMessage(key)} color="red">
                  {lineContent}
                </Tooltip>
              ) : (
                lineContent
              );
            })}
          </div>
          <div>
            {'}'}
            {!isLast && <span style={{ color: '#24292e' }}>,</span>}
          </div>
        </>
      );
    }

    return (
      <span
        style={{
          color: typeof value === 'string' ? '#22863a' : '#005cc5'
        }}
      >
        {typeof value === 'string' ? `"${value}"` : value}
      </span>
    );
  };

  return (
    <div
      style={{
        minHeight: '100%',
        fontFamily: 'Monaco, monospace',
        fontSize: '14px',
        backgroundColor: '#f6f8fa',
        padding: '16px',
        borderRadius: '6px',
        whiteSpace: 'pre-wrap'
      }}
    >
      {renderValue(data)}
    </div>
  );
};

export { JsonDisplay };
