import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, DatePicker, Form, Input, Row, Select } from 'antd';
import dayjs from 'dayjs';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import DataQualityService from 'service/dataQualityService';
import EventDataService from 'service/eventDataService';
import { t } from 'utils/translation';
import { ArraySelect } from '../components/arraySelect';
import { BetweenInput } from '../components/betweenInput';
import { columnConfig, DataTypeMapping, DEFAULT_GUTTER, MAX_RULES, RuleTypeOptions } from './constants';

import {
  type CustomizedFormProps,
  type DetailedDataType,
  type EventPropertyList,
  type Step2ComponentRef,
  type Step2FormValue
} from './types';

const { Option } = Select;

export const Step2Component = forwardRef<Step2ComponentRef, CustomizedFormProps>(
  ({ formValue, setRemoveTempList, eventValue }, ref) => {
    const [step2Form] = Form.useForm<Step2FormValue>();
    const [eventPropertyList, setEventPropertyList] = useState<
      { displayName: string; tableSchemaName: string; dataType: string }[]
    >([]);
    const [formValueTemp, setFormValueTemp] = useState<Step2FormValue>({
      rules: []
    });

    useEffect(() => {
      const fetchData = async () => {
        const [qualityRes, propertyRes] = await Promise.all([
          DataQualityService.listBy([
            {
              operator: 'EQ',
              propertyName: 'eventId',
              value: eventValue?.id
            }
          ]),
          EventDataService.getPublicEventProperty(eventValue?.id) as Promise<EventPropertyList[]>
        ]);

        const mappedPropertyList = propertyRes.map((n) => ({
          displayName: n.displayName,
          tableSchemaName: n.tableSchema.name,
          dataType: n.tableSchema.dataType
        }));

        setEventPropertyList(mappedPropertyList);

        const formData = {
          rules: qualityRes.map((n: any) => ({
            tableSchemaName: `${n.displayName}[${n.tableSchemaName}]`,
            ruleType: n.ruleType,
            dataType: n?.dataType || 'STRING',
            ruleTypeValue:
              n?.dataType === 'TIMESTAMP'
                ? n.ruleType === 'INCLUDE_CHECK'
                  ? dayjs(parseInt(n.ruleTypeValue))
                  : n.ruleType === 'INTERVAL_CHECK'
                    ? n.ruleTypeValue.split(',').map((n: any) => dayjs(parseInt(n)))
                    : n.ruleTypeValue
                : n.ruleTypeValue,
            id: n?.id
          }))
        };

        step2Form.setFieldsValue(formData);
      };

      fetchData();
    }, [eventValue?.id]);

    useImperativeHandle(ref, () => ({
      getFieldsValue: () => step2Form.getFieldsValue(),
      validateFields: () => step2Form.validateFields()
    }));

    const renderValueInput = useCallback(
      (field: any, fieldKey: number) => {
        const current = step2Form.getFieldValue(['rules', fieldKey]) as Step2FormValue['rules'][number];

        if (current?.ruleType === 'ENUM_CHECK') {
          return <ArraySelect placeholder={t('dataCenter-Bci51gU1snBp')} allowClear />;
        } else if (current?.ruleType === 'INTERVAL_CHECK') {
          if (current.dataType === 'TIMESTAMP') {
            return <DatePicker.RangePicker showTime style={{ width: '100%' }} />;
          }
          return <BetweenInput />;
        } else if (current?.ruleType === 'MUST_PASS_CHECK') {
          return <span />;
        } else if (current?.ruleType === 'INCLUDE_CHECK' && current?.dataType === 'TIMESTAMP') {
          return <DatePicker showTime style={{ width: '100%' }} />;
        }
        return <Input placeholder={t('dataCenter-u674KVpefegX')} />;
      },
      [JSON.stringify(formValueTemp)]
    );

    const handlePropertyChange = (value: string, index: number) => {
      const allOptions = [
        // { displayName: '事件名', tableSchemaName: 'eventName', dataType: 'STRING' },
        ...(formValue?.specialPropertyMappingList || []),
        ...eventPropertyList
      ];
      const [displayName] = value.split('[');
      const option = allOptions.find((n) => n.displayName === displayName);
      step2Form.setFieldValue(['rules', index, 'ruleTypeValue'], undefined);
      step2Form.setFieldValue(['rules', index, 'ruleType'], undefined);
      step2Form.setFieldValue(['rules', index, 'dataType'], option?.dataType);
    };

    /**
     * 清空校验规则值 避免类型不匹配
     * @param index
     */
    const handleRuleTypeChange = (index: number) => {
      step2Form.setFieldValue(['rules', index, 'ruleTypeValue'], undefined);
    };

    const handleRemove = (index: number, remove: (name: number) => void) => {
      const formValue = step2Form.getFieldsValue();
      setRemoveTempList((prev) => [...prev, formValue.rules[index]]);
      remove(index);
    };

    return (
      <div className="w-full">
        <Form layout="vertical" form={step2Form} onValuesChange={(_, allValues) => setFormValueTemp(allValues)}>
          <Row className="mb-2.5" gutter={{ xl: DEFAULT_GUTTER, lg: DEFAULT_GUTTER, md: DEFAULT_GUTTER }}>
            <Col {...columnConfig.property}>{t('dataCenter-BSHmepoDbH1A')}</Col>
            <Col {...columnConfig.rule}>{t('dataCenter-6QcJccfkC7Ai')}</Col>
            <Col {...columnConfig.condition}>{t('dataCenter-0VBooEpgdKnu')}</Col>
            <Col {...columnConfig.action}>{t('dataCenter-QzmIdtrEvLbx')}</Col>
          </Row>

          <Form.List name="rules">
            {(fields, { add, remove }) => (
              <>
                {fields.map((field, index) => (
                  <Row
                    className="mb-4"
                    gutter={{ xl: DEFAULT_GUTTER, lg: DEFAULT_GUTTER, md: DEFAULT_GUTTER }}
                    key={field?.key}
                  >
                    <Form.Item {...field} name={[field.name, 'id']} hidden>
                      <Input />
                    </Form.Item>
                    <Form.Item {...field} name={[field.name, 'dataType']} hidden>
                      <Input />
                    </Form.Item>
                    <Col {...columnConfig.property}>
                      <Form.Item
                        {...field}
                        name={[field.name, 'tableSchemaName']}
                        fieldKey={[field.fieldKey!, 'tableSchemaName']}
                        rules={[{ required: true, message: t('dataCenter-2QBNsHUiUFVK') }]}
                      >
                        <Select
                          allowClear
                          showSearch
                          optionFilterProp="children"
                          placeholder={t('dataCenter-QDhMl3stjEvR')}
                          onChange={(value) => handlePropertyChange(value, index)}
                        >
                          {/* <Option value="事件名[eventName]">事件名[eventName]</Option> */}
                          {formValue?.specialPropertyMappingList?.map((n) => (
                            <Option key={n?.index} value={`${n.displayName}[${n.propertySchema}]`}>
                              {`${n.displayName}[${n.propertySchema}]`}
                            </Option>
                          ))}
                          {eventPropertyList.map((n) => (
                            <Option key={n.tableSchemaName} value={`${n.displayName}[${n.tableSchemaName}]`}>
                              {`${n.displayName}[${n.tableSchemaName}]`}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col {...columnConfig.rule}>
                      <Form.Item
                        {...field}
                        name={[field.name, 'ruleType']}
                        fieldKey={[field.fieldKey!, 'ruleType']}
                        rules={[{ required: true, message: t('dataCenter-eThoQhMqcFgx') }]}
                      >
                        <Select
                          allowClear
                          showSearch
                          optionFilterProp="children"
                          placeholder={t('dataCenter-QDhMl3stjEvR')}
                          onChange={() => handleRuleTypeChange(index)}
                          options={
                            RuleTypeOptions[
                              DataTypeMapping[
                                (step2Form.getFieldValue(['rules', index, 'dataType']) as DetailedDataType) || 'STRING'
                              ]
                            ]
                          }
                        />
                      </Form.Item>
                    </Col>
                    <Col {...columnConfig.condition}>
                      <Form.Item
                        {...field}
                        name={[field.name, 'ruleTypeValue']}
                        // fieldKey={[field.fieldKey!, 'ruleTypeValue']}
                        // dependencies={[['rules', field.name, 'ruleType']]}
                        rules={[
                          {
                            required: step2Form.getFieldValue(['rules', field.name, 'ruleType']) !== 'MUST_PASS_CHECK',
                            message: t('dataCenter-u674KVpefegX')
                          },
                          {
                            validator: (__, value, callback) => {
                              const isTimestamp =
                                step2Form.getFieldValue(['rules', field.name, 'dataType']) === 'TIMESTAMP';

                              const isEnum =
                                step2Form.getFieldValue(['rules', field.name, 'ruleType']) === 'ENUM_CHECK';
                              if (isEnum && value === '[]') {
                                callback(t('dataCenter-u674KVpefegX'));
                              } else if (!isTimestamp && value && value.includes(' ')) {
                                callback(t('dataCenter-H46uvxHczFEX'));
                              }
                              callback();
                            }
                          }
                        ]}
                      >
                        {renderValueInput(field, index)}
                      </Form.Item>
                    </Col>
                    <Col {...columnConfig.action}>
                      <MinusCircleOutlined className="mt-2.5" onClick={() => handleRemove(field.name, remove)} />
                    </Col>
                  </Row>
                ))}
                <Form.Item>
                  <div className="flex items-center gap-10">
                    <Button
                      type="dashed"
                      disabled={fields.length >= MAX_RULES}
                      onClick={() => fields.length < MAX_RULES && add()}
                      icon={<PlusOutlined />}
                    >
                      {t('dataCenter-bm0pH5HvAU1V')}
                    </Button>
                    <span>{` [${fields.length}/${MAX_RULES}] ${t('dataCenter-aPqE3yJn0pFo')} ${MAX_RULES} ${t('dataCenter-NbrQGpopzzZ1')}`}</span>
                  </div>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      </div>
    );
  }
);
