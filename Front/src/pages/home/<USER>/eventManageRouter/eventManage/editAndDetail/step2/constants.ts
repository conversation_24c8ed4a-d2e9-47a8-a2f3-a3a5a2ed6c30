/** 规则相关常量 */
import { t } from 'utils/translation';
import { BaseDataType, DetailedDataType } from './types';

export const MAX_RULES = 50;
export const DEFAULT_GUTTER = 30;

/** 列配置 */
export const columnConfig = {
  property: { xl: 8, lg: 8, md: 12, sm: 24 },
  rule: { xl: 4, lg: 4, md: 12, sm: 24 },
  condition: { xl: 10, lg: 10, md: 12, sm: 24 },
  action: { xl: 2, lg: 2, md: 12, sm: 24 }
} as const;

/** 数据类型分组映射 */
export const DataTypeMapping: Record<DetailedDataType, BaseDataType> = {
  STRING: 'STRING',
  LONG: 'NUMBER',
  INT: 'NUMBER',
  DOUBLE: 'NUMBER',
  BOOL: 'BOOL',
  TIMESTAMP: 'TIMESTAMP',
  DATE: 'TIMESTAMP',
  DATETIME: 'TIMESTAMP'
};

/** 规则类型选项 */
export const RuleTypeOptions: Record<BaseDataType, { label: string; value: string }[]> = {
  STRING: [
    { label: t('dataCenter-nTeoTZvenhGf'), value: 'MUST_PASS_CHECK' },
    { label: t('dataCenter-8VFzkAyCXknQ'), value: 'ENUM_CHECK' },
    { label: t('dataCenter-tfBQjCcKTHLM'), value: 'REGULAR_MATCHING_CHECK' },
    { label: t('dataCenter-Q0nEpNrWJMc4'), value: 'INCLUDE_CHECK' }
  ],
  NUMBER: [
    { label: t('dataCenter-nTeoTZvenhGf'), value: 'MUST_PASS_CHECK' },
    { label: t('dataCenter-8VFzkAyCXknQ'), value: 'ENUM_CHECK' },
    { label: t('dataCenter-tfBQjCcKTHLM'), value: 'REGULAR_MATCHING_CHECK' },
    { label: t('dataCenter-j3wH8pQ9QCDh'), value: 'INTERVAL_CHECK' },
    { label: t('dataCenter-Q0nEpNrWJMc4'), value: 'INCLUDE_CHECK' }
  ],
  TIMESTAMP: [
    { label: t('dataCenter-nTeoTZvenhGf'), value: 'MUST_PASS_CHECK' },
    { label: t('dataCenter-tfBQjCcKTHLM'), value: 'REGULAR_MATCHING_CHECK' },
    { label: t('dataCenter-j3wH8pQ9QCDh'), value: 'INTERVAL_CHECK' },
    { label: t('dataCenter-Q0nEpNrWJMc4'), value: 'INCLUDE_CHECK' }
  ],
  BOOL: [
    { label: t('dataCenter-nTeoTZvenhGf'), value: 'MUST_PASS_CHECK' },
    { label: t('dataCenter-8VFzkAyCXknQ'), value: 'ENUM_CHECK' },
    { label: t('dataCenter-Q0nEpNrWJMc4'), value: 'INCLUDE_CHECK' }
  ]
};
