import { Select } from 'antd';
import type { SelectProps } from 'antd/lib/select';
import React from 'react';

interface ArraySelectProps extends Omit<SelectProps, 'value' | 'onChange'> {
  value?: string;
  onChange?: (value: string) => void;
}

export const ArraySelect: React.FC<ArraySelectProps> = ({ value, onChange, ...props }) => {
  // 将字符串形式的数组转换为实际的数组
  const parseValue = (val?: string) => {
    if (!val) return undefined;
    try {
      return JSON.parse(val);
    } catch (e) {
      return undefined;
    }
  };

  // 处理值变化
  const handleChange = (newValue: any) => {
    if (onChange) {
      // 将数组转换为字符串形式
      onChange(JSON.stringify(newValue));
    }
  };

  return (
    <Select
      {...props}
      mode="tags"
      value={parseValue(value)}
      onChange={handleChange}
      style={{ width: '100%', ...props.style }}
    />
  );
};
