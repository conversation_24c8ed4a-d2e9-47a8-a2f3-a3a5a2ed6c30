import { type Ref } from 'react';

/** 基础类型定义 */
export type RuleType = 'MUST_PASS_CHECK' | 'ENUM_CHECK' | 'REGULAR_MATCHING_CHECK' | 'INTERVAL_CHECK' | 'INCLUDE_CHECK';
export type DataType = 'TIMESTAMP' | 'STRING' | 'NUMBER' | 'BOOLEAN';
export type BaseDataType = 'STRING' | 'NUMBER' | 'TIMESTAMP' | 'BOOL';
export type DetailedDataType = 'STRING' | 'LONG' | 'INT' | 'DOUBLE' | 'BOOL' | 'TIMESTAMP' | 'DATE' | 'DATETIME';

/** 规则接口定义 */
export interface Rule {
  tableSchemaName: string;
  ruleType: RuleType;
  ruleTypeValue: string;
  dataType: DataType;
  id?: string;
}

/** 组件引用接口 */
export interface Step2ComponentRef {
  validateFields: () => Promise<any>;
  getFieldsValue: () => Step2FormValue;
}

/** 组件属性接口 */
export interface CustomizedFormProps {
  formValue: {
    specialPropertyMappingList: {
      dataType: DataType;
      displayName: string;
      index: number;
      propertySchema: string;
    }[];
  };
  setRemoveTempList: (updater: (prev: Rule[]) => Rule[]) => void;
  ref: Ref<Step2ComponentRef>;
  eventValue: {
    id: string;
  };
}

/** 表单值接口 */
export interface Step2FormValue {
  rules: Rule[];
}

/** 事件属性列表接口 */
export interface EventPropertyList {
  displayName: string;
  tableSchema: {
    name: string;
    dataType: string;
  };
}
