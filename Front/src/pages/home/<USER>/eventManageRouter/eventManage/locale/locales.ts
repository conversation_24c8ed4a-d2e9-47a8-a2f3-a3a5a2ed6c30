export default {
  cn: {
    'dataCenter-icOfcDFeCdEj': '近14天无数据',
    'dataCenter-YoCAfaV6a5tX': '近14天有数据',
    'dataCenter-QCIhbOdlO82a': '事件名称',
    'dataCenter-Th4DArtyPFVC': '事件显示名',
    'dataCenter-EdkVaorvrK61': '事件状态',
    'dataCenter-WD5tvTmw2ThP': '是否校验',
    'dataCenter-OeUvh6KLqLD9': '是',
    'dataCenter-TcusgZ5gdD58': '否',
    'dataCenter-l1UNWDrWdw6N': '更新者',
    'dataCenter-VOn0TnedV6tp': '更新时间',
    'dataCenter-axygwxY42s6e': '共',
    'dataCenter-k9sRGFrrFjPz': '条',
    'dataCenter-PMxxknv2e6Kv': '未监测到数据',
    'dataCenter-5EAqc4VRcK1Y': '今日数据有异常！',
    'dataCenter-S4qQBhWq6Axt': '1，近14天无数据：从查询日往前推14天无数据。',
    'dataCenter-jbofXj1ZrV': '2，近14天有数据：从查询日往前推14天有数据。',
    'dataCenter-BTxifOWyOw': '当前时间所在日期也记1天',
    'dataCenter-s1OrgZ1W5fnw': '近7天事件发生次数',
    'dataCenter-mlM6u0q1rs2D': '更新者',
    'dataCenter-Pvu2EOQbWXZm': '更新时间',
    'dataCenter-KoLPo6jgr214': '是否质量校验',
    'dataCenter-nHL1eiEPevYG': '操作',
    'dataCenter-jgTBEIa4CSIL': '编辑',
    'dataCenter-8rjWbZnxHw3j': '校验明细',
    'dataCenter-Pjyn5emDp9vp': '详情',
    'dataCenter-9DMJMA4YacK1': '删除',
    'dataCenter-UQBm5LaCuC6A': '更多',
    'dataCenter-oQQHVvW8ks9P': '事件定义',
    'dataCenter-OxKS2CwN1akp': '通过新建事件绑定eventName即为事件定义',
    'dataCenter-1tbLJMithBeX': '事件',
    'dataCenter-9ynbRgnJEUzn': '已定义的事件个数',
    'dataCenter-qA4Qn7twqS6M': '事件属性',
    'dataCenter-InTyPSwXEbd2': '已定义事件的所有属性之和',
    'dataCenter-O7BodJSzXufX': '事件统计',
    'dataCenter-SmyKsR2FoRXR': '近7日所有事件发生次数',
    'dataCenter-YLuVqxDC9Tn1': '近7日未定义和已定义的事件发生次数之和',
    'dataCenter-r8esZRWfwQny': '近7日已定义事件发生次数',
    'dataCenter-s4CCSjQpYzek': '近7日已定义事件发生次数总和',
    'dataCenter-rOxJjNCPM8XO': '近14天已定义事件有数据个数',
    'dataCenter-UBGbqks6h0vq': '已定义事件中，近14天有数据的事件个数',
    'dataCenter-6YFImYS0dUBg': '近14天已定义事件有数据占比',
    'dataCenter-GraR4foEZJCf': '已定义事件中近14天有数据的事件个数/所有已定义事件的个数',
    'dataCenter-QSMdKGMDNZeR': '近14天已定义事件无数据个数',
    'dataCenter-RxcicOF3EQMc': '已定义事件中，近14天无数据的事件个数',
    'dataCenter-DWw3LdLy8Ep8': '近14天已定义事件无数据占比',
    'dataCenter-JsAdmR9Xa9ID': '已定义事件中近14天无数据的事件个数/所有已定义事件的个数',
    'dataCenter-L2uKm2IpsBdw': '事件列表',
    'dataCenter-Nwnp8d41rrlO': '新建事件',
    'dataCenter-GaizkhUJaJMd': '数量',
    'dataCenter-R6P1ky3EktLx': '必填校验',
    'dataCenter-NokzvimSN2bj': '枚举校验',
    'dataCenter-cvZTbYtEFoyu': '正则校验',
    'dataCenter-yCuxKyWwugxP': '区间校验',
    'dataCenter-i7EwUYxRLFvP': '包含校验',
    'dataCenter-MlsRrZUcJQIh': '校验明细',
    'dataCenter-NqL5gAO2eVaC': '取消',
    'dataCenter-X4Zc514Zu38U': '确定',
    'dataCenter-tWYOUkCSYGkf': '基础信息',
    'dataCenter-apHfnf9NRoIh': '事件名称: ',
    'dataCenter-5hdJ705Z9M0R': '事件显示名: ',
    'dataCenter-wK6fvYx2pOVv': '异常事件趋势',
    'dataCenter-pEoXl1LaWsNg': '表示事件量已超过校验阈值，数据为抽样数据',
    'dataCenter-kNCqty0c8HOk': '异常详情',
    'dataCenter-3YN8JHntyXlL': '属性: ',
    'dataCenter-GkNu0Qdl3vm0': '全部',
    'dataCenter-y4sZ5JN996CF': '校验规则: ',
    'dataCenter-U5nFSjC04x4h': '全部',
    'dataCenter-1i6eT1w30ZE4': '异常条数: ',
    'dataCenter-JYT5sGY55Nmi': '下载日志',
    'dataCenter-mzK9LzE8SudR': '原始日志',
    'dataCenter-6WSKP9PggLAR': '日志时间: ',
    'dataCenter-1t2BX8i7uR3L': '密码错误',
    'dataCenter-1Auzuu1bI4C6': '删除成功',
    'dataCenter-n7K8DpO4qfch': '确认要删除该事件吗？',
    'dataCenter-lH2ruEsHc2nK': '删除',
    'dataCenter-aQ7MgPj0PCdA': '取消',
    'dataCenter-XZ1xrLYJ2FJj': '你将删除事件：',
    'dataCenter-7w4MsPFSObeD': '删除后，所有应用该事件的配置将失效',
    'dataCenter-3zlq7LRLKvvO': '请输入登陆密码',
    'dataCenter-nrrIDoQqkco4': '最小值',
    'dataCenter-PdDR442Vqgaa': '最大值',
    'dataCenter-nTeoTZvenhGf': '必传校验',
    'dataCenter-8VFzkAyCXknQ': '枚举校验',
    'dataCenter-tfBQjCcKTHLM': '正则匹配校验',
    'dataCenter-Q0nEpNrWJMc4': '包含校验',
    'dataCenter-j3wH8pQ9QCDh': '区间校验',
    'dataCenter-Bci51gU1snBp': '请输入',
    'dataCenter-u674KVpefegX': '请输入条件',
    'dataCenter-R2pFDpEJoMJ4': '事件属性',
    'dataCenter-oB9c3W5zPWo0': '校验规则',
    'dataCenter-Bpdc1hhiRxmh': '输入条件',
    'dataCenter-hVZxJlkFx62Y': '操作',
    'dataCenter-2QBNsHUiUFVK': '请填写属性字段',
    'dataCenter-QDhMl3stjEvR': '请选择',
    'dataCenter-eThoQhMqcFgx': '请填写校验规则',
    'dataCenter-bm0pH5HvAU1V': '添加一行配置',
    'dataCenter-H46uvxHczFEX': '不能有空格',
    'dataCenter-aPqE3yJn0pFo': '最多添加',
    'dataCenter-NbrQGpopzzZ1': '条',
    'dataCenter-UFSH3PKEbFqi': '日期',
    'dataCenter-HCCohekvzvu6': '发生次数',
    'dataCenter-dWaoSLeNliv7': '事件详情',
    'dataCenter-F0mCGNetdDkp': '编辑',
    'dataCenter-dEhFWNvIb6s4': '编辑事件',
    'dataCenter-IT26V5N7NTg2': '创建事件',
    'dataCenter-97ZHlG7JiDuV': '配置质量校验',
    'dataCenter-hm8RXLkeOrrk': '保存成功',
    'dataCenter-1vZ93XSHz69H': '事件显示名',
    'dataCenter-MLTfxXMDw4Tc': '请输入事件显示名',
    'dataCenter-7FGr6bakjbwa': '最大长度限制为256位字符',
    'dataCenter-p61OqFhLKHhC': '不能输入空格',
    'dataCenter-IibRCqjgL3tr': '事件显示名已存在',
    'dataCenter-wGWiyBhsZ9kO': '请输入',
    'dataCenter-mUKWFe1h89kU': '备注',
    'dataCenter-l18jIcykeP82': '最大长度限制为64位字符',
    'dataCenter-KucfgNBoVhg2': '请填写备注',
    'dataCenter-nG3bx271rDrr': '事件条件',
    'dataCenter-Dy0WSSotQqGj': '请选择eventName业务含义的字段',
    'dataCenter-J4HPOyl1cx5S': 'eventName必填',
    'dataCenter-U6uaJhXUGgUu': '请输入',
    'dataCenter-xpvkCo5pt7rS': '事件属性',
    'dataCenter-uXBGJCLm6aGS': '事件属性字段',
    'dataCenter-laEGk2TbjPgl': '事件属性显示名',
    'dataCenter-FxfZ0IoyUP5l': '数据类型',
    'dataCenter-gg6f2Oyr2ZtZ': '操作',
    'dataCenter-X3yw2zWT9mxO': '请填写属性字段',
    'dataCenter-4XwKZRNszsGn': '请填写字段显示名',
    'dataCenter-SZVFb5iza8Ya': '请选择数据类型',
    'dataCenter-FEA6w7KR0Cwa': '添加一行配置',
    'dataCenter-MXcUftL4MQzQ': '最多添加50条',
    'dataCenter-NRla6hiIoN2u': '属性名称',
    'dataCenter-otbXn6J1pkJ0': '事件属性字段名',
    'dataCenter-lwoF84Il7IRF': '通用事件属性',
    'dataCenter-9lxGlOvlgBT4': '事件统计（近7天事件发生次数）',
    'dataCenter-nXjU8SaRS4Xu': '取消',
    'dataCenter-haZAklzwoMOh': '配置质量校验',
    'dataCenter-s8Zcb3kUgeMX': '上一步',
    'dataCenter-nL5OY5Y2xaDZ': '保存成功',
    'dataCenter-VMBcIztdmbHC': '确定',
    'dataCenter-s0EEVGoMIcqm': 'eventName 等于',
    'dataCenter-BSHmepoDbH1A': '事件属性',
    'dataCenter-6QcJccfkC7Ai': '校验规则',
    'dataCenter-0VBooEpgdKnu': '输入条件',
    'dataCenter-QzmIdtrEvLbx': '操作'
  },
  en: {
    'dataCenter-icOfcDFeCdEj': 'Near 14 days no data',
    'dataCenter-YoCAfaV6a5tX': 'Near 14 days have data',
    'dataCenter-QCIhbOdlO82a': 'Event name',
    'dataCenter-Th4DArtyPFVC': 'Event display name',
    'dataCenter-EdkVaorvrK61': 'Event status',
    'dataCenter-WD5tvTmw2ThP': 'Is quality check',
    'dataCenter-OeUvh6KLqLD9': 'Yes',
    'dataCenter-TcusgZ5gdD58': 'No',
    'dataCenter-l1UNWDrWdw6N': 'Updater',
    'dataCenter-VOn0TnedV6tp': 'Update time',
    'dataCenter-axygwxY42s6e': 'Total',
    'dataCenter-k9sRGFrrFjPz': 'items',
    'dataCenter-PMxxknv2e6Kv': 'No data',
    'dataCenter-5EAqc4VRcK1Y': "Today's data is abnormal!",
    'dataCenter-S4qQBhWq6Axt': '1, Near 14 days no data: 14 days before the query date without data.',
    'dataCenter-jbofXj1ZrV': '2, Near 14 days have data: 14 days before the query date with data.',
    'dataCenter-BTxifOWyOw': 'The current date is also counted as 1 day',
    'dataCenter-s1OrgZ1W5fnw': 'Number of events occurred in the last 7 days',
    'dataCenter-mlM6u0q1rs2D': 'Updater',
    'dataCenter-Pvu2EOQbWXZm': 'Update time',
    'dataCenter-KoLPo6jgr214': 'Is quality check',
    'dataCenter-nHL1eiEPevYG': 'Operation',
    'dataCenter-jgTBEIa4CSIL': 'Edit',
    'dataCenter-8rjWbZnxHw3j': 'Check details',
    'dataCenter-Pjyn5emDp9vp': 'Detail',
    'dataCenter-9DMJMA4YacK1': 'Delete',
    'dataCenter-UQBm5LaCuC6A': 'More',
    'dataCenter-oQQHVvW8ks9P': 'Event definition',
    'dataCenter-OxKS2CwN1akp': 'By creating a new event and binding eventName is the event definition',
    'dataCenter-1tbLJMithBeX': 'Event',
    'dataCenter-9ynbRgnJEUzn': 'Number of defined events',
    'dataCenter-qA4Qn7twqS6M': 'Event properties',
    'dataCenter-InTyPSwXEbd2': 'Sum of all event properties',
    'dataCenter-O7BodJSzXufX': 'Event statistics',
    'dataCenter-SmyKsR2FoRXR': 'Total number of events in the last 7 days',
    'dataCenter-YLuVqxDC9Tn1': 'Total number of events in the last 7 days',
    'dataCenter-r8esZRWfwQny': 'Number of defined events in the last 7 days',
    'dataCenter-s4CCSjQpYzek': 'Total number of defined events in the last 7 days',
    'dataCenter-rOxJjNCPM8XO': 'Number of defined events with data in the last 14 days',
    'dataCenter-UBGbqks6h0vq': 'Number of defined events with data in the last 14 days',
    'dataCenter-6YFImYS0dUBg': 'Percentage of defined events with data in the last 14 days',
    'dataCenter-GraR4foEZJCf': 'Number of defined events with data in the last 14 days/Total number of defined events',
    'dataCenter-QSMdKGMDNZeR': 'Number of defined events without data in the last 14 days',
    'dataCenter-RxcicOF3EQMc': 'Number of defined events without data in the last 14 days',
    'dataCenter-DWw3LdLy8Ep8': 'Percentage of defined events without data in the last 14 days',
    'dataCenter-JsAdmR9Xa9ID': 'Number of defined events without data in the last 14 days/Total number of defined events',
    'dataCenter-L2uKm2IpsBdw': 'Event list',
    'dataCenter-Nwnp8d41rrlO': 'New event',
    'dataCenter-GaizkhUJaJMd': 'Number',
    'dataCenter-R6P1ky3EktLx': 'Must pass check',
    'dataCenter-NokzvimSN2bj': 'Enum check',
    'dataCenter-cvZTbYtEFoyu': 'Regular matching check',
    'dataCenter-yCuxKyWwugxP': 'Interval check',
    'dataCenter-i7EwUYxRLFvP': 'Include check',
    'dataCenter-MlsRrZUcJQIh': 'check details',
    'dataCenter-NqL5gAO2eVaC': 'Cancel',
    'dataCenter-X4Zc514Zu38U': 'Confirm',
    'dataCenter-tWYOUkCSYGkf': 'Basic Information',
    'dataCenter-apHfnf9NRoIh': 'Event name: ',
    'dataCenter-5hdJ705Z9M0R': 'Event display name: ',
    'dataCenter-wK6fvYx2pOVv': 'Abnormal event trend',
    'dataCenter-pEoXl1LaWsNg': 'Indicates that the event volume has exceeded the check threshold, and the data is sampled data',
    'dataCenter-kNCqty0c8HOk': 'Abnormal details',
    'dataCenter-3YN8JHntyXlL': 'Property: ',
    'dataCenter-GkNu0Qdl3vm0': 'All',
    'dataCenter-y4sZ5JN996CF': 'Check rule: ',
    'dataCenter-U5nFSjC04x4h': 'All',
    'dataCenter-1i6eT1w30ZE4': 'Abnormal items: ',
    'dataCenter-JYT5sGY55Nmi': 'Download log',
    'dataCenter-mzK9LzE8SudR': 'Raw log',
    'dataCenter-6WSKP9PggLAR': 'Log time: ',
    'dataCenter-1t2BX8i7uR3L': 'Password error',
    'dataCenter-1Auzuu1bI4C6': 'Delete successfully',
    'dataCenter-n7K8DpO4qfch': 'Are you sure you want to delete this event?',
    'dataCenter-lH2ruEsHc2nK': 'Delete',
    'dataCenter-aQ7MgPj0PCdA': 'Cancel',
    'dataCenter-XZ1xrLYJ2FJj': 'You will delete event: ',
    'dataCenter-7w4MsPFSObeD': 'After deletion, all configurations that use this event will be invalid',
    'dataCenter-3zlq7LRLKvvO': 'Please enter login password',
    'dataCenter-nrrIDoQqkco4': 'Minimum value',
    'dataCenter-PdDR442Vqgaa': 'Maximum value',
    'dataCenter-nTeoTZvenhGf': 'Must pass check',
    'dataCenter-8VFzkAyCXknQ': 'Enum check',
    'dataCenter-tfBQjCcKTHLM': 'Regular matching check',
    'dataCenter-Q0nEpNrWJMc4': 'Include check',
    'dataCenter-j3wH8pQ9QCDh': 'Interval check',
    'dataCenter-Bci51gU1snBp': 'Please enter',
    'dataCenter-u674KVpefegX': 'Please enter condition',
    'dataCenter-R2pFDpEJoMJ4': 'Event properties',
    'dataCenter-oB9c3W5zPWo0': 'Check rule',
    'dataCenter-Bpdc1hhiRxmh': 'Input condition',
    'dataCenter-hVZxJlkFx62Y': 'Operation',
    'dataCenter-2QBNsHUiUFVK': 'Please fill in the attribute field',
    'dataCenter-QDhMl3stjEvR': 'Please select',
    'dataCenter-eThoQhMqcFgx': 'Please fill in the check rule',
    'dataCenter-bm0pH5HvAU1V': 'Add a row configuration',
    'dataCenter-H46uvxHczFEX': 'No spaces allowed',
    'dataCenter-aPqE3yJn0pFo': 'Add up to',
    'dataCenter-NbrQGpopzzZ1': 'items',
    'dataCenter-UFSH3PKEbFqi': 'Date',
    'dataCenter-HCCohekvzvu6': 'Occurrence',
    'dataCenter-dWaoSLeNliv7': 'Event details',
    'dataCenter-F0mCGNetdDkp': 'Edit',
    'dataCenter-dEhFWNvIb6s4': 'Edit event',
    'dataCenter-IT26V5N7NTg2': 'Create event',
    'dataCenter-97ZHlG7JiDuV': 'Configure quality check',
    'dataCenter-hm8RXLkeOrrk': 'Save successfully',
    'dataCenter-1vZ93XSHz69H': 'Event display name',
    'dataCenter-MLTfxXMDw4Tc': 'Please enter event display name',
    'dataCenter-7FGr6bakjbwa': 'The maximum length is 256 characters',
    'dataCenter-p61OqFhLKHhC': 'No spaces allowed',
    'dataCenter-IibRCqjgL3tr': 'Event display name already exists',
    'dataCenter-wGWiyBhsZ9kO': 'Please enter',
    'dataCenter-mUKWFe1h89kU': 'Note',
    'dataCenter-l18jIcykeP82': 'The maximum length is 64 characters',
    'dataCenter-KucfgNBoVhg2': 'Please fill in the note',
    'dataCenter-nG3bx271rDrr': 'Event condition',
    'dataCenter-Dy0WSSotQqGj': 'Please select the field of eventName business meaning',
    'dataCenter-J4HPOyl1cx5S': 'eventName is required',
    'dataCenter-U6uaJhXUGgUu': 'Please enter',
    'dataCenter-xpvkCo5pt7rS': 'Event properties',
    'dataCenter-uXBGJCLm6aGS': 'Event property field',
    'dataCenter-laEGk2TbjPgl': 'Event property display name',
    'dataCenter-FxfZ0IoyUP5l': 'Data type',
    'dataCenter-gg6f2Oyr2ZtZ': 'Operation',
    'dataCenter-X3yw2zWT9mxO': 'Please fill in the attribute field',
    'dataCenter-4XwKZRNszsGn': 'Please fill in the field display name',
    'dataCenter-SZVFb5iza8Ya': 'Please select data type',
    'dataCenter-FEA6w7KR0Cwa': 'Add a row configuration',
    'dataCenter-MXcUftL4MQzQ': 'Add up to 50 rows',
    'dataCenter-NRla6hiIoN2u': 'Property name',
    'dataCenter-otbXn6J1pkJ0': 'Event property field name',
    'dataCenter-lwoF84Il7IRF': 'Common event properties',
    'dataCenter-9lxGlOvlgBT4': 'Event statistics (number of events in the last 7 days)',
    'dataCenter-nXjU8SaRS4Xu': 'Cancel',
    'dataCenter-haZAklzwoMOh': 'Configure quality check',
    'dataCenter-s8Zcb3kUgeMX': 'Previous step',
    'dataCenter-nL5OY5Y2xaDZ': 'Save successfully',
    'dataCenter-VMBcIztdmbHC': 'Confirm',
    'dataCenter-s0EEVGoMIcqm': 'eventName equals',
    'dataCenter-BSHmepoDbH1A': 'Event properties',
    'dataCenter-6QcJccfkC7Ai': 'Check rule',
    'dataCenter-0VBooEpgdKnu': 'Input condition',
    'dataCenter-QzmIdtrEvLbx': 'Operation'
  }
};
