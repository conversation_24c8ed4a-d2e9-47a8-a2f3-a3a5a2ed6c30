import { t } from 'utils/translation';

import { RuleType } from './types';

export const DEFAULT_PAGE_SIZE = 20;

export const INITIAL_DATE_RANGE = {
  START_DAYS: 6,
  FORMAT: 'YYYY/MM/DD'
};

export const DRAWER_WIDTH = 1000;

export const RULE_TYPE_OPTIONS = [
  { label: t('dataCenter-R6P1ky3EktLx'), value: 'MUST_PASS_CHECK' },
  { label: t('dataCenter-NokzvimSN2bj'), value: 'ENUM_CHECK' },
  { label: t('dataCenter-cvZTbYtEFoyu'), value: 'REGULAR_MATCHING_CHECK' },
  { label: t('dataCenter-yCuxKyWwugxP'), value: 'INTERVAL_CHECK' },
  { label: t('dataCenter-i7EwUYxRLFvP'), value: 'INCLUDE_CHECK' }
] as const;

export const INITIAL_STATE = {
  loading: false,
  currentRule: undefined,
  currentCheckRules: '' as RuleType,
  currentField: undefined,
  rulesMap: {},
  rules: [],
  fields: [],
  totalFieldsCount: 0
};
