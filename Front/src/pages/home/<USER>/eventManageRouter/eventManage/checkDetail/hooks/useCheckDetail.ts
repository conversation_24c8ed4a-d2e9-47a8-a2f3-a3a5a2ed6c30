import dayjs, { Dayjs } from 'dayjs';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import DataQualityService from 'service/dataQualityService';
import { DEFAULT_PAGE_SIZE } from '../constants';
import { Field, PaginationState, Rule, RuleType } from '../types';

export const useCheckDetail = (eventId: number) => {
  const [loading, setLoading] = useState(false);
  const [dataValue, setDataValue] = useState<[Dayjs, Dayjs]>([dayjs().subtract(6, 'day'), dayjs()]);
  const [currentRule, setCurrentRule] = useState<Rule | undefined>(undefined);
  const [currentCheckRules, setCurrentCheckRules] = useState<RuleType>('');
  const [currentField, setCurrentField] = useState<Field | undefined>(undefined);
  const [rulesMap, setRulesMap] = useState<Record<string, Rule[]>>({});
  const [rules, setRules] = useState<Rule[]>([]);
  const [fields, setFields] = useState<Field[]>([]);
  const [totalFieldsCount, setTotalFieldsCount] = useState(0);
  const eventBoxRef = useRef<HTMLDivElement>(null);
  const page = useRef<PaginationState>({
    page: 0,
    size: DEFAULT_PAGE_SIZE
  });

  const [chartData, setChartData] = useState<any[]>([]);

  const fetchChartData = useCallback(
    async (value: [Dayjs, Dayjs] = dataValue) => {
      const start = dayjs(value[0]).startOf('day').format('YYYY-MM-DD');
      const end = dayjs(value[1]).endOf('day').format('YYYY-MM-DD');
      const response = await DataQualityService.getChartData({
        endTime: end,
        eventId,
        startTime: start
      });
      setChartData(response);
    },
    [eventId, dataValue]
  );

  const fetchRules = useCallback(async () => {
    try {
      const rules = await DataQualityService.listBy([
        {
          operator: 'EQ',
          propertyName: 'eventId',
          value: eventId
        }
      ]);

      const rulesMap = rules.reduce((acc: Record<string, Rule[]>, curr: Rule) => {
        acc[curr.tableSchemaName] = [...(acc[curr.tableSchemaName] || []), curr];
        return acc;
      }, {});

      setRulesMap(rulesMap);
      setRules(rules);
      return rules;
    } catch (error) {
      console.error('Failed to fetch rules:', error);
      return [];
    }
  }, [eventId]);

  const fetchFields = useCallback(
    async (params: { tableSchemaName?: string; ruleType?: RuleType; resetPage?: boolean; time?: [Dayjs, Dayjs] }) => {
      try {
        if (params.resetPage) {
          page.current = { page: 0, size: DEFAULT_PAGE_SIZE };
        }

        const response = await DataQualityService.searchByFields({
          eventId,
          sortOrder: 'DESC',
          sortField: 'createTime',
          startTime: params.time?.[0]?.startOf('day').valueOf() || dataValue[0].startOf('day').valueOf(),
          endTime: params.time?.[1]?.endOf('day').valueOf() || dataValue[1].endOf('day').valueOf(),
          page: page.current,
          ...params
        });

        if (params.resetPage) {
          setFields(response.eventBnormalLogVoList);
        } else {
          setFields((prev) => [...prev, ...response.eventBnormalLogVoList]);
        }
        setTotalFieldsCount(response.total);
        return response;
      } catch (error) {
        console.error('Failed to fetch fields:', error);
        return null;
      }
    },
    [eventId, dataValue]
  );

  const handleDateChange = useCallback(
    (value: [Dayjs, Dayjs]) => {
      if (value && Array.isArray(value) && value[0] && value[1]) {
        setDataValue([value[0], value[1]]);
        Promise.all([fetchFields({ resetPage: true, time: [value[0], value[1]] }), fetchChartData(value)]);
      }
    },
    [fetchFields, fetchChartData]
  );

  const handleAttributeChange = useCallback(
    async (ruleId: string | number) => {
      eventBoxRef.current?.scrollTo({
        top: 0
      });

      const find = rules.find((item) => item.id === ruleId);
      setCurrentRule(find);
      setCurrentField(undefined);
      await fetchFields({
        tableSchemaName: find?.tableSchemaName,
        resetPage: true
      });
      setCurrentCheckRules('');
    },
    [rules, fetchFields]
  );

  const handleRuleTypeChange = useCallback(
    async (ruleType: RuleType) => {
      eventBoxRef.current?.scrollTo({
        top: 0
      });

      setCurrentCheckRules(ruleType);
      const params = {
        tableSchemaName: currentRule?.tableSchemaName,
        resetPage: true
      };
      await fetchFields(_.isEmpty(ruleType) ? params : { ...params, ruleType });
    },
    [currentRule, fetchFields]
  );

  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      if (totalFieldsCount <= fields.length) return;

      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      if (scrollTop + clientHeight >= scrollHeight) {
        setLoading(true);
        page.current = {
          ...page.current,
          page: page.current.page + DEFAULT_PAGE_SIZE
        };

        const params = {
          tableSchemaName: currentRule?.tableSchemaName
        };
        fetchFields(_.isEmpty(currentCheckRules) ? params : { ...params, ruleType: currentCheckRules }).finally(() => {
          setLoading(false);
        });
      }
    },
    [fields.length, currentRule, currentCheckRules, fetchFields, totalFieldsCount]
  );

  useEffect(() => {
    (async () => {
      setLoading(true);
      await Promise.all([fetchRules(), fetchFields({ resetPage: true }), fetchChartData()]);
      setLoading(false);
    })();
  }, []);

  return {
    loading,
    dataValue,
    currentRule,
    currentCheckRules,
    currentField,
    rulesMap,
    rules,
    fields,
    totalFieldsCount,
    eventBoxRef,
    setCurrentField,
    handleDateChange,
    handleAttributeChange,
    handleRuleTypeChange,
    handleScroll,
    chartData
  };
};
