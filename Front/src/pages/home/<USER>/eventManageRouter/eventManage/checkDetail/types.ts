export interface Rule {
  displayName: string;
  eventId: number;
  eventName: string;
  id: number;
  projectId: string;
  ruleType: string;
  ruleTypeValue: string;
  tableSchemaName: string;
}

export interface Field {
  createTime: number;
  errorReason: string;
  eventId: number;
  eventName: string;
  messageInfo: string;
  projectId: string;
  receivedTimestamp: number;
  ruleType: string;
  topicName: string;
}

export type RuleType =
  | ''
  | 'MUST_PASS_CHECK'
  | 'ENUM_CHECK'
  | 'REGULAR_MATCHING_CHECK'
  | 'INTERVAL_CHECK'
  | 'INCLUDE_CHECK';

export interface CheckDetailProps {
  value: {
    id: number;
    eventNameValue: string;
    name: string;
  };
  doAction: (visible: boolean) => void;
}

export interface PaginationState {
  page: number;
  size: number;
}
