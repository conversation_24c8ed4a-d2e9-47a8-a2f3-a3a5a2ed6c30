import { DatePicker } from '@/components';
import { transformUrl } from '@/utils/universal';
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, Select, Spin, Timeline, Typography } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React from 'react';
import DataQualityService from 'service/dataQualityService';
import { t } from 'utils/translation';
import { JsonDisplay } from '../editAndDetail/components/jsonDisplay';
import { TrendChart } from './components/TrendChart';
import { DRAWER_WIDTH, INITIAL_DATE_RANGE, RULE_TYPE_OPTIONS } from './constants';
import { useCheckDetail } from './hooks/useCheckDetail';
import { CheckDetailProps } from './types';

const { Title, Paragraph } = Typography;
const { RangePicker } = DatePicker;

export const CheckDetail: React.FC<CheckDetailProps> = ({ value, doAction }) => {
  const {
    loading,
    dataValue,
    currentRule,
    currentCheckRules,
    currentField,
    rulesMap,
    rules,
    fields,
    totalFieldsCount,
    eventBoxRef,
    setCurrentField,
    handleDateChange,
    handleAttributeChange,
    handleRuleTypeChange,
    handleScroll,
    chartData
  } = useCheckDetail(value.id);

  const checkRules = React.useMemo(() => {
    const findOptions = rulesMap[currentRule?.tableSchemaName!];
    const _options = findOptions?.map((item) => ({
      label: RULE_TYPE_OPTIONS.find((option) => option.value === item.ruleType)?.label,
      value: item.ruleType
    }));
    return _options || RULE_TYPE_OPTIONS;
  }, [currentRule, rulesMap]);

  return (
    <Drawer
      className="checkEventDetail"
      title={t('dataCenter-MlsRrZUcJQIh')} 
      placement="right"
      open
      closable
      destroyOnClose
      width={DRAWER_WIDTH}
      onClose={() => doAction(false)}
      footer={
        <div className="text-right">
          <Button loading={loading} onClick={() => doAction(false)} className="mr-8">
            {t('dataCenter-NqL5gAO2eVaC')}
          </Button>
          <Button loading={loading} type="primary" className="mr-8">
            {t('dataCenter-X4Zc514Zu38U')}
          </Button>
        </div>
      }
    >
      <Spin spinning={loading}>
        <Typography>
          <Title level={5}>{t('dataCenter-tWYOUkCSYGkf')}</Title>
          <Paragraph className="flex gap-32">
            <div>{t('dataCenter-apHfnf9NRoIh')}{value.eventNameValue}</div>
            <div>{t('dataCenter-5hdJ705Z9M0R')}{value.name}</div>
          </Paragraph>

          <Title level={5}>
            <div className="flex gap-32">
              <div>{t('dataCenter-wK6fvYx2pOVv')}</div>
              <RangePicker
                value={dataValue}
                format={INITIAL_DATE_RANGE.FORMAT}
                allowClear={false}
                onChange={(value) => handleDateChange(value as [Dayjs, Dayjs])}
              />
              {/* <div className="cursor-pointer">
                <ReloadOutlined />
              </div> */}
            </div>
          </Title>

          <Title level={4}>
            <Alert
              message={
                <div>
                  <span className="w-10 h-10 rounded-[50%] inline-block border-[2px] border-solid border-red-500 mr-10" />
                  {t('dataCenter-pEoXl1LaWsNg')}
                </div>
              }
            />
          </Title>

          <TrendChart data={chartData} />
        </Typography>

        <Title level={5}>{t('dataCenter-kNCqty0c8HOk')}</Title>
        <Paragraph className="flex gap-32">
          <div>
            <span>{t('dataCenter-3YN8JHntyXlL')}</span>
            <Select
              value={currentRule?.id || ''}
              style={{ width: 300 }}
              showSearch
              optionFilterProp="label"
              onChange={handleAttributeChange}
              options={[
                {
                  label: t('dataCenter-GkNu0Qdl3vm0'),
                  value: ''
                },
                ...Array.from(new Set(rules.map((item) => item.tableSchemaName))).map((tableSchemaName) => {
                  const item = rules.find((rule) => rule.tableSchemaName === tableSchemaName);
                  return {
                    label: item?.tableSchemaName || '',
                    value: item?.id || ''
                  };
                })
              ]}
            />
          </div>

          <div>
            <span>{t('dataCenter-y4sZ5JN996CF')}</span>
            <Select
              style={{ width: 300 }}
              options={[{ label: t('dataCenter-GkNu0Qdl3vm0'), value: '' }, ...checkRules]}
              value={currentCheckRules}
              onChange={handleRuleTypeChange}
            />
          </div>

          <div>{t('dataCenter-1i6eT1w30ZE4')}{totalFieldsCount}</div>
          <a
            onClick={async () => {
              const params = {
                eventId: value.id,
                startTime: dataValue[0].startOf('day').valueOf(),
                endTime: dataValue[1].endOf('day').valueOf(),
                page: { page: 0, size: totalFieldsCount },
                tableSchemaName: currentRule?.tableSchemaName
              };
              const res = await DataQualityService.searchByFieldsDownload(
                _.isEmpty(currentCheckRules) ? params : { ...params, ruleType: currentCheckRules }
              );
              if (res.flag === 'SUCCESS') {
                window.location.href = transformUrl(res.path);
              }
            }}
          >
            {t('dataCenter-JYT5sGY55Nmi')}
          </a>
        </Paragraph>

        <Paragraph>
          <div className="flex h-400">
            <div className="w-[40%]">
              <div className="h-40 bg-slate-300 pl-24 items-center flex">
                <div>{value.name}</div>
              </div>
              <div
                className="h-[calc(100%-40px)] overflow-y-auto py-24 pl-20"
                onScroll={handleScroll}
                ref={eventBoxRef}
              >
                <Timeline>
                  {fields.map((item, index) => (
                    <Timeline.Item key={index} color="green">
                      <span
                        className="block hover:shadow-md cursor-pointer"
                        onClick={() => setCurrentField(item)}
                        style={{
                          background: JSON.stringify(currentField) === JSON.stringify(item) ? '#e9e9e9' : 'transparent'
                        }}
                      >
                        {dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')}
                      </span>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </div>
            </div>

            <div className="h-full w-1 bg-black" />

            <div className="w-[60%] p-[0 24px]">
              <div className="h-40 bg-slate-300 px-24 flex items-center justify-between">
                <span>{t('dataCenter-mzK9LzE8SudR')}</span>
                <span>
                  {t('dataCenter-6WSKP9PggLAR')}{currentField && dayjs(currentField.receivedTimestamp).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              </div>
              <div className="h-[calc(100%-40px)] overflow-y-auto">
                <JsonDisplay
                  data={JSON.parse(currentField?.messageInfo || '{}') || '{}'}
                  errors={JSON.parse(currentField?.errorReason || '{}') || '{}'}
                />
              </div>
            </div>
          </div>
        </Paragraph>
      </Spin>
    </Drawer>
  );
};
