import { Line } from '@ant-design/charts';
import dayjs from 'dayjs';
import React from 'react';
import { t } from 'utils/translation';

interface DataItem {
  dataDate: number;
  num: number;
  sample?: number;
}

interface TrendChartProps {
  data: DataItem[];
}

export const TrendChart: React.FC<TrendChartProps> = ({ data }) => {
  const config = {
    data,
    xField: 'dataDate',
    yField: 'num',
    xAxis: {
      label: {
        formatter: (v: string) => {
          return dayjs(parseInt(v)).format('YYYY-MM-DD');
        }
      }
    },
    // label: {},
    point: {
      size: (d: { dataDate: number }) => {
        const find = data.find((item) => item.dataDate === d.dataDate);
        return find?.sample === 1 ? 4 : 0;
      },
      shape: (d: { dataDate: number }) => {
        const find = data.find((item) => item.dataDate === d.dataDate);
        return find?.sample === 1 ? 'circle' : undefined;
      },
      style: {
        fill: 'white',
        stroke: 'red',
        lineWidth: 1
      }
    },
    tooltip: {
      showMarkers: false,
      formatter: (datum: DataItem) => {
        return {
          name: t('dataCenter-GaizkhUJaJMd'),
          value: datum.num,
          title: dayjs(datum.dataDate).format('YYYY-MM-DD')
        };
      }
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: '#000',
          fill: 'red'
        }
      }
    },
    interactions: [
      {
        type: 'marker-active'
      }
    ]
  };
  // @ts-ignore
  return <Line {...config} />;
};
