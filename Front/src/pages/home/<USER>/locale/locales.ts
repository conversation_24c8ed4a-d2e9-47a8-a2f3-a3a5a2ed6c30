export default {
  cn: {
    'operationCenter-Uxr1GaJegdbD': '共',
    'operationCenter-WlT3BUwkdueR': '条',
    'operationCenter-CVMeansvtlY4': '新建流程引擎会话数据',
    'operationCenter-fF7h7BpodTY1': '用户属性',
    'operationCenter-oSobjHis7Vcl': '事件-通用属性',
    'operationCenter-Ke1p88oENNcz': '事件-专有属性',
    'operationCenter-XiII3JYyW7iZ': '用户标签',
    'operationCenter-MwCbH3bm6mMV': '系统',
    'operationCenter-SYw2HMMOdhMg': 'id只能输入纯数字',
    'operationCenter-K80YRmgtlaUU': '删除',
    'operationCenter-uOZLIMkSihSf': '您将删除选项：',
    'operationCenter-UTQVkNzm0PZA': '该操作不可撤销，删除后该数据不能在流程引擎中传递，请谨慎操作。',
    'operationCenter-aV4zRYyFO12o': '确认删除',
    'operationCenter-tISt46r7JIOq': '删除成功',
    'operationCenter-n6gWTutNVdeU': '查看流程引擎会话数据',
    'operationCenter-AYWpcfyuxnk8': '业务实体',
    'operationCenter-bRsp62BZZpKz': '数据关键字',
    'operationCenter-6SGnvmag24YN': '关联数据',
    'operationCenter-LUUMVdGYTFe1': '数据来源类型',
    'operationCenter-MI3MQn6wlwwf': '数据类型',
    'operationCenter-ikNKnNKskQJ9': '是否预设',
    'operationCenter-gBhA0V6DMQv5': '预设',
    'operationCenter-ZqQtWQL6xYOB': '自定义',
    'operationCenter-IgYt1dFIWdxF': '创建人',
    'operationCenter-AsZgoQOXmCfq': '创建时间',
    'operationCenter-vG5NwBediwt9': '更新人',
    'operationCenter-NQh7WG5ZZRC2': '更新时间',
    'operationCenter-KnIq6V8DHXBz': '操作',
    'operationCenter-KukFJlpCrFUK': '查看',
    'operationCenter-PK3aXRyfQexx': '更多',
    'operationCenter-1XyYxgdRZjpz': '筛选',
    'operationCenter-AXvzfe5UqrmT': '新建',
    'operationCenter-GtVVqM5i0Rzf': '请输入数据关键字',
    'operationCenter-N8qo4rGlxbUT': '最大长度为64个字符',
    'operationCenter-byug8oCKlZX9': '支持英文大小写、数字、下划线，首位必须为英文，末位不能为下划线',
    'operationCenter-8ARZqaVEDOt4': '请输入',
    'operationCenter-Ule8gjdLNy2c': '请选择业务实体',
    'operationCenter-cNEfJ7KCInSa': '请选择',
    'operationCenter-bYSWTelijP8g': '请选择数据来源类型',
    'operationCenter-Erz22pDCwi2X': '请选择关联数据',
    'operationCenter-ZkTmLLBpqnhT': '事件',
    'operationCenter-WJ3co4GFPGAD': '请选择事件',
    'operationCenter-jML30jUHPYJ8': '关联属性字段',
    'operationCenter-FAmUZlUSkwtn': '请选择关联属性字段',
    'operationCenter-Zw4XZPzJAJ0q': '用户ID类型',
    'operationCenter-bs1hQRb81JUj': '请选择用户ID类型',
    'operationCenter-F3NWUxgW58Mf': '关联标签',
    'operationCenter-BOKJMRKuRHMU': '请选择关联标签',
    'operationCenter-zUQUq6GBb2iB': '取消',
    'operationCenter-mUOHeqjVDN2O': '确定'
  },
  en: {
    'operationCenter-Uxr1GaJegdbD': 'Total',
    'operationCenter-WlT3BUwkdueR': 'records',
    'operationCenter-CVMeansvtlY4': 'Create Process Engine Session Data',
    'operationCenter-fF7h7BpodTY1': 'User Properties',
    'operationCenter-oSobjHis7Vcl': 'Event-General Properties',
    'operationCenter-Ke1p88oENNcz': 'Event-Specific Properties',
    'operationCenter-XiII3JYyW7iZ': 'User Labels',
    'operationCenter-MwCbH3bm6mMV': 'System',
    'operationCenter-SYw2HMMOdhMg': 'ID can only contain numbers',
    'operationCenter-K80YRmgtlaUU': 'Delete',
    'operationCenter-uOZLIMkSihSf': 'You will delete the following options:',
    'operationCenter-UTQVkNzm0PZA': 'This operation cannot be undone. After deletion, the data cannot be passed in the process engine, please proceed with caution.',
    'operationCenter-aV4zRYyFO12o': 'Confirm Delete',
    'operationCenter-tISt46r7JIOq': 'Delete Success',
    'operationCenter-n6gWTutNVdeU': 'View Process Engine Session Data',
    'operationCenter-AYWpcfyuxnk8': 'Business Entity',
    'operationCenter-bRsp62BZZpKz': 'Data Key',
    'operationCenter-6SGnvmag24YN': 'Associated Data',
    'operationCenter-LUUMVdGYTFe1': 'Data Source Type',
    'operationCenter-MI3MQn6wlwwf': 'Data Type',
    'operationCenter-ikNKnNKskQJ9': 'Is Preset',
    'operationCenter-gBhA0V6DMQv5': 'Preset',
    'operationCenter-ZqQtWQL6xYOB': 'Custom',
    'operationCenter-IgYt1dFIWdxF': 'Creator',
    'operationCenter-AsZgoQOXmCfq': 'Creation Time',
    'operationCenter-vG5NwBediwt9': 'Updater',
    'operationCenter-NQh7WG5ZZRC2': 'Update Time',
    'operationCenter-KnIq6V8DHXBz': 'Operation',
    'operationCenter-KukFJlpCrFUK': 'View',
    'operationCenter-PK3aXRyfQexx': 'More',
    'operationCenter-1XyYxgdRZjpz': 'Filter',
    'operationCenter-AXvzfe5UqrmT': 'Create',
    'operationCenter-GtVVqM5i0Rzf': 'Please enter data key',
    'operationCenter-N8qo4rGlxbUT': 'Maximum length of 64 characters',
    'operationCenter-byug8oCKlZX9': 'Supports English uppercase and lowercase letters, numbers, and underscores, the first character must be an English letter, and the last character cannot be an underscore',
    'operationCenter-8ARZqaVEDOt4': 'Please enter',
    'operationCenter-Ule8gjdLNy2c': 'Please select business entity',
    'operationCenter-cNEfJ7KCInSa': 'Please select',
    'operationCenter-bYSWTelijP8g': 'Please select data source type',
    'operationCenter-Erz22pDCwi2X': 'Please select associated data',
    'operationCenter-ZkTmLLBpqnhT': 'Event',
    'operationCenter-WJ3co4GFPGAD': 'Please select event',
    'operationCenter-jML30jUHPYJ8': 'Associated Attribute Field',
    'operationCenter-FAmUZlUSkwtn': 'Please select associated attribute field',
    'operationCenter-Zw4XZPzJAJ0q': 'User ID Type',
    'operationCenter-bs1hQRb81JUj': 'Please select user ID type',
    'operationCenter-F3NWUxgW58Mf': 'Associated Label',
    'operationCenter-BOKJMRKuRHMU': 'Please select associated label',
    'operationCenter-zUQUq6GBb2iB': 'Cancel',
    'operationCenter-mUOHeqjVDN2O': 'Confirm'
  }
};
