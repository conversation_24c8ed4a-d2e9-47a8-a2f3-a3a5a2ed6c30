export default {
  cn: {
    // index.jsx 翻译 - Front/src/pages/home/<USER>/index.jsx
    'approve-mGM3HUnFsEMu': '审批事项',
    'approve-Iu2kxuqk5hqN': '审批详情',
    'approve-sGSuVXwjOrGy': '审批名称',
    'approve-I6bhcMPQtoFs': '驳回',
    'approve-WWncMkSUS8WM': '通过',
    'approve-2vex7RqjP2Wa': '审批编号',
    'approve-XYazlDOO9UTU': '审批事项名称',
    'approve-gvmFA2nFRMoW': '类型',
    'approve-GI1MY67AUs4v': '审批历史',
    'approve-MeoIL4UUitty': '审批流程',
    'approve-Er3OCPtKIFDu': '确认通过',
    'approve-RykF0uuJYTeQ': '确认驳回',
    'approve-oB2vrTY1RrrJ': '审批意见',
    'approve-Qg20rpYR26Ke': '请输入审批意见',
    'approve-tRYU45kuu5bW': '最大长度限制为100位字符',
    'approve-3iuGeWEK5yuH': '审批成功',
    'approve-qir8AeKCfDqF': '用户分群',
    'approve-IdoIY8IRREw9': '流程画布',
    'approve-hGBdEdt7MQuZ': '营销作品',
    'approve-WmSovDrv2VRw': '标签系统',
    'approve-u8Adgr17G0xc': '消息模板',
    'approve-yUjtFaPUEARe': '流程详情',
    'approve-3jCLxMkB0db6': '分群详情',
    'approve-RQzD4WLJNlQg': '状态',

    // config.js 翻译 - Front/src/pages/home/<USER>/config.js
    'approve-cjLhV8mX8XEu': '审批中',
    'approve-xfUALfgz8iaf': '审批通过',
    'approve-WTu8SpaYe7NL': '驳回',
    'approve-alPFKV5eeZdZ': '已撤销',
    'approve-6syfqhJzfnCe': '已取消',
    'approve-Tz7LXM7oiauE': '开始',
    'approve-oxOg1ceV1Idp': '提交人',
    'approve-ZXiAAOW0sN1x': '审批人',
    'approve-H46puG4mXzfo': '结束',
    'approve-5j8n4hMP0zqb': '审批异常',

    // approvalHistory/index.jsx 翻译 - Front/src/pages/home/<USER>/approvalHistory/index.jsx
    'approve-vLvj1oyf2CfS': '审批历史',
    'approve-Ls5atnDJq4QP': '提交人',
    'approve-yQHu47Kv1Rab': '审批人',
    'approve-6aosIAIA2EWO': '通过',
    'approve-eHUJ6pO5URgq': '驳回',
    'approve-SwGw51euOF5f': '申请时间',
    'approve-JH0a0WmVuoX5': '审批时间',
    'approve-29HTiunDsGyy': '审批意见',

    // approvalProcess/index.jsx 翻译 - Front/src/pages/home/<USER>/approvalProcess/index.jsx
    'approve-rZvtIJKAyDfa': '开始',
    'approve-bMiohBG5R94F': '结束',
    'approve-JT2kMDYvQjS0': '审批人',
    'approve-5pq16hJ4nnMJ': '提交人',
    'approve-3GrVjDRmzfC1': '会签',
    'approve-fCcOWJePeeyh': '或签',
    'approve-xR8HeqBv0PPw': '组合',
    'approve-1efXgB608hJe': '将审批人节点拖入组合框内，形成组合条件，组合框内不需要连线；',
    'approve-PP4ZUVU0oxEj': '初始化画布',
    'approve-5WUxcufEBInY': '部门：',
    'approve-vbrpwxw5wc9R': '角色：',
    'approve-Jj0zePRymoMs': '审批人：',
    'approve-LMBzrBOxTIw8': '重置定位',
    'approve-83cTZ80nnxFP': '快速上手',

    // approvalProcess/nodeDrawer/index.jsx 翻译 - Front/src/pages/home/<USER>/approvalProcess/nodeDrawer/index.jsx
    'approve-afL0mXdRbMnT': '审批人',
    'approve-W0EGXsxW3fXM': '支持同时按部门、角色、指定审批人配置。符合任一条件的用户均可审批。',
    'approve-JPfDhWb4SxnL': '或签',
    'approve-MhdtH5vwMqJm': '部门',
    'approve-7ra9PHlm01Nq': '角色',
    'approve-wTTHNcfeAxc6': '指定审批人',

    // approvalProcess/helpModal/index.jsx 翻译 - Front/src/pages/home/<USER>/approvalProcess/helpModal/index.jsx
    'approve-cESnuBhgVJ8t': '使用帮助',
    'approve-nm2mKw4XCPsr': '一、流程配置组件',
    'approve-zWy26YpdkD58': '开始：审批流程开始（不能编辑）；',
    'approve-5TzHsLYOB5Ye': '结束：审批流程结束（不能编辑）；',
    'approve-KN1XsI38hpMt': '提交人：审批任务的提交人（不能编辑），此节点读取具体的审批人及其部门信息等；',
    'approve-O0ISHVTnBSmI': '审批人：审批任务的审批人，支持按部门、按角色、按指定审批人审批，支持多选，可设置或签；',
    'approve-trmdifehoFpz': '会签：将审批流变为会签形式，从会签节点输出的分支为且的关系，即分支需要全部走完；',
    'approve-vHBGMyBUqqFI': '组合：将【审批人节点】拖入组合框内，形成组合条件，组合框内不需要连线；',
    'approve-Wd24TGrAvImZ': '连线：代表审批的流转方向，【双击连线】设置业务规则可控制审批的流转（开始、结束、提交人的输入输出连线不能设置）；',
    'approve-Ov71Cg7vjanb': '二、配置一个正确的审批流程',
    'approve-WyCq8SwdXmkK': '审批对象创建后，第一次做流程配置时，会初始化一个简单的审批流（如上图），在简单审批流的基础上进行调整，或删除重新编排。一个正确的审批流程必须符合：',
    'approve-sGGjnE7A7KTZ': '1. 节点不能少：必须包含开始、提交人、审批人、结束节点；',
    'approve-v79c38y2Jcuc': '2. 节点不能多：开始和提交人节点只能是一个，审批人和结束节点可以是多个；',
    'approve-Sktr4yaRXM8t': '3. 顺序不能错：开始-提交人-审批人-结束；',
    'approve-a5okyekZeiZJ': '4. 连线要正确：除开始和结束节点只有一条连线，其余节点至少有"进入"和"输出"两条线；',
    'approve-yUKBfHfgSCBg': '三、配置或签流程',
    'approve-bQquZtBxMUgE': '1. 点击审批人节点，在弹出的侧边栏设置审批人；',
    'approve-i5p5I2V2MNbI': '2. 支持按部门、按角色、按指定审批人三种类型设置，每种类型支持多选；',
    'approve-9n31Be1Ex82K': '3. 每种类型内部，各类型之间都是"或"的关系，即为"或签"；',
    'approve-jjTbF3tpWLid': '四、配置会签流程',
    'approve-jB7iPj1PhqD0': '方法一：通过会签组件',
    'approve-4bkcgvmnrSJ2': '1. 将会签节点拖入画布中，会签节点后至少跟着两个审批人节点；',
    'approve-EEmcmV2jqXFN': '2. 会签节点以组的形式存在，即一前一后代表会签的开始与结束；',
    'approve-wvtLskZEy2IX': '3. 会签的所有分支走完，才会走到下一个审批节点；',
    'approve-946me3oY0xBo': '方法二：通过组合组件',
    'approve-Qiq8k2iW6DY7': '1. 将组合组件拖入画布中，组合节点只有输入、输出两条连线；',
    'approve-2PETn4FyInv6': '2. 将审批人节点拖入组合框中，并设置审批人；',
    'approve-oQUJV5MS6AHF': '3. 组合框中的审批人节点为"且"的关系，即为会签；',
    'approve-9kGNV7Gt2syB': '五、设置流转的业务规则',
    'approve-zBKFXZqWseuY': '业务规则有两种：通用业务规则以及配置的业务规则；',
    'approve-bjE0m8qQ6GPv': '1. 通用业务规则主要针对提交人，即按提交人的不同走不同的分支；',
    'approve-eaj0d55CzdrH': '2. 通用规则不绑定审批对象，所有审批对象通用；',
    'approve-EJy3VDWHgiFk': '3. 通用业务规则有三个参数：提交人-按部门、提交人-按角色、提交人-指定提交人；',
    'approve-bN6y03zUZXv2': '4. 配置的业务规则绑定审批对象，每个审批对象子类都可以设置自己的业务规则，当对审批对象进行流程配置时，仅展示自己的业务规则；',
    'approve-QL8m1Hygq4zm': '5. 双击连线即可设置业务规则；',

    // Additional helpModal translations
    'approve-VKhwiPx4tFKP': '使用帮助',
    'approve-rqZt6BFMSQ4M': '一、流程配置组件',
    'approve-1PkwDD6qaKur': '开始：审批流程开始（不能编辑）；',
    'approve-XpqgQbjkm7Zl': '结束：审批流程结束（不能编辑）；',
    'approve-rndqOM0MlAyc': '提交人：审批任务的提交人（不能编辑），此节点读取具体的审批人及其部门信息等；',
    'approve-OMu2FcCtjdw0': '审批人：审批任务的审批人，支持按部门、按角色、按指定审批人审批，支持多选，可设置或签；',
    'approve-qZTCwSraY8gA': '会签：将审批流变为会签形式，从会签节点输出的分支为且的关系，即分支需要全部走完；',
    'approve-HEAsaw5vYfIA': '组合：将【审批人节点】拖入组合框内，形成组合条件，组合框内不需要连线；',
    'approve-eMTZywrH1JuA': '连线：代表审批的流转方向，【双击连线】设置业务规则可控制审批的流转（开始、结束、提交人的输入输出连线不能设置）；',
    'approve-9RUloodHzQiy': '二、配置一个正确的审批流程',
    'approve-AXPxyNxOZwxq': '审批对象创建后，第一次做流程配置时，会初始化一个简单的审批流（如上图），在简单审批流的基础上进行调整，或删除重新编排。一个正确的审批流程必须符合：',
    'approve-atpk8lKZ2OYU': '1. 节点不能少：必须包含开始、提交人、审批人、结束节点；',
    'approve-zUZt4Ty3076Q': '2. 节点不能多：开始和提交人节点只能是一个，审批人和结束节点可以是多个；',
    'approve-K71hz2zpWQHH': '3. 顺序不能错：开始-提交人-审批人-结束；',
    'approve-hIoqUhQLEsAZ': '4. 连线要正确：除开始和结束节点只有一条连线，其余节点至少有"进入"和"输出"两条线；',
    'approve-pPj1VnUyB1Et': '三、配置或签流程',
    'approve-42nnq9AtjQm6': '1. 点击审批人节点，在弹出的侧边栏设置审批人；',
    'approve-jSwsuEZJBuIj': '2. 支持按部门、按角色、按指定审批人三种类型设置，每种类型支持多选；',
    'approve-QrbvfIlERMeR': '3. 每种类型内部，各类型之间都是"或"的关系，即为"或签"；',
    'approve-Za7JBfSvS8M0': '四、配置会签流程',
    'approve-5bRcVjMcqA7o': '方法一：通过会签组件',
    'approve-bgGWB49uCWxm': '1. 将会签节点拖入画布中，会签节点后至少跟着两个审批人节点；',
    'approve-jw7rUpXSXlFm': '2. 会签节点以组的形式存在，即一前一后代表会签的开始与结束；',
    'approve-yW7VuRHHhc9p': '3. 会签的所有分支走完，才会走到下一个审批节点；',
    'approve-gjrzBoET8D5g': '方法二：通过组合组件',
    'approve-fGTvLAcjd4HM': '1. 将组合组件拖入画布中，组合节点只有输入、输出两条连线；',
    'approve-0ymzxNFdD3GT': '2. 将审批人节点拖入组合框中，并设置审批人；',
    'approve-cDuOqPopmMHd': '3. 组合框中的审批人节点为"且"的关系，即为会签；',
    'approve-OMjMttYSKP1Q': '五、设置流转的业务规则',
    'approve-KxH61C7MGF2G': '业务规则有两种：通用业务规则以及配置的业务规则；',
    'approve-VChQCaL1J0o1': '1. 通用业务规则主要针对提交人，即按提交人的不同走不同的分支；',
    'approve-Pwi5FEZ0FDqX': '2. 通用规则不绑定审批对象，所有审批对象通用；',
    'approve-wOMxOXt6RlyC': '3. 通用业务规则有三个参数：提交人-按部门、提交人-按角色、提交人-指定提交人；',
    'approve-fuDEWXZXvS7x': '4. 配置的业务规则绑定审批对象，每个审批对象子类都可以设置自己的业务规则，当对审批对象进行流程配置时，仅展示自己的业务规则；',
    'approve-IkVD5QI8YLM5': '5. 双击连线即可设置业务规则；'
  },
  en: {
    // index.jsx translations - Front/src/pages/home/<USER>/index.jsx
    'approve-mGM3HUnFsEMu': 'Approval Items',
    'approve-Iu2kxuqk5hqN': 'Approval Detail',
    'approve-sGSuVXwjOrGy': 'Approval Name',
    'approve-I6bhcMPQtoFs': 'Reject',
    'approve-WWncMkSUS8WM': 'Approve',
    'approve-2vex7RqjP2Wa': 'Approval Number',
    'approve-XYazlDOO9UTU': 'Approval Item Name',
    'approve-gvmFA2nFRMoW': 'Type',
    'approve-GI1MY67AUs4v': 'Approval History',
    'approve-MeoIL4UUitty': 'Approval Process',
    'approve-Er3OCPtKIFDu': 'Confirm Approval',
    'approve-RykF0uuJYTeQ': 'Confirm Rejection',
    'approve-oB2vrTY1RrrJ': 'Approval Opinion',
    'approve-Qg20rpYR26Ke': 'Please enter approval opinion',
    'approve-tRYU45kuu5bW': 'Maximum length is 100 characters',
    'approve-3iuGeWEK5yuH': 'Approval Successful',
    'approve-qir8AeKCfDqF': 'User Segment',
    'approve-IdoIY8IRREw9': 'Process Canvas',
    'approve-hGBdEdt7MQuZ': 'Marketing Works',
    'approve-WmSovDrv2VRw': 'Label System',
    'approve-u8Adgr17G0xc': 'Message Template',
    'approve-yUjtFaPUEARe': 'Process Detail',
    'approve-3jCLxMkB0db6': 'Segment Detail',
    'approve-RQzD4WLJNlQg': 'Status',

    // config.js translations - Front/src/pages/home/<USER>/config.js
    'approve-cjLhV8mX8XEu': 'Under Approval',
    'approve-xfUALfgz8iaf': 'Approved',
    'approve-WTu8SpaYe7NL': 'Rejected',
    'approve-alPFKV5eeZdZ': 'Withdrawn',
    'approve-6syfqhJzfnCe': 'Cancelled',
    'approve-Tz7LXM7oiauE': 'Start',
    'approve-oxOg1ceV1Idp': 'Submitter',
    'approve-ZXiAAOW0sN1x': 'Approver',
    'approve-H46puG4mXzfo': 'End',
    'approve-5j8n4hMP0zqb': 'Approval Error',

    // approvalHistory/index.jsx translations - Front/src/pages/home/<USER>/approvalHistory/index.jsx
    'approve-vLvj1oyf2CfS': 'Approval History',
    'approve-Ls5atnDJq4QP': 'Submitter',
    'approve-yQHu47Kv1Rab': 'Approver',
    'approve-6aosIAIA2EWO': 'Approved',
    'approve-eHUJ6pO5URgq': 'Rejected',
    'approve-SwGw51euOF5f': 'Application Time',
    'approve-JH0a0WmVuoX5': 'Approval Time',
    'approve-29HTiunDsGyy': 'Approval Opinion',

    // approvalProcess/index.jsx translations - Front/src/pages/home/<USER>/approvalProcess/index.jsx
    'approve-rZvtIJKAyDfa': 'Start',
    'approve-bMiohBG5R94F': 'End',
    'approve-JT2kMDYvQjS0': 'Approver',
    'approve-5pq16hJ4nnMJ': 'Submitter',
    'approve-3GrVjDRmzfC1': 'Parallel Gateway',
    'approve-fCcOWJePeeyh': 'Or Gateway',
    'approve-xR8HeqBv0PPw': 'Combination',
    'approve-1efXgB608hJe': 'Drag approver nodes into the combination box to form combination conditions, no connection needed within the combination box;',
    'approve-PP4ZUVU0oxEj': 'Initialize Canvas',
    'approve-5WUxcufEBInY': 'Department:',
    'approve-vbrpwxw5wc9R': 'Role:',
    'approve-Jj0zePRymoMs': 'Approver:',
    'approve-LMBzrBOxTIw8': 'Reset Position',
    'approve-83cTZ80nnxFP': 'Quick Start',

    // approvalProcess/nodeDrawer/index.jsx translations - Front/src/pages/home/<USER>/approvalProcess/nodeDrawer/index.jsx
    'approve-afL0mXdRbMnT': 'Approver',
    'approve-W0EGXsxW3fXM': 'Supports configuration by department, role, and designated approver simultaneously. Users meeting any condition can approve.',
    'approve-JPfDhWb4SxnL': 'Or Gateway',
    'approve-MhdtH5vwMqJm': 'Department',
    'approve-7ra9PHlm01Nq': 'Role',
    'approve-wTTHNcfeAxc6': 'Designated Approver',

    // approvalProcess/helpModal/index.jsx translations - Front/src/pages/home/<USER>/approvalProcess/helpModal/index.jsx
    'approve-cESnuBhgVJ8t': 'Help Guide',
    'approve-nm2mKw4XCPsr': '1. Process Configuration Components',
    'approve-zWy26YpdkD58': 'Start: Approval process start (cannot be edited);',
    'approve-5TzHsLYOB5Ye': 'End: Approval process end (cannot be edited);',
    'approve-KN1XsI38hpMt': 'Submitter: Submitter of approval task (cannot be edited), this node reads specific approver and department information;',
    'approve-O0ISHVTnBSmI': 'Approver: Approver of approval task, supports approval by department, role, designated approver, supports multiple selection, can set or gateway;',
    'approve-trmdifehoFpz': 'Parallel Gateway: Changes approval flow to parallel form, branches output from parallel gateway have AND relationship, all branches must be completed;',
    'approve-vHBGMyBUqqFI': 'Combination: Drag [Approver Nodes] into combination box to form combination conditions, no connection needed within combination box;',
    'approve-Wd24TGrAvImZ': 'Connection: Represents approval flow direction, [Double-click connection] to set business rules to control approval flow (start, end, submitter input/output connections cannot be set);',
    'approve-Ov71Cg7vjanb': '2. Configure a Correct Approval Process',
    'approve-WyCq8SwdXmkK': 'After creating approval object, when configuring process for the first time, a simple approval flow will be initialized (as shown above). Adjust based on the simple approval flow or delete and rearrange. A correct approval process must comply with:',
    'approve-sGGjnE7A7KTZ': '1. Nodes cannot be missing: Must include start, submitter, approver, end nodes;',
    'approve-v79c38y2Jcuc': '2. Nodes cannot be excessive: Start and submitter nodes can only be one each, approver and end nodes can be multiple;',
    'approve-Sktr4yaRXM8t': '3. Order cannot be wrong: Start-Submitter-Approver-End;',
    'approve-a5okyekZeiZJ': '4. Connections must be correct: Except start and end nodes have only one connection, other nodes must have at least "input" and "output" connections;',
    'approve-yUKBfHfgSCBg': '3. Configure Or Gateway Process',
    'approve-bQquZtBxMUgE': '1. Click approver node, set approver in the popup sidebar;',
    'approve-i5p5I2V2MNbI': '2. Supports three types of settings: by department, by role, by designated approver, each type supports multiple selection;',
    'approve-9n31Be1Ex82K': '3. Within each type, all types have "OR" relationship, which is "Or Gateway";',
    'approve-jjTbF3tpWLid': '4. Configure Parallel Gateway Process',
    'approve-jB7iPj1PhqD0': 'Method 1: Through Parallel Gateway Component',
    'approve-4bkcgvmnrSJ2': '1. Drag parallel gateway node into canvas, parallel gateway node must be followed by at least two approver nodes;',
    'approve-EEmcmV2jqXFN': '2. Parallel gateway nodes exist in group form, front and back represent start and end of parallel gateway;',
    'approve-wvtLskZEy2IX': '3. All branches of parallel gateway must be completed before proceeding to next approval node;',
    'approve-946me3oY0xBo': 'Method 2: Through Combination Component',
    'approve-Qiq8k2iW6DY7': '1. Drag combination component into canvas, combination node has only input and output connections;',
    'approve-2PETn4FyInv6': '2. Drag approver nodes into combination box and set approvers;',
    'approve-oQUJV5MS6AHF': '3. Approver nodes in combination box have "AND" relationship, which is parallel gateway;',
    'approve-9kGNV7Gt2syB': '5. Set Business Rules for Flow',
    'approve-zBKFXZqWseuY': 'There are two types of business rules: general business rules and configured business rules;',
    'approve-bjE0m8qQ6GPv': '1. General business rules mainly target submitters, different submitters take different branches;',
    'approve-eaj0d55CzdrH': '2. General rules are not bound to approval objects, universal for all approval objects;',
    'approve-EJy3VDWHgiFk': '3. General business rules have three parameters: submitter-by department, submitter-by role, submitter-designated submitter;',
    'approve-bN6y03zUZXv2': '4. Configured business rules are bound to approval objects, each approval object subclass can set its own business rules, only its own business rules are displayed when configuring process for approval objects;',
    'approve-QL8m1Hygq4zm': '5. Double-click connection to set business rules;',

    // Additional helpModal translations
    'approve-VKhwiPx4tFKP': 'Usage Help',
    'approve-rqZt6BFMSQ4M': '1. Process Configuration Components',
    'approve-1PkwDD6qaKur': 'Start: Approval process begins (cannot be edited);',
    'approve-XpqgQbjkm7Zl': 'End: Approval process ends (cannot be edited);',
    'approve-rndqOM0MlAyc': 'Submitter: The submitter of the approval task (cannot be edited), this node reads specific approver and department information;',
    'approve-OMu2FcCtjdw0': 'Approver: The approver of the approval task, supports approval by department, role, or designated approver, supports multiple selection, can set OR approval;',
    'approve-qZTCwSraY8gA': 'Countersign: Changes the approval flow to countersign form, branches output from countersign nodes have AND relationship, meaning all branches must be completed;',
    'approve-HEAsaw5vYfIA': 'Combination: Drag [Approver Nodes] into the combination box to form combination conditions, no connection lines needed within the combination box;',
    'approve-eMTZywrH1JuA': 'Connection: Represents the flow direction of approval, [double-click connection] to set business rules to control approval flow (start, end, submitter input/output connections cannot be set);',
    'approve-9RUloodHzQiy': '2. Configure a Correct Approval Process',
    'approve-AXPxyNxOZwxq': 'After creating an approval object, when configuring the process for the first time, a simple approval flow will be initialized (as shown above), adjust based on the simple approval flow, or delete and rearrange. A correct approval process must comply with:',
    'approve-atpk8lKZ2OYU': '1. Nodes cannot be missing: must include start, submitter, approver, and end nodes;',
    'approve-zUZt4Ty3076Q': '2. Nodes cannot be excessive: start and submitter nodes can only be one each, approver and end nodes can be multiple;',
    'approve-K71hz2zpWQHH': '3. Order cannot be wrong: start-submitter-approver-end;',
    'approve-hIoqUhQLEsAZ': '4. Connections must be correct: except start and end nodes which have only one connection, other nodes must have at least "input" and "output" connections;',
    'approve-pPj1VnUyB1Et': '3. Configure OR Approval Process',
    'approve-42nnq9AtjQm6': '1. Click the approver node, set the approver in the pop-up sidebar;',
    'approve-jSwsuEZJBuIj': '2. Supports three types of settings: by department, by role, by designated approver, each type supports multiple selection;',
    'approve-QrbvfIlERMeR': '3. Within each type, the relationship between types is "OR", which is "OR approval";',
    'approve-Za7JBfSvS8M0': '4. Configure Countersign Process',
    'approve-5bRcVjMcqA7o': 'Method 1: Through Countersign Component',
    'approve-bgGWB49uCWxm': '1. Drag the countersign node into the canvas, the countersign node should be followed by at least two approver nodes;',
    'approve-jw7rUpXSXlFm': '2. Countersign nodes exist in group form, one before and one after representing the start and end of countersign;',
    'approve-yW7VuRHHhc9p': '3. All branches of countersign must be completed before proceeding to the next approval node;',
    'approve-gjrzBoET8D5g': 'Method 2: Through Combination Component',
    'approve-fGTvLAcjd4HM': '1. Drag the combination component into the canvas, combination nodes have only input and output connections;',
    'approve-0ymzxNFdD3GT': '2. Drag approver nodes into the combination box and set approvers;',
    'approve-cDuOqPopmMHd': '3. Approver nodes in the combination box have "AND" relationship, which is countersign;',
    'approve-OMjMttYSKP1Q': '5. Set Business Rules for Flow',
    'approve-KxH61C7MGF2G': 'There are two types of business rules: general business rules and configured business rules;',
    'approve-VChQCaL1J0o1': '1. General business rules mainly target submitters, i.e., different branches based on different submitters;',
    'approve-Pwi5FEZ0FDqX': '2. General rules are not bound to approval objects, applicable to all approval objects;',
    'approve-wOMxOXt6RlyC': '3. General business rules have three parameters: submitter-by department, submitter-by role, submitter-designated submitter;',
    'approve-fuDEWXZXvS7x': '4. Configured business rules are bound to approval objects, each approval object subclass can set its own business rules, when configuring processes for approval objects, only its own business rules are displayed;',
    'approve-IkVD5QI8YLM5': '5. Double-click the connection to set business rules;'
  }
};
