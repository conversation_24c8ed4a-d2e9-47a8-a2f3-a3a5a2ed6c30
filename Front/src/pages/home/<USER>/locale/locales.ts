export default {
  cn: {
    // index.tsx translations - Front/src/pages/home/<USER>/index.tsx
    'setting-CbXu9H3kQLu1': '操作',
    'setting-pvSSNe0Pb6GV': '查看明细',
    'setting-6rYKcbeyyWNd': '操作成功',
    'setting-s1Fys6nl01Oa': '站内消息',
    'setting-dFwgHoVapjUG': '删除',
    'setting-URCSl4UhYstc': '标记为已读',
    'setting-nlPRBf3m4wbq': '全部删除',
    'setting-yVOCbjnOh0eN': '确定要全部删除站内信吗？',
    'setting-MJlnNIzmfDVo': '该操作无法撤销，请确认后操作',
    'setting-WTIhRZZjYGxR': '确定',
    'setting-DXTyt99HiSRG': '取消',
    'setting-ZkiFtd69CQAj': '全部已读',
    'setting-wntVLAYoyPvX': '确定要标记全部站内信为已读吗?',
    'setting-wuogY6Ii34jq': '数据质量监控告警说明',
    'setting-rDP6LQf02gE2': '事件监控告警：从当天事件入库开始，会根据配置的校验规则进行事件质量监控，如果发现异常则对事件负责人发送站内消息，在"事件管理-校验明细"可查看详细日志。',
    'setting-SHeeAjJPufGR': '1. 发送频率：首次发现异常后会记录，并会在5分钟后发送之前的异常数据，后续将以10-20-40-60分钟的频率发送；',
    'setting-QUbBpexbtrYc': '2. 冷却期：当触发60分钟频率发送时，会进入60分钟冷却期，60分钟过后解除冷却期，直到下次发现异常再以5-10-20-40-60分钟的频率发送；',

    // config.ts translations - Front/src/pages/home/<USER>/config.ts
    'setting-QDrrH5XCk9cC': '状态',
    'setting-Tzv0U48a8ahE': '请选择状态',
    'setting-2S0PgZ4fAVKb': '已读消息',
    'setting-o33htOuoCYci': '未读消息',
    'setting-9CBCu3rd1ixK': '标题',
    'setting-OKlCchXGVwF0': '请输入',
    'setting-p66xYfnG5MAg': '消息时间',

    // constants.tsx translations - Front/src/pages/home/<USER>/constants.tsx
    'setting-k8EmdiZDRBhG': '强制登出消息',
    'setting-Z45tiVHexnTr': '登录消息',
    'setting-iLlMDzF8UOvg': '密码即将过期消息',
    'setting-Isg8gYdxDmCb': 'Token过期消息',
    'setting-0FuaW1P7SRG4': '用户通用消息',
    'setting-LwBkMhEw7HRx': '系统通用消息',
    'setting-6ecUl0HdIixJ': '数据质量告警',
    'setting-AwNwAL0oYR0R': '已读',
    'setting-HVus5GfUH7Pb': '未读',
    'setting-9vXXAxbdVTwt': '消息id',
    'setting-WGsu1ljIKA3A': '消息时间',
    'setting-ZMuDIxcSG6q2': '标题',
    'setting-LSPNITgULoBE': '分类',
    'setting-FxB9ZegGZ3PH': '消息内容',
    'setting-lyUKESzF5p4a': '状态',
    'setting-a0nEkNy8Kwka': '共 {{total}} 条'
  },
  en: {
    // index.tsx translations - Front/src/pages/home/<USER>/index.tsx
    'setting-CbXu9H3kQLu1': 'Actions',
    'setting-pvSSNe0Pb6GV': 'View Details',
    'setting-6rYKcbeyyWNd': 'Operation Successful',
    'setting-s1Fys6nl01Oa': 'Internal Messages',
    'setting-dFwgHoVapjUG': 'Delete',
    'setting-URCSl4UhYstc': 'Mark as Read',
    'setting-nlPRBf3m4wbq': 'Delete All',
    'setting-yVOCbjnOh0eN': 'Are you sure you want to delete all internal messages?',
    'setting-MJlnNIzmfDVo': 'This operation cannot be undone, please confirm before proceeding',
    'setting-WTIhRZZjYGxR': 'Confirm',
    'setting-DXTyt99HiSRG': 'Cancel',
    'setting-ZkiFtd69CQAj': 'Mark All as Read',
    'setting-wntVLAYoyPvX': 'Are you sure you want to mark all internal messages as read?',
    'setting-wuogY6Ii34jq': 'Data Quality Monitoring Alert Description',
    'setting-rDP6LQf02gE2': 'Event monitoring alerts: Starting from the daily event storage, event quality monitoring will be performed according to configured validation rules. If anomalies are found, internal messages will be sent to event owners. Detailed logs can be viewed in "Event Management - Validation Details".',
    'setting-SHeeAjJPufGR': '1. Sending frequency: After the first anomaly is detected, it will be recorded and sent after 5 minutes. Subsequently, it will be sent at intervals of 10-20-40-60 minutes;',
    'setting-QUbBpexbtrYc': '2. Cooldown period: When the 60-minute frequency is triggered, it will enter a 60-minute cooldown period. After 60 minutes, the cooldown will be lifted until the next anomaly is detected, then sent at 5-10-20-40-60 minute intervals;',

    // config.ts translations - Front/src/pages/home/<USER>/config.ts
    'setting-QDrrH5XCk9cC': 'Status',
    'setting-Tzv0U48a8ahE': 'Please select status',
    'setting-2S0PgZ4fAVKb': 'Read Messages',
    'setting-o33htOuoCYci': 'Unread Messages',
    'setting-9CBCu3rd1ixK': 'Title',
    'setting-OKlCchXGVwF0': 'Please enter',
    'setting-p66xYfnG5MAg': 'Message Time',

    // constants.tsx translations - Front/src/pages/home/<USER>/constants.tsx
    'setting-k8EmdiZDRBhG': 'Force Logout Message',
    'setting-Z45tiVHexnTr': 'Login Message',
    'setting-iLlMDzF8UOvg': 'Password Expiring Message',
    'setting-Isg8gYdxDmCb': 'Token Expired Message',
    'setting-0FuaW1P7SRG4': 'User General Message',
    'setting-LwBkMhEw7HRx': 'System General Message',
    'setting-6ecUl0HdIixJ': 'Data Quality Alert',
    'setting-AwNwAL0oYR0R': 'Read',
    'setting-HVus5GfUH7Pb': 'Unread',
    'setting-9vXXAxbdVTwt': 'Message ID',
    'setting-WGsu1ljIKA3A': 'Message Time',
    'setting-ZMuDIxcSG6q2': 'Title',
    'setting-LSPNITgULoBE': 'Category',
    'setting-FxB9ZegGZ3PH': 'Message Content',
    'setting-lyUKESzF5p4a': 'Status',
    'setting-a0nEkNy8Kwka': 'Total {{total}} items'
  }
};
