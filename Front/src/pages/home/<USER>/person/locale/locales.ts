export default {
  cn: {
    // Front/src/pages/home/<USER>/personSetting/personSetting.jsx
    'setting-dIVc6jvCGdzS': '个人设置',

    // Front/src/pages/home/<USER>/personSetting/configs.js
    'setting-zPUoXirDDQKT': '账号设置',
    'setting-3o2235LSuWIQ': '皮肤设置',

    // Front/src/pages/home/<USER>/person/list/index.jsx
    'setting-PWiJ02BiJWWD': '登录时间',
    'setting-vsHPNb7TxxIs': '登录状态',
    'setting-ILxDxXH5UQS0': '登录成功',
    'setting-XyfnIK5ttDqe': '登录失败',
    'setting-2270dW4DRj3m': '发送失败，请重试',
    'setting-fRchdu3rvACw': '发送成功',
    'setting-CMA0Fdw3Ay6F': '安全等级高',
    'setting-xuqHtgDAGSYW': '安全等级中',
    'setting-g62XPGdhBA7w': '安全等级低',
    'setting-8cyCLFViJb26': '账号设置',
    'setting-uH2zrzXfxTaj': '账号信息',
    'setting-M4FuQs93bjZ7': '编辑',
    'setting-Y15I18L0MkQp': '用户姓名',
    'setting-An45O0AOJJBD': '员工号',
    'setting-cH739OLbb6gF': '修改',
    'setting-0r9o4P31dXR9': '验证',
    'setting-XFwdm05zzYTj': '邮箱',
    'setting-650KVDOS7wuA': '已验证',
    'setting-3jbVAWvOQeSq': '未验证',
    'setting-nK5YtBdkgu0v': '手机号码',
    'setting-dh6U7BH8Devk': '修改',
    'setting-TBr81ER2EpvM': '设置',
    'setting-zqtPlr4aq6dZ': '密码',
    'setting-0CB77UdX3N69': '创建时间',
    'setting-TU0ryqK4aJb8': '最近10次访问记录',

    // Front/src/pages/home/<USER>/person/updateInfo/updateEmail.jsx
    'setting-o0ljLtadCgxf': '邮箱已存在！',
    'setting-QtRGIW5HnVSe': '修改成功',
    'setting-gLACfCh0sCwv': '密码错误，请重试！',
    'setting-gfia3ktH2V0O': '修改邮箱',
    'setting-XnayjZCqHuh2': '登录密码',
    'setting-UVMGtbaXLrAB': '请输入登录密码',
    'setting-uK6ZVJT0cwWz': '新邮箱',
    'setting-OJhLunDYt2k3': '请输入新邮箱',
    'setting-F4u8VaRSYSpf': '请输入正确的邮箱格式',

    // Front/src/pages/home/<USER>/person/updateInfo/updateMobile.jsx
    'setting-NklXc1KkxVCj': '修改成功',
    'setting-jwFLZUOMJPNT': '密码错误，请重试！',
    'setting-PUAkggPcz52J': '发送失败，请重试',
    'setting-ELofcBOMx9yL': '发送成功',
    'setting-vrQgIsFW4GBt': '修改手机号',
    'setting-mero1tF7ujZu': '登录密码',
    'setting-01EyFd13gnnc': '请输入登录密码',
    'setting-067TOHookvlc': '新手机号',
    'setting-RhwGm3k1oJFx': '请输入新手机号',
    'setting-v6bk17U3DnCq': '请输入正确手机号',
    'setting-MC5xIAu23RwH': '手机号已存在',
    'setting-YnUtir02b6tO': '验证码',
    'setting-L1u95v3mQ4yf': '请输入验证码',
    'setting-YRWhy6lJXmYv': '秒后重试',
    'setting-1R3x2tq0aQHa': '获取验证码',

    // Front/src/pages/home/<USER>/person/updateInfo/updateName.jsx
    'setting-DqODMzWaoqBr': '修改成功',
    'setting-wgfQly1CNFqg': '编辑用户姓名',
    'setting-HMoEtBYE62Lt': '用户姓名',
    'setting-m979toMKoHsu': '请输入用户姓名',
    'setting-WVPMCxGTqdV9': '最大支持32个字符，请重新输入',
    'setting-z8exow7pcynO': '请输入用户姓名',

    // Front/src/pages/home/<USER>/person/updateInfo/updatePassword.jsx
    'setting-QrscplHDAm7g': '修改密码成功',
    'setting-bhboTiA5kLNj': '设置密码成功',
    'setting-crhRarTg0nER': '两次输入的密码不相同!',
    'setting-xBCFKhdZ7Qjx': '修改密码',
    'setting-rsnehUFLdbZM': '设置密码',
    'setting-aMSTUD1lIYZB': '原密码',
    'setting-eRrKBD4EKeFd': '请输入原密码',
    'setting-ycMNzyh8tKk6': '请输入原密码',
    'setting-KyFRoPXZwItS': '新密码',
    'setting-Mjxn2AcJWpxv': '请输入新密码',
    'setting-jfEEROAlaKNc': '密码必须包含大写字母、小写字母、数字和特殊字符，长度大于{{minLength}}个字符',
    'setting-mv3Q71bPhJ9Q': '必须为6-32位的数字字母或特殊字符',
    'setting-V2D6F2NUZIem': '密码不能和登录账号（邮箱/手机号/员工号）相同',
    'setting-eQA6SwRlKNJu': '请求错误',
    'setting-SW84HbuRxSJc': '请输入新密码',
    'setting-Zl2w4hnNZE5R': '确认新密码',
    'setting-jx1Zgs6AFluk': '请输入确认新密码',
    'setting-U557L4BRbcyp': '请输入确认新密码',

    // Front/src/pages/home/<USER>/person/verifyMail/index.js"setting-qOXaYLAEMjlr": "",
    'setting-u8woPJUNPnlD': '原密码',
    'setting-IiPGQt8lucif': '操作完成',
    'setting-MzyTMIWdTcLj': '邮箱验证成功',
    'setting-3AntknQrRkfJ': '返回设置',
    'setting-JRvMdAG25IF5': '发送失败，请重试',
    'setting-ybnrJaRFQnI9': '发送成功',
    'setting-FUiIrq3Umn5I': '验证邮箱',
    'setting-s8NuGir7F4mY': '已发送验证码到邮箱 {{account}}',
    'setting-B2ImcaHNnoX7': '验证码',
    'setting-qpCG0mUqWe96': '请输入验证码!',
    'setting-fldLJ3rhHKgu': '秒后重试',
    'setting-oiXTdNe8G2fG': '获取验证码',
    'setting-cbVsmpg3zLFK': '确定'
  },
  en: {
    // personSetting/personSetting.jsx translations - Front/src/pages/home/<USER>/personSetting/personSetting.jsx
    'setting-dIVc6jvCGdzS': 'Personal Settings',

    // personSetting/configs.js translations - Front/src/pages/home/<USER>/personSetting/configs.js
    'setting-zPUoXirDDQKT': 'Account Settings',
    'setting-3o2235LSuWIQ': 'Theme Settings',

    // person/list/index.jsx translations - Front/src/pages/home/<USER>/person/list/index.jsx
    'setting-PWiJ02BiJWWD': 'Login Time',
    'setting-vsHPNb7TxxIs': 'Login Status',
    'setting-ILxDxXH5UQS0': 'Login Success',
    'setting-XyfnIK5ttDqe': 'Login Failed',
    'setting-2270dW4DRj3m': 'Send failed, please try again',
    'setting-fRchdu3rvACw': 'Send successful',
    'setting-CMA0Fdw3Ay6F': 'High security level',
    'setting-xuqHtgDAGSYW': 'Medium security level',
    'setting-g62XPGdhBA7w': 'Low security level',
    'setting-8cyCLFViJb26': 'Account Settings',
    'setting-uH2zrzXfxTaj': 'Account Information',
    'setting-M4FuQs93bjZ7': 'Edit',
    'setting-Y15I18L0MkQp': 'User Name',
    'setting-An45O0AOJJBD': 'Employee ID',
    'setting-cH739OLbb6gF': 'Modify',
    'setting-0r9o4P31dXR9': 'Verify',
    'setting-XFwdm05zzYTj': 'Email',
    'setting-650KVDOS7wuA': 'Verified',
    'setting-3jbVAWvOQeSq': 'Unverified',
    'setting-nK5YtBdkgu0v': 'Mobile Number',
    'setting-dh6U7BH8Devk': 'Modify',
    'setting-TBr81ER2EpvM': 'Set',
    'setting-zqtPlr4aq6dZ': 'Password',
    'setting-0CB77UdX3N69': 'Creation Time',
    'setting-TU0ryqK4aJb8': 'Recent 10 Access Records',

    // person/updateInfo/updateEmail.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updateEmail.jsx
    'setting-o0ljLtadCgxf': 'Email already exists!',
    'setting-QtRGIW5HnVSe': 'Modification successful',
    'setting-gLACfCh0sCwv': 'Password incorrect, please try again!',
    'setting-gfia3ktH2V0O': 'Modify Email',
    'setting-XnayjZCqHuh2': 'Login Password',
    'setting-UVMGtbaXLrAB': 'Please enter login password',
    'setting-uK6ZVJT0cwWz': 'New Email',
    'setting-OJhLunDYt2k3': 'Please enter new email',
    'setting-F4u8VaRSYSpf': 'Please enter correct email format',

    // person/updateInfo/updateMobile.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updateMobile.jsx
    'setting-NklXc1KkxVCj': 'Modification successful',
    'setting-jwFLZUOMJPNT': 'Password incorrect, please try again!',
    'setting-PUAkggPcz52J': 'Send failed, please try again',
    'setting-ELofcBOMx9yL': 'Send successful',
    'setting-vrQgIsFW4GBt': 'Modify Mobile Number',
    'setting-mero1tF7ujZu': 'Login Password',
    'setting-01EyFd13gnnc': 'Please enter login password',
    'setting-067TOHookvlc': 'New Mobile Number',
    'setting-RhwGm3k1oJFx': 'Please enter new mobile number',
    'setting-v6bk17U3DnCq': 'Please enter correct mobile number',
    'setting-MC5xIAu23RwH': 'Mobile number already exists',
    'setting-YnUtir02b6tO': 'Verification Code',
    'setting-L1u95v3mQ4yf': 'Please enter verification code',
    'setting-YRWhy6lJXmYv': 'seconds to retry',
    'setting-1R3x2tq0aQHa': 'Get Verification Code',

    // person/updateInfo/updateName.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updateName.jsx
    'setting-DqODMzWaoqBr': 'Modification successful',
    'setting-wgfQly1CNFqg': 'Edit User Name',
    'setting-HMoEtBYE62Lt': 'User Name',
    'setting-m979toMKoHsu': 'Please enter user name',
    'setting-WVPMCxGTqdV9': 'Maximum 32 characters, please re-enter',
    'setting-z8exow7pcynO': 'Please enter user name',

    // person/updateInfo/updatePassword.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updatePassword.jsx
    'setting-QrscplHDAm7g': 'Password modification successful',
    'setting-bhboTiA5kLNj': 'Password setting successful',
    'setting-crhRarTg0nER': 'The two passwords entered are different!',
    'setting-xBCFKhdZ7Qjx': 'Modify Password',
    'setting-rsnehUFLdbZM': 'Set Password',
    'setting-aMSTUD1lIYZB': 'Original Password',
    'setting-eRrKBD4EKeFd': 'Please enter original password',
    'setting-ycMNzyh8tKk6': 'New Password',
    'setting-KyFRoPXZwItS': 'Please enter new password',
    'setting-jfEEROAlaKNc': 'Password must contain uppercase letters, lowercase letters, numbers and special characters, length greater than {{minLength}} characters',
    'setting-mv3Q71bPhJ9Q': 'Must be 6-32 digits, letters or special characters',
    'setting-V2D6F2NUZIem': 'Password cannot be the same as login account (email/mobile/employee ID)',
    'setting-eQA6SwRlKNJu': 'Request error',
    'setting-Mjxn2AcJWpxv': 'Please enter new password',
    'setting-Zl2w4hnNZE5R': 'Confirm New Password',
    'setting-jx1Zgs6AFluk': 'Please enter confirm new password',
    'setting-SW84HbuRxSJc': 'Please enter confirm new password',
    'setting-U557L4BRbcyp': 'seconds to retry',

    // Front/src/pages/home/<USER>/person/verifyMail/index.js"setting-qOXaYLAEMjlr": "",
    'setting-u8woPJUNPnlD': 'Original Password',
    'setting-IiPGQt8lucif': 'Operation completed',
    'setting-MzyTMIWdTcLj': 'Email verification successful',
    'setting-3AntknQrRkfJ': 'Return to Settings',
    'setting-JRvMdAG25IF5': 'Send failed, please try again',
    'setting-ybnrJaRFQnI9': 'Send successful',
    'setting-FUiIrq3Umn5I': 'Verify Email',
    'setting-s8NuGir7F4mY': 'Sent verification code to email {{account}}',
    'setting-B2ImcaHNnoX7': 'Verification Code',
    'setting-qpCG0mUqWe96': 'Please enter verification code!',
    'setting-fldLJ3rhHKgu': 'seconds to retry',
    'setting-oiXTdNe8G2fG': 'Get Verification Code',
    'setting-cbVsmpg3zLFK': 'Confirm'
  }
};
