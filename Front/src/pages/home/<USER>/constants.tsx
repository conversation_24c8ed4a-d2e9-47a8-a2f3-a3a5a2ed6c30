import dayjs from 'dayjs';
import { MessageType } from './types';
import { t } from '@/utils/translation';

export const STATUS_MAP: Record<MessageType, string> = {
  FORCE_LOGIN_OUT: t('setting-k8EmdiZDRBhG'),
  LOGIN: t('setting-Z45tiVHexnTr'),
  PASSWORD_EXPIRE: t('setting-iLlMDzF8UOvg'),
  TOKEN_EXPIRE: t('setting-Isg8gYdxDmCb'),
  USER_GENERAL_MESSAGE: t('setting-0FuaW1P7SRG4'),
  GENERAL_MESSAGE: t('setting-LwBkMhEw7HRx'),
  EVENT_JUMP_MESSAGE: t('setting-6ecUl0HdIixJ')
};

const statusMap = {
  READ: t('setting-AwNwAL0oYR0R'),
  UNREAD: t('setting-HVus5GfUH7Pb')
};

export const TABLE_COLUMNS = [
  {
    title: t('setting-9vXXAxbdVTwt'),
    dataIndex: 'msgRecordId',
    width: 80
  },
  {
    title: t('setting-WGsu1ljIKA3A'),
    dataIndex: 'createTime',
    width: 200,
    sorter: (a: { createTime: number }, b: { createTime: number }) => a.createTime - b.createTime,
    defaultSortOrder: 'descend',
    render: (text: number) => dayjs(text).format('YYYY-MM-DD HH:mm:ss')
  },
  {
    title: t('setting-ZMuDIxcSG6q2'),
    dataIndex: 'title',
    width: 150
  },
  {
    title: t('setting-LSPNITgULoBE'),
    dataIndex: 'messageType',
    width: 150,
    render: (text: MessageType) => STATUS_MAP[text]
  },
  {
    title: t('setting-FxB9ZegGZ3PH'),
    dataIndex: 'content',
    render: (text: string) => (
      <div
        title={text}
        style={{
          overflow: 'hidden'
        }}
      >
        <pre
          style={{
            margin: 0,
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 10,
            WebkitBoxOrient: 'vertical',
            maxHeight: '200px',
            lineHeight: '20px'
          }}
        >
          {text}
        </pre>
      </div>
    )
  },
  {
    title: t('setting-lyUKESzF5p4a'),
    dataIndex: 'status',
    width: 100,
    render: (text: 'READ' | 'UNREAD') => statusMap[text]
  }
];

export const PAGINATION_CONFIG = {
  showSizeChanger: true,
  showLessItems: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total: number) => t('setting-a0nEkNy8Kwka', { total }),
  showQuickJumper: true,
  defaultPageSize: 10
};
