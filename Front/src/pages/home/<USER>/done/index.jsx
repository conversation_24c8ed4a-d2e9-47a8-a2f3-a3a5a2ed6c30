import { Modal, Space, Table, Tooltip } from 'antd';
import TableSearch from 'components/bussinesscoms/tableSearch/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import UserService from 'service/UserService';
import MyToDoListService from 'service/myToDoListService';
import { elements, initHistoryParam, initParam } from '../config';
import { t } from '@/utils/translation';

import myToDoListService from '../../../../service/myToDoListService';
import '../index.scss';

const userService = new UserService();

const pagination = {
  showTotal: (totals) => t('setting-dTfih21LwAyo', { totals }),
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const hisToryPagination = {
  showTotal: (totals) => t('setting-dTfih21LwAyo', { totals }),
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const statusList = {
  RUNNING: t('setting-O7MOlpe3qY7F'),
  PASS: t('setting-yYg6DIfOjM11'),
  REJECT: t('setting-2CGzOnrlLV3n'),
  BACKOUT: t('setting-VUD9MZHeCQWG'),
  CANCEL: t('setting-EtVVaAMzxzWm'),
  DRAFT: t('setting-ihwW7h6KGkH2')
};
const detailUrlList = {
  CAMPAIGN: '/aimarketer/home/<USER>',
  SEGMENT: '/aimarketer/home/<USER>/userGroup'
};

export default function MyToDoListDone({ props, tabKey, dictTypeList, userList }) {
  const {
    location: { state }
  } = props;

  const [dataSource, setDataSource] = useState([]);
  const [historyTableSource, setHistoryTableSource] = useState([]);
  const [userId, setUserId] = useState(undefined);
  const [loading, setLoading] = useState(false);
  const [param, setParam] = useState(_.cloneDeep(state?.paramDone || initParam));
  const [historyParam, setHistoryParam] = useState(initHistoryParam);
  const [logStatus, setLogStatus] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);

  const renderOptions = (record) => (
    <Space size={16}>
      <a onClick={() => onColumnActionClick('log', record)} type="link">
        {t('setting-Fk3XSUBBKSRj')}
      </a>
    </Space>
  );
  const columns = [
    {
      title: t('setting-0Hxiy8270JEO'),
      key: 'approvalNo',
      dataIndex: 'approvalNo',
      sorter: true,
      width: 200
    },
    {
      title: t('setting-os2cT70oD9m2'),
      key: 'contentName',
      width: 200,
      dataIndex: 'contentName',
      ellipsis: true,
      render: (text, record) => (
        <Tooltip title={text}>
          <a onClick={() => onColumnActionClick('detail', record)}>{text}</a>
        </Tooltip>
      )
    },
    {
      title: t('setting-KdBgpjlIFbHd'),
      key: 'contentType',
      width: 150,
      dataIndex: 'contentType',
      render: (text) => <span>{dictTypeList.find((item) => item.value === text)?.label}</span>
    },
    {
      title: t('setting-Nip5KRu5hlK1'),
      key: 'promoterName',
      width: 150,
      dataIndex: 'promoterName'
    },
    {
      title: t('setting-bVj3Vgvn1bCT'),
      width: 200,
      dataIndex: 'createTime',
      sorter: true,
      key: 'createTime',
      render: (text, record) => (record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: t('setting-D1PQtuGlweYg'),
      width: 100,
      key: 'status',
      render: (text) => <span>{statusList[text.status]}</span>
    },
    {
      title: t('setting-sbffRb2ZS4K0'),
      key: 'opinion',
      width: 200,
      render: (record) => (
        <Tooltip title={record.opinion ? record.opinion : ''}>
          <span className="optionWrap">{record.opinion ? record.opinion : '-'}</span>
        </Tooltip>
      )
    },
    {
      title: t('setting-4jkm2leY0arn'),
      key: 'approverName',
      width: 100,
      render: (record) => <span>{record.approverName ? record.approverName : '-'}</span>
    },
    {
      title: t('setting-aRpmjtv2J1GM'),
      width: 200,
      dataIndex: 'approvalTime',
      sorter: true,
      key: 'approvalTime',
      render: (text, record) => (record.approvalTime ? dayjs(record.approvalTime).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: t('setting-z2rOwTLKfp9o'),
      width: 100,
      fixed: 'right',
      render: (text, record) => <Space>{renderOptions(record)}</Space>
    }
  ];

  const columnsHistory = [
    {
      title: t('setting-Nip5KRu5hlK1'),
      key: 'promoterName',
      width: 150,
      dataIndex: 'promoterName',
      render: (text, record) => (
        <div>{(record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT' ? text : '-'}</div>
      )
    },
    {
      title: t('setting-bVj3Vgvn1bCT'),
      width: 200,
      dataIndex: 'createTime',
      sorter: true,
      key: 'createTime',
      render: (text, record) =>
        (record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT'
          ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-'
    },
    {
      title: t('setting-D1PQtuGlweYg'),
      width: 100,
      key: 'status',
      render: (text) => <span>{statusList[text.status]}</span>
    },
    {
      title: t('setting-4jkm2leY0arn'),
      key: 'approverName ',
      width: 100,
      render: (record) => (
        // <span>{record.approverName ? record.approverName : '-'}</span>
        <span>
          {(record.status === 'PASS' || record.status === 'REJECT' || record.status === 'BACKOUT') &&
          record.approverName
            ? record.approverName
            : '-'}
        </span>
      )
    },
    {
      title: t('setting-sbffRb2ZS4K0'),
      key: 'opinion',
      width: 240,
      render: (record) => (
        // <Tooltip title={record.opinion ? record.opinion : ''}>
        //   <span className="optionWrap">
        //     {record.opinion ? record.opinion : '-'}
        //   </span>
        // </Tooltip>
        <Tooltip
          title={(record.status === 'PASS' || record.status === 'REJECT') && record.opinion ? record.opinion : ''}
        >
          <span className="optionWrap">
            {(record.status === 'PASS' || record.status === 'REJECT') && record.opinion ? record.opinion : '-'}
          </span>
        </Tooltip>
      )
    },
    {
      title: t('setting-aRpmjtv2J1GM'),
      width: 200,
      dataIndex: 'approvalTime',
      sorter: true,
      key: 'approvalTime',
      render: (text, record) =>
        // record.approvalTime
        //   ? dayjs(record.approvalTime).format('YYYY-MM-DD HH:mm:ss')
        //   : '-'
        (record.status === 'PASS' || record.status === 'REJECT' || record.status === 'BACKOUT') && record.approvalTime
          ? dayjs(record.approvalTime).format('YYYY-MM-DD HH:mm:ss')
          : '-'
    }
  ];

  useEffect(() => {
    const init = async () => {
      const { id } = await userService.getCurrentUser();

      setUserId(id);
    };
    init();
  }, []);

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const handleHistoryTableChange = (lastpagination, filtersArg, sorter) => {
    historyParam.page = lastpagination.current;
    historyParam.size = lastpagination.pageSize;
    if (sorter.field) {
      historyParam.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setHistoryParam({ ...historyParam });
  };

  useEffect(() => {
    const getTableData = async () => {
      setLoading(true);

      if (userId) {
        const finalParam = _.cloneDeep(param);
        finalParam.search = [
          ...finalParam.search,
          {
            operator: 'EQ',
            propertyName: 'projectId',
            value: localStorage.getItem('projectId')
          },
          { operator: 'IN', propertyName: 'status', value: 'PASS,REJECT' },
          {
            operator: 'EQ',
            propertyName: 'approverId',
            value: Number(localStorage.getItem('userId'))
          },
          {
            operator: 'EQ',
            propertyName: 'delFlag',
            value: false
          },
          {
            operator: 'EQ',
            propertyName: 'approvalType',
            value: 'ACTIVITI'
          }
        ];

        const result = await MyToDoListService.queryV2(finalParam);
        pagination.total = result.totalElements;
        pagination.current = param.page;
        pagination.pageSize = param.size;
        props.history.replace({
          state: { ...state, paramRunning: param, tabKey }
        });
        setDataSource(result.content);
      }
      setLoading(false);
    };
    tabKey === '3' && getTableData();
  }, [param, tabKey, userId]);

  useEffect(() => {
    const getHistoryTableData = async () => {
      setHistoryLoading(true);
      const finalParam = _.cloneDeep(historyParam);
      const result = await MyToDoListService.processHistory(finalParam);
      hisToryPagination.total = result.totalElements;
      hisToryPagination.current = historyParam.page;
      hisToryPagination.pageSize = historyParam.size;
      setHistoryTableSource(result.content.filter((item) => item.status !== 'PENDING'));
      setLoading(false);
      setHistoryLoading(false);
    };
    getHistoryTableData();
  }, [historyParam]);

  const hideLogModal = () => {
    setLogStatus(false);
  };

  const onColumnActionClick = async (key, record) => {
    const { contentId, contentType, contentUrl, mark, approvalNo, contentName, createUserId, status } = record;
    if (key === 'detail') {
      if (contentType === 'MARKET_WORKS') {
        const result = await myToDoListService.getAuthorization({
          loginId: userId,
          projectId: localStorage.getItem('projectId')
        });

        const newUrl =
          contentUrl.indexOf('?') >= 0
            ? `${contentUrl}&Authorization=${result}`
            : `${contentUrl}?Authorization=${result}`;
        window.open(newUrl, '_blank');
      } else if (mark === 'INSIDE') {
        props.history.push(
          `${detailUrlList[contentType]}/detail?id=${contentId}&definition=${true}&status=${true}&currentApproval=DONE&approvalType=ACTIVITI`
        );
      } else {
        const newContentUrl = contentUrl.includes('?') ? `${contentUrl}&isTag=true` : `${contentUrl}?isTag=true`;
        const pathName = `/aimarketer/home/<USER>
        props.history.push(pathName);
      }
    } else if (key === 'log') {
      // showLogModal(record.id);
      props.history.push(`/aimarketer/home/<USER>
        contentType,
        approvalNo,
        contentName,
        approvalStatus: status,
        createUserId,
        contentId,
        mark,
        contentUrl,
        currentApproval: 'DONE',
        approvalType: 'ACTIVITI'
      });
    }
  };
  return (
    <div className="todoList">
      <div className="search">
        <TableSearch
          elements={elements(3, userList, dictTypeList)}
          onChange={(data) => setParam({ ...param, ...data })}
          span={8}
          initialValues={state?.paramDone?.search}
        />
      </div>
      <div className="content">
        <div className="tableWrap">
          <Table
            dataSource={dataSource}
            columns={columns}
            loading={loading}
            onChange={handleTableChange}
            rowKey="id"
            pagination={pagination}
            scroll={{ x: 1300 }}
          />
        </div>

        <Modal
          title={t('setting-Fk3XSUBBKSRj')}
          className="processHistoryModalWrap"
          open={logStatus}
          destroyOnClose
          confirmLoading={historyLoading}
          footer={null}
          onCancel={hideLogModal}
        >
          <div>
            <Table
              dataSource={historyTableSource}
              pagination={hisToryPagination}
              columns={columnsHistory}
              loading={historyLoading}
              onChange={handleHistoryTableChange}
              scroll={{ x: 400 }}
              rowKey="id"
            />
          </div>
        </Modal>
      </div>
    </div>
  );
}
