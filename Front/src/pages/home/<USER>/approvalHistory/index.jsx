import { Spin, Tag, Timeline } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { t } from '@/utils/translation';

import { statusColor, statusList, statusV2List } from '../config';

const ApprovalHistroy = (props) => {
  const { loading, approveDetailData, approvalType } = props;
  return (
    <>
      <div
        className=" h-[56px] flex items-center font-[600] text-[16px] pl-[24px]"
        style={{ borderBottom: '1px solid #F0F0F0' }}
      >
        {t('approve-vLvj1oyf2CfS')}
      </div>
      <Spin spinning={loading}>
        <div className="p-[24px]" style={{ height: 'calc(100% - 56px)' }}>
          <div>
            <Timeline>
              {approveDetailData.map((item, index) => {
                return approvalType === 'ACTIVITI' ? (
                  <Timeline.Item color="var(--ant-primary-color)" key={index}>
                    <div className="flex gap-[40px]">
                      {item.userInfo ? (
                        item.userInfo.map((userItem) => (
                          <div key={userItem.id}>
                            <div className="font-[600] text-[16px] mb-[8px]">{statusV2List[userItem.status]}</div>
                            {userItem.status === 'BACKOUT' ||
                            userItem.status === 'START' ||
                            userItem.status === 'STOP' ||
                            userItem.status === 'ERROR' ? null : (
                              <div>
                                <div className="mb-[8px]">
                                  {userItem.status === 'RUNNING'
                                    ? t('approve-Ls5atnDJq4QP')
                                    : t('approve-yQHu47Kv1Rab')}
                                  ：{userItem.status === 'RUNNING' ? userItem.promoterName : userItem.approverName}
                                  {userItem.status === 'PASS' || userItem.status === 'REJECT' ? (
                                    <Tag color={userItem.status === 'PASS' ? 'green' : 'red'} className="ml-[8px]">
                                      {userItem.status === 'PASS'
                                        ? t('approve-6aosIAIA2EWO')
                                        : t('approve-eHUJ6pO5URgq')}
                                    </Tag>
                                  ) : null}
                                </div>
                                <div
                                  style={
                                    userItem.status === 'PASS' || userItem.status === 'REJECT'
                                      ? { marginBottom: 8 }
                                      : {}
                                  }
                                >
                                  {userItem.status === 'RUNNING'
                                    ? t('approve-SwGw51euOF5f')
                                    : t('approve-JH0a0WmVuoX5')}
                                  ：
                                  {dayjs(
                                    userItem.status === 'RUNNING' ? userItem.createTime : userItem.approvalTime
                                  ).format('YYYY-MM-DD HH:mm:ss')}
                                </div>
                                {userItem.status === 'PASS' || userItem.status === 'REJECT' ? (
                                  <div>
                                    {t('approve-29HTiunDsGyy')}：{userItem.opinion || '-'}
                                  </div>
                                ) : null}
                              </div>
                            )}
                          </div>
                        ))
                      ) : (
                        <div>
                          <div
                            className="font-[600] text-[16px] mb-[8px]"
                            style={item.status === 'ERROR' ? { color: '#FF4D4F' } : {}}
                          >
                            {item.status === 'ERROR' && item.errorInfo
                              ? `${statusV2List[item.status]}: ${item.errorInfo}`
                              : statusV2List[item.status]}
                          </div>
                          {item.status === 'BACKOUT' ||
                          item.status === 'START' ||
                          item.status === 'STOP' ||
                          item.status === 'ERROR' ? null : (
                            <div>
                              <div className="mb-[8px]">
                                {item.status === 'RUNNING' ? t('approve-Ls5atnDJq4QP') : t('approve-yQHu47Kv1Rab')}：
                                {item.status === 'RUNNING' ? item.promoterName : item.approverName}
                                {item.status === 'PASS' || item.status === 'REJECT' ? (
                                  <Tag color={item.status === 'PASS' ? 'green' : 'red'} className="ml-[8px]">
                                    {item.status === 'PASS' ? t('approve-6aosIAIA2EWO') : t('approve-eHUJ6pO5URgq')}
                                  </Tag>
                                ) : null}
                              </div>
                              <div
                                style={item.status === 'PASS' || item.status === 'REJECT' ? { marginBottom: 8 } : {}}
                              >
                                {item.status === 'RUNNING' ? t('approve-SwGw51euOF5f') : t('approve-JH0a0WmVuoX5')}：
                                {dayjs(item.status === 'RUNNING' ? item.createTime : item.approvalTime).format(
                                  'YYYY-MM-DD HH:mm:ss'
                                )}
                              </div>
                              {item.status === 'PASS' || item.status === 'REJECT' ? (
                                <div>
                                  {t('approve-29HTiunDsGyy')}：{item.opinion || '-'}
                                </div>
                              ) : null}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </Timeline.Item>
                ) : (
                  <Timeline.Item color={statusColor[item.status]} key={item.id}>
                    <div>
                      <div className=" font-[600] text-[16px] mb-[8px]">{statusList[item.status]}</div>
                      {item.status === 'BACKOUT' || item.status === 'CANCEL' ? null : (
                        <div>
                          <div className="mb-[8px]">
                            {item.status === 'RUNNING' ? t('approve-Ls5atnDJq4QP') : t('approve-yQHu47Kv1Rab')}：
                            {item.status === 'RUNNING' ? item.promoterName : item.approverName}
                          </div>
                          <div style={item.status === 'PASS' || item.status === 'REJECT' ? { marginBottom: 8 } : {}}>
                            {item.status === 'RUNNING' ? t('approve-SwGw51euOF5f') : t('approve-JH0a0WmVuoX5')}：
                            {dayjs(item.status === 'RUNNING' ? item.createTime : item.approvalTime).format(
                              'YYYY-MM-DD HH:mm:ss'
                            )}
                          </div>
                          {item.status === 'PASS' || item.status === 'REJECT' ? (
                            <div>
                              {t('approve-29HTiunDsGyy')}：{item.opinion || '-'}
                            </div>
                          ) : null}
                        </div>
                      )}
                    </div>
                  </Timeline.Item>
                );
              })}
            </Timeline>
          </div>
        </div>
      </Spin>
    </>
  );
};

export default ApprovalHistroy;
