import { Spin, Tree } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import User360 from 'service/User360';
import './tagSelector.scss';

interface TagSelectorProps {
  value: any[];
  onChange: (tags: any[]) => void;
  listWrapperStyle?: React.CSSProperties;
  scenario: string;
}

interface TreeNode {
  title: string;
  key: string;
  children?: TreeNode[];
  isLeaf?: boolean;
  tagValue?: any;
  icon?: React.ReactNode;
}

interface CategoryResponse {
  categoryList?: Array<{
    id: number;
    name: string;
  }>;
  userLabels?: Array<{
    id: string;
    displayName: string;
    [key: string]: any;
  }>;
}

const user360 = new User360();

const TagSelector: React.FC<TagSelectorProps> = ({ value = [], onChange, listWrapperStyle, scenario }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<TreeNode[]>([]);

  const getAsyncData = useCallback(async (key: string): Promise<TreeNode[]> => {
    try {
      setLoading(true);
      const categoryId = parseInt(key.split('.')[1]);
      const result: CategoryResponse = await user360.findAllCategory({
        categoryId,
        scenario
      });

      const data: TreeNode[] = [];

      result.categoryList?.forEach((category) => {
        data.push({
          title: category.name,
          key: `key.${category.id}`
        });
      });

      result.userLabels?.forEach((label) => {
        data.push({
          title: label.displayName,
          key: label.id,
          isLeaf: true,
          tagValue: label,
          icon: <></>
        });
      });

      return data;
    } catch (error) {
      console.error('Error fetching data:', error);
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  const updateTreeData = useCallback((list: TreeNode[], key: string, children: TreeNode[]): TreeNode[] => {
    return list.map((node) => {
      if (node.key === key) {
        return { ...node, children };
      }
      if (node.children) {
        return {
          ...node,
          children: updateTreeData(node.children, key, children)
        };
      }
      return node;
    });
  }, []);

  const onSelect = useCallback(
    (keys: React.Key[], info: any) => {
      if (!info.node) return;

      if (info.node.isLeaf) {
        const existingIndex = value.findIndex((item) => item.title === info.node.title);
        if (existingIndex === -1) {
          onChange([...value, info.node]);
        }
      } else {
        onChange(value.filter((item) => item.title !== info.node.title));
      }
    },
    [value, onChange]
  );

  const onLoadData = useCallback(
    async ({ key, children }: any) => {
      if (children) return;

      const data = await getAsyncData(key);
      setTreeData((origin) => updateTreeData(origin, key, data));
    },
    [getAsyncData, updateTreeData]
  );

  useEffect(() => {
    const initializeData = async () => {
      const data = await getAsyncData('key.0');
      setTreeData(data);
    };

    initializeData();
  }, [getAsyncData]);

  return (
    <div className="downloadSegmentTag">
      <div className="wrapper">
        <div className="listWrapper" style={listWrapperStyle}>
          <Spin spinning={loading}>
            <Tree.DirectoryTree
              className="fieldDirectoryTree"
              onSelect={onSelect}
              loadData={onLoadData}
              treeData={treeData}
            />
          </Spin>
        </div>
      </div>
    </div>
  );
};

export default TagSelector;
