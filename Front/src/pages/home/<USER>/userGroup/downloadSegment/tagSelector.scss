.downloadSegmentTag {

  .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected {
    color: rgba(0, 0, 0, 0.85);
  }

  .ant-tree.ant-tree-directory .ant-tree-treenode-selected:before {
    background-color: unset !important;
  }

  .site-tree-search-value {
    color: #f50;
  }

  .ant-tree-title {
    white-space: nowrap;
    max-width: 200px;
  }

  .title {
    margin-bottom: 4px;
  }

  .title::before {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }

  .ant-form {
    .ant-form-item {
      display: flex;
      flex-direction: column;
    }
  }

  .wrapper {
    display: flex;
    border: 1px solid #F0F0F0;
    height: calc(100% - 44px);
    overflow: hidden;

    .listWrapper {
      // height: 234px;
      width: 100%;
      // height: calc(100% - 102px);
      overflow-y: auto;

      .listTitle {
        height: 40px;
        line-height: 40px;
        margin-left: 15px;
        // border-bottom: 1px solid black;
      }

      .ant-tree {
        padding: 0 16px;
        overflow-x: hidden;
      }

      .listItem {
        display: flex;
        padding-left: 16px;
        height: 48px;
        line-height: 48px;
        font-style: normal;
        font-weight: 400;

        .ant-checkbox {
          margin-right: 16px;
        }

        // border-bottom: 1px solid #F0F0F0;
      }

      .ant-tree-list {
        .ant-tree-treenode {
          height: 40px;

          span {
            line-height: 40px;
          }
        }
      }
    }

    .ant-divider {
      height: 100%;
    }

    .selectArea {
      width: 49%;

      // max-height: 500px;
      .title {
        height: 54px;
        line-height: 54px;
        padding-left: 16px;
        font-style: normal;
        font-weight: 400;
        color: #000000A6;
      }

      .header {
        padding-left: 16px;
        background: #FAFAFA;
        height: 48px;
        line-height: 48px;
        font-style: normal;
        font-weight: 500;

        .ant-checkbox {
          margin-right: 16px;
        }

        border-bottom: 1px solid #F0F0F0;
      }

    }

    .selectedArea {
      width: 49%;

      // max-height: 500px;
      .title {
        height: 54px;
        line-height: 54px;
        padding-left: 16px;
        font-style: normal;
        font-weight: 400;
        color: #000000A6;
      }

      .header {
        padding-left: 16px;
        background: #FAFAFA;
        height: 48px;
        line-height: 48px;
        font-style: normal;
        font-weight: 500;

        .ant-checkbox {
          margin-right: 16px;
        }

        border-bottom: 1px solid #F0F0F0;
      }

      .listWrapper {
        // height: 234px;
        height: calc(100% - 102px);
        overflow-y: auto;

        .listItem {
          padding-left: 16px;
          height: 48px;
          line-height: 48px;
          font-style: normal;
          font-weight: 400;

          .ant-checkbox {
            margin-right: 16px;
          }

          border-bottom: 1px solid #F0F0F0;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .anticon-close {
            margin-right: 24px;
            cursor: pointer;
          }
        }
      }
    }
  }
}