import { Divider, Empty, Modal, Typography } from 'antd';
import React from 'react';

import dayjs from 'dayjs';
import _ from 'lodash';
import DataEngineService from 'service/dataEngineService';
import FunnelAnalysis from 'service/funnelAnalysis';
import UserGroupService from 'service/UserGroupService';
import { t } from 'utils/translation';
import { ActionCollective, Complex } from 'wolf-static-cpnt';
// import ActionCollective from '../../../create/actioncollection/filterCondition/actioncollective/actioncollective';
import CampaignDetail from '../../../create/campaign/selectCampaign/detail';
import MyFilter from '../../../create/conditional/filterCondition/myFilter/MyFilter';
import { getString } from '../../config';
import './index.scss';

const { Title } = Typography;
const dataEngineService = new DataEngineService();
const userGroupService = new UserGroupService();

// const dataProvider = {
//   getPropertyList: async (name) => {
//     const propertyList = await dataEngineService.propertyList({
//       name,
//       eventId: 0
//     });
//     return propertyList;
//   },
//   getPropertyEnumList: async (tableId, schemaId) => {
//     const propertyItem = await dataEngineService.findFilterEnum({
//       tableId,
//       schemaId
//     });
//     return propertyItem;
//   }
// };

const dataProvider = {
  getPropertyList: async (name, eventId) => {
    const propertyList = await dataEngineService.propertyList({
      name,
      eventId: eventId || 0
    });
    return propertyList;
  },
  getPropertyEnumList: async (tableId, schemaId) => {
    const propertyItem = await dataEngineService.findFilterEnum({
      tableId,
      schemaId
    });
    return propertyItem;
  },

  getEventList: async (name) =>
    userGroupService.getEventList({
      size: 20,
      page: 1,
      search: [{ operator: 'LIKE', propertyName: 'name', value: name }],
      sorts: [{ direction: 'desc', propertyName: 'updateTime' }]
    }),
  getEventPropertyList: async (name, eventId) =>
    userGroupService.getEventPropertyList({
      name,
      eventId
    }),
  getEventCountLogsByProjectId: async () =>
    FunnelAnalysis.getEventCountLogsByProjectId({
      projectId: localStorage.getItem('projectId')
    }),
  getGroupList: async () =>
    userGroupService.list([
      {
        operator: 'EQ',
        propertyName: 'status',
        value: 'NORMAL'
      },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: window.getDeptId()
      }
    ])
  // getSegmentList: async (name) => {
  //   let resultData = await userGroupService.getSegmentList({ size: 20, page: 1, search: [{ operator: 'LIKE', propertyName: 'name', value: name }, { operator: 'EQ', propertyName: 'status', value: 'NORMAL' }, { operator: 'EQ', propertyName: 'calcStatus', value: 'SUC' }, { operator: 'EQ', propertyName: 'scenario.id', value: props.scenario.id }], sorts: [{ direction: 'desc', propertyName: 'updateTime' }] });
  //   return resultData.content;
  // }
};

const operatorMap = {
  EQ: t('portraitCenter-userGroup-detail-operator-EQ'),
  GT: t('portraitCenter-userGroup-detail-operator-GT'),
  GTE: t('portraitCenter-userGroup-detail-operator-GTE'),
  LT: t('portraitCenter-userGroup-detail-operator-LT'),
  LTE: t('portraitCenter-userGroup-detail-operator-LTE')
};

export default (props) => {
  const { data, groupList, segmentName } = props;

  const okHandle = () => {
    props.okHandle && props.okHandle();
  };

  const cancelHandle = () => {
    props.cancelHandle && props.cancelHandle();
  };

  const ruleRender = () => {
    if (data.type === 'CONDITIONAL') {
      return (
        <section>
          <h1>
            <span>{data.customerCount.toLocaleString()}</span> {t('portraitCenter-userGroup-releModal-count')}
          </h1>
          <p className="dateTime">{dayjs(data.lastCalcTime).format('YYYY-MM-DD HH:mm:ss')}</p>
          <MyFilter
            mode="detail"
            value={{
              filterInfo: data.filterInfo || {},
              label: data.label || {},
              connector: data.connector || 'AND'
            }}
          />
        </section>
      );
    } else if (data.type === 'COMPLEX') {
      return (
        <section>
          <h1>
            <span>{data.customerCount.toLocaleString()}</span> {t('portraitCenter-userGroup-releModal-count')}
          </h1>
          <p className="dateTime">{dayjs(data.lastCalcTime).format('YYYY-MM-DD HH:mm:ss')}</p>
          <Title level={4}>{t('portraitCenter-userGroup-detail-belongToSegments')}</Title>
          <Complex
            dataProvider={dataProvider}
            value={data.includeSegments || {}}
            selectList={groupList}
            mode="detail"
          />
          <div hidden={data.excludeSegments.filters.length === 0}>
            <Divider />
            <Title level={4}>{t('portraitCenter-userGroup-detail-excludeSegments')}</Title>
            <Complex
              dataProvider={dataProvider}
              value={data.excludeSegments || {}}
              selectList={groupList}
              mode="detail"
            />
          </div>
        </section>
      );
    } else if (data.type === 'CAMPAIGN') {
      return (
        <section>
          <CampaignDetail info={data} />
        </section>
      );
    } else if (data.type === 'UPLOAD') {
      return (
        <section style={{ height: 300 }}>
          <Empty
            description={
              <span style={{ fontSize: 14, color: 'var(--ant-primary-color)' }}>
                {t('portraitCenter-userGroup-releModal-uploadRule')}
              </span>
            }
          />
        </section>
      );
    } else if (data.type === 'CONDITION_AGGREGATE') {
      return (
        <section>
          <h1>
            <span>{data.customerCount.toLocaleString()}</span>人
          </h1>
          <p className="dateTime">{dayjs(data.lastCalcTime).format('YYYY-MM-DD HH:mm:ss')}</p>
          <Title level={4}>{t('portraitCenter-userGroup-detail-belongToSegments')}</Title>
          <ActionCollective
            dataProvider={dataProvider}
            value={data.includeConditionAggregate || {}}
            onChange={() => {}}
            mode="detail"
          />
          <div
            hidden={_.isEmpty(data.excludeConditionAggregate) || data.excludeConditionAggregate.filters.length === 0}
          >
            <Divider />
            <Title level={4}>{t('portraitCenter-userGroup-detail-excludeSegments')}</Title>
            <ActionCollective
              dataProvider={dataProvider}
              value={data.excludeConditionAggregate || {}}
              onChange={() => {}}
              mode="detail"
            />
          </div>
        </section>
      );
    } else if (data.type === 'SHORT_LINK') {
      return (
        <section>
          <h1>
            <span>{data.customerCount.toLocaleString()}</span> {t('portraitCenter-userGroup-releModal-count')}
          </h1>
          <p className="dateTime">{dayjs(data.lastCalcTime).format('YYYY-MM-DD HH:mm:ss')}</p>
          <div className="short-link-detail">
            <div>
              {t('portraitCenter-userGroup-detail-shortLinkTaskName')}:{data?.shortLinkInfo?.shortLinkTask?.taskName}
            </div>
            <div>
              {t('portraitCenter-userGroup-detail-shortLinkTaskId')}:{data?.shortLinkInfo?.taskId}
            </div>{' '}
          </div>
          <div className="short-link-rules">
            {_.map(data?.shortLinkInfo?.filters, (item) => {
              if (item.function === 'COUNT') {
                return (
                  <div key={`${item.name}${item.function}`} className="short-link-rule">
                    {/* {`总点击次数：${operatorMap[item.operator]} ${item.value}`} */}
                    {t('portraitCenter-userGroup-releModal-totalClicks', {
                      operator: operatorMap[item.operator],
                      value: item.value
                    })}
                  </div>
                );
              }
              return (
                <div
                  key={`${item.name}${item.function}`}
                  className="short-link-rule"
                >{`${item.name}: ${operatorMap[item.operator]} ${item.value}`}</div>
              );
            })}
          </div>
        </section>
      );
    } else if (data.type === 'FUNNEL_CHART' && data?.funnelChartInfo) {
      const value = data.funnelChartInfo.dateRange2;
      const conversion = data.funnelChartInfo.chartConfig.funnelDataQuery.conversion;
      const time1 = getString(value[0], true);
      const time2 = getString(value[1], true);
      const type = {
        MINUTE: t('global-minute'),
        HOUR: t('global-hour'),
        DAY: t('global-day')
      };
      return (
        <section>
          <h1>
            <span>{data.customerCount.toLocaleString()}</span> {t('portraitCenter-userGroup-releModal-count')}
          </h1>
          <p className="dateTime">{dayjs(data.lastCalcTime).format('YYYY-MM-DD HH:mm:ss')}</p>
          <div className="funnel-rules">
            {t('portraitCenter-userGroup-releModal-people')}:{data?.name || segmentName}
            <div>
              {t('portraitCenter-userGroup-releModal-time')}:{time1} - {time2}
            </div>
            <div>
              {t('portraitCenter-userGroup-releModal-conversionWindow')}：{conversion?.amount}
              {type[conversion?.stepTerm]}
            </div>
          </div>
        </section>
      );
    } else if (data.type === 'RETENTION_CHART' && data?.retentionChartInfo) {
      const value = data.retentionChartInfo.dateRange2;
      const stepList = data.retentionChartInfo.chartConfig.retentionAnalysisDataQuery.stepList;
      const time1 = getString(value[0], true);
      const time2 = getString(value[1], true);
      return (
        <section>
          <h1>
            <span>{data.customerCount.toLocaleString()}</span> {t('portraitCenter-userGroup-releModal-count')}
          </h1>
          <p className="dateTime">{dayjs(data.lastCalcTime).format('YYYY-MM-DD HH:mm:ss')}</p>
          <div className="funnel-rules">
            {t('portraitCenter-userGroup-releModal-people')}：{data?.name || segmentName}
            <div>
              {t('portraitCenter-userGroup-releModal-time')}：{time1} - {time2}
            </div>
            <div>
              {t('portraitCenter-userGroup-releModal-initialBehavior')}：{stepList[0].displayName}
            </div>
            <div>
              {t('portraitCenter-userGroup-releModal-retentionBehavior')}：{stepList[1].displayName}
            </div>
          </div>
        </section>
      );
    }
  };

  return (
    <Modal
      title={t('portraitCenter-userGroup-releModal-viewRule')}
      open
      width={800}
      height={480}
      onOk={okHandle}
      maskClosable
      onCancel={cancelHandle}
      className="ruleModal"
    >
      {ruleRender()}
    </Modal>
  );
};
