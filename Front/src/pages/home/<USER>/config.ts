import { t } from '@/utils/translation';

const configElements = {
  status: {
    type: 'select',
    label: t('setting-QDrrH5XCk9cC'),
    width: 12,
    operator: 'IN',
    componentOptions: {
      allowClear: true,
      placeholder: t('setting-Tzv0U48a8ahE'),
      showSearch: true,
      mode: 'multiple',
      options: [
        { name: t('setting-2S0PgZ4fAVKb'), text: t('setting-2S0PgZ4fAVKb'), value: 'READ' },
        { name: t('setting-o33htOuoCYci'), text: t('setting-o33htOuoCYci'), value: 'UNREAD' }
      ]
    }
  },
  title: {
    type: 'input',
    label: t('setting-9CBCu3rd1ixK'),
    width: 12,
    operator: 'LIKE',
    componentOptions: {
      allowClear: true,
      placeholder: t('setting-OKlCchXGVwF0')
    }
  },
  createTime: {
    type: 'dateRange',
    label: t('setting-p66xYfnG5MAg'),
    width: 12,
    operator: 'DATE_BETWEEN',
    componentOptions: {
      allowClear: true
    }
  }
};

export { configElements };
