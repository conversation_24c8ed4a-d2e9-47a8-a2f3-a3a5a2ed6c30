import { DownOutlined } from '@ant-design/icons';
import { Input, Popover, Select } from 'antd';
import _ from 'lodash';
import React, { useState } from 'react';
import type { IProps } from './config';
import './index.scss';
import MyForm from './myForm';
import { t } from 'utils/translation';

interface FormValues {
  operator?: string;
  propertyName?: string;
  connector?: string;
  value?: string | string[];
}

interface Props {
  items: IProps[];
  onChange?: (values: FormValues[]) => void;
}
export default function FilterComponents({ items, onChange }: Props) {
  const [filterVisible, setFilterVisible] = useState<boolean>(false);
  const [allValues, setAllValues] = useState<FormValues[]>([]);

  const [myFormValues, setMyFormValues] = useState<FormValues[]>([]);

  const updataForm = (values: FormValues[]) => {
    setMyFormValues(values);
    onChange && onChange([...allValues, ...values]);
  };

  const renderPopover = (components: IProps[] | undefined) => {
    return <MyForm components={components} onChange={updataForm} setFilterVisible={setFilterVisible} />;
  };

  const changeSelect = (value: string, record: IProps) => {
    const selectValue = {
      operator: record.operator,
      propertyName: record.name,
      value: value || ''
    };
    const findValue = _.find(allValues, (item) => item.propertyName === record.name);
    const newAllValues = _.cloneDeep(allValues);
    if (findValue) {
      const index = _.findIndex(allValues, (item) => item.propertyName === record.name);
      newAllValues[index] = selectValue;
    } else {
      newAllValues.push(selectValue);
    }
    setAllValues(newAllValues);
    onChange && onChange([...newAllValues, ...myFormValues]);
  };

  const renderComponents = () => {
    return items.map((item, index) => {
      if (item.type === 'select') {
        return (
          <div key={index}>
            <Select
              // style={{ width: item.width || 100 }}
              placeholder={item.connector}
              allowClear
              showSearch
              optionFilterProp="children"
              onChange={(value) => changeSelect(value, item)}
            >
              {item.options &&
                item.options.map((option, index) => (
                  <Select.Option key={index} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
            </Select>
          </div>
        );
      } else if (item.type === 'input') {
        return (
          <div key={index}>
            <Input
              // style={{ width: item.width || 100 }}
              placeholder={item.connector}
              allowClear
            />
          </div>
        );
      } else if (item.type === 'more') {
        return (
          <Popover
            getPopupContainer={() => document.querySelector('.filterComponents') as HTMLElement}
            content={() => renderPopover(item.components)}
            trigger="click"
            overlayClassName="campaignsFilterPop"
            placement="topRight"
            onOpenChange={(e) => setFilterVisible(e)}
            open={filterVisible}
            key={index}
          >
            <div className="moreFilter" onClick={() => setFilterVisible(true)}>
              {t('operationCenter-otP11NNUYub8')}
              <DownOutlined />
            </div>
          </Popover>
        );
      }
      return null;
    });
  };
  return <div className="filterComponents">{renderComponents()}</div>;
}
