import { But<PERSON>, DatePicker, Divider, Form, Select } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React from 'react';
import type { IProps } from './config';
import { t } from 'utils/translation';

const { RangePicker } = DatePicker;
interface Props {
  components: IProps[] | undefined;
  onChange?: (values: FormValues[]) => void;
  setFilterVisible?: (value: boolean) => void;
}
interface FormValues {
  operator?: string;
  propertyName?: string;
  connector?: string;
  value?: string | string[];
}
export default function MyForm({ components, onChange, setFilterVisible }: Props) {
  const [form] = Form.useForm();

  const search = async () => {
    const values = await form.validateFields();
    const formatValues = [] as FormValues[];
    _.forEach(values, (value, key) => {
      const findValue = _.find(components, (item) => item.name === key);
      switch (findValue?.name) {
        case 'createUserId':
        case 'updateUserId':
          formatValues.push({
            operator: findValue?.operator,
            propertyName: findValue?.name,
            connector: findValue?.connector,
            value: value || ''
          });
          break;
        case 'createTime':
        case 'updateTime':
          if (value) {
            const start = dayjs(value[0]).startOf('day').valueOf().toString();
            const end = dayjs(value[1]).endOf('day').valueOf().toString();
            formatValues.push({
              operator: findValue?.operator,
              propertyName: findValue?.name,
              connector: findValue?.connector,
              value: `${start},${end}` || ''
            });
          }
          break;
        default:
      }
    });
    onChange && onChange(formatValues);
    setFilterVisible && setFilterVisible(false);
  };

  const empty = () => {
    form.resetFields();
    onChange && onChange([]);
  };

  return (
    <div>
      <Form layout="vertical" form={form}>
        {components?.map((item, index) => {
          if (item.type === 'select') {
            return (
              <Form.Item key={index} name={item.name} label={item.label}>
                <Select
                  // style={{ width: item.width || 100 }}
                  className="123"
                  placeholder={t('operationCenter-Ah5YsDhnUy6u')}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                >
                  {item.options?.map((option, index) => (
                    <Select.Option key={index} value={option.value}>
                      {option.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            );
          } else if (item.type === 'date') {
            return (
              <Form.Item key={index} name={item.name} label={item.label}>
                <RangePicker style={{ width: '100%' }} allowClear />
              </Form.Item>
            );
          }
          return null;
        })}
      </Form>

      <div className="myFormFooter">
        <Divider />
        <div className="buttons">
          <Button onClick={empty}>{t('operationCenter-fuNkRywFhBNn')}</Button>
          <Button type="primary" onClick={search}>
            {t('operationCenter-HjSjQBuiQKEL')}
          </Button>
        </div>
      </div>
    </div>
  );
}
