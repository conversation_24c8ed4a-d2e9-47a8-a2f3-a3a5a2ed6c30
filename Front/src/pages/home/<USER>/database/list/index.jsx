import CheckAuth from '@/utils/checkAuth';
import { DownOutlined, FilterOutlined, FullscreenOutlined, MoreOutlined } from '@ant-design/icons';
import { Button, Col, Dropdown, Modal, Pagination, Row, Spin, Tooltip, message } from 'antd';
import QueryForList from 'components/bussinesscoms/queryforlist/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import { getString, getTime } from 'pages/home/<USER>/userGroup/detail/config';
import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import VisibilitySensor from 'react-visibility-sensor';
import UserService from 'service/UserService';
import AnalysisCenterService from 'service/analysisCenterService';
import getMenuTitle from 'utils/menuTitle';
import { MyIcon } from 'utils/myIcon';
import { t } from 'utils/translation';
import { calcPageNo } from 'utils/universal';
import Com<PERSON>hart from '../comChart/index';
import { configElements, initPagination, initParam } from '../config';
import AddChart from './addChart/index';
import './index.scss';

const stateToProps = (state) => {
  return {
    meunData: state.meunData.menus.main
  };
};

const userService = new UserService();

const AnalysisCenter = (props) => {
  const {
    location: { state }
  } = props;
  const [userId, setUserId] = useState(null);
  const [isFold, setIsFold] = useState(false);
  const [loading, setLoading] = useState(false);
  const [elements] = useState(configElements);
  const [visible, setVisible] = useState(state?.visibleState || false);
  const [list, setList] = useState([]);
  const [param, setParam] = useState(state && state.param ? _.cloneDeep(state.param) : _.cloneDeep(initParam));
  const [activeKey, setActiveKey] = useState(state && state.activeKey ? _.cloneDeep(state.activeKey) : 'ALL');
  const [pagination, setPagination] = useState(
    state && state.pagination ? _.cloneDeep(state.pagination) : _.cloneDeep(initPagination)
  );
  const [formVal, setFormVal] = useState(state && state.formVal ? _.cloneDeep(state.formVal) : {});

  const [loadedComChartIndexArr, setLoadedComChartIndexArr] = useState([]);
  const [recentSelectKey, setRecentSelectKey] = useState(
    state && state.recentKey ? _.cloneDeep(state.recentKey) : null
  );

  const uniqueAfterArr = (arr, name) => {
    const hash = {};
    return arr.reduce((acc, cru) => {
      if (!hash[cru[name]]) {
        hash[cru[name]] = { index: acc.length };
        acc.push(cru);
      } else {
        acc.splice(hash[cru[name]].index, 1, cru);
      }
      return acc;
    }, []);
  };

  const beforeunload = () => {
    props.history.replace({ state: null });
  };

  useEffect(() => {
    (async () => {
      try {
        if ((!visible && localStorage.getItem('isActivityAnalysis')) || localStorage.getItem('activityCache')) {
          localStorage.removeItem('isActivityAnalysis');
          localStorage.removeItem('activityCache');
        }
        window.addEventListener('beforeunload', beforeunload);
        const userInfo = await userService.getCurrentUser();
        // const res = await EventAnalysis.getBusiness({ businessType: 'SegmentBusinessConfig' });
        // setBussinessData(res);
        const defaultFilter = {};
        if (state && state.param) {
          for (const item of state.param.search) {
            if (item.value !== '' && item.propertyName !== 'operationTime') {
              defaultFilter[`${item.propertyName}`] = item.value;
              if (item.operator === 'DATE_BETWEEN') {
                const dateArr = item.value.split(',');
                defaultFilter[`${item.propertyName}`] = dateArr.map((item) => dayjs(parseInt(item)));
              }
            }
          }
        }
        setFormVal(defaultFilter);
        setUserId(userInfo.id);
      } catch {
        setLoading(false);
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    (async () => {
      localStorage.removeItem('timeFilter');
      try {
        setLoading(true);
        const _params = _.cloneDeep(param);
        let tableParams;

        if (activeKey === 'RECENT' || activeKey === 'FAVORITE') {
          if (activeKey === 'RECENT') {
            tableParams = {
              ..._params,
              search: uniqueAfterArr(
                [
                  ..._params.search,
                  {
                    propertyName: 'recordType',
                    operator: 'IN',
                    value: 'RECENT,FAVORITE'
                  },
                  {
                    operator: 'EQ',
                    propertyName: 'deptId',
                    value: window.getDeptId()
                  }
                ],
                'propertyName'
              )
            };
          } else {
            tableParams = {
              ..._params,
              search: uniqueAfterArr(
                [
                  ..._params.search,
                  {
                    propertyName: 'recordType',
                    operator: 'EQ',
                    value: 'FAVORITE'
                  },
                  {
                    operator: 'EQ',
                    propertyName: 'deptId',
                    value: window.getDeptId()
                  }
                ],
                'propertyName'
              )
            };
          }
        } else if (activeKey === 'MINE') {
          _params.search = uniqueAfterArr(
            [
              ..._params.search,
              { operator: 'EQ', propertyName: 'createUserId', value: userId },
              {
                operator: 'EQ',
                propertyName: 'deptId',
                value: window.getDeptId()
              }
            ],
            'propertyName'
          );
          const index = _.findIndex(_params.search, (v) => v.propertyName === 'operationTime');
          if (index !== -1) {
            _params.search.splice(index, 1);
          }
          tableParams = _params;
        } else {
          _params.search = uniqueAfterArr(
            [
              ..._params.search,
              {
                operator: 'EQ',
                propertyName: 'deptId',
                value: window.getDeptId()
              }
            ],
            'propertyName'
          );
          const index = _.findIndex(_params.search, (v) => v.propertyName === 'operationTime');
          if (index !== -1) {
            _params.search.splice(index, 1);
          }
          tableParams = _params;
        }
        tableParams?.search?.forEach((item) => {
          if (item.propertyName === 'chartType' && !_.isEmpty(item.value)) {
            item.value = item.value.join(',');
          }
        });
        let result;
        if (userId) {
          result =
            activeKey === 'ALL' || activeKey === 'MINE'
              ? await AnalysisCenterService.getChartConfigList(tableParams)
              : await AnalysisCenterService.findChartConfigNewQuery(tableParams);

          pagination.total = result.totalElements;
          pagination.current = param.page;
        }
        setList(result?.content || []);
        setLoading(false);
        props.history.replace({
          state: { ...state, pagination, formVal, activeKey, param }
        });
      } catch (error) {
        console.error(error);
        setLoading(false);
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [param, activeKey, userId]);

  const saveRecordInfo = async (id, userId, n) => {
    await AnalysisCenterService.saveUserOperationRecord({
      targetId: id,
      id: n?.recentUserOperationRecord?.id,
      targetType: 'CHART',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  const saveCollect = async (id, userId) => {
    try {
      await AnalysisCenterService.saveUserOperationRecord({
        targetId: id,
        targetType: 'CHART',
        type: 'FAVORITE',
        createUserId: userId,
        updateUserId: userId,
        createTime: dayjs().valueOf(),
        updateTime: dayjs().valueOf()
      });
      setParam({ ...param });
      message.success(t('analysisCenter-Yh7jK4lM9nB'));
    } catch (error) {
      console.error(error);
    }
  };

  const delCollect = async (id, userId, n) => {
    try {
      await AnalysisCenterService.delByCondition({
        targetId: id,
        targetType: 'CHART',
        type: 'FAVORITE',
        loginId: userId
      });

      saveRecordInfo(id, userId, n);
      setParam({ ...param });
      message.success(t('analysisCenter-ATbGOyRoT4au'));
    } catch (error) {
      console.error(error);
    }
  };

  const getRecentRange = (key) => {
    let startTime;
    const endTime = dayjs().endOf('day').valueOf();
    if (key === 'WEEK') {
      startTime = dayjs().subtract(1, 'week').startOf('day').valueOf();
    } else if (key === 'MON') {
      startTime = dayjs().subtract(1, 'months').startOf('day').valueOf();
    } else {
      startTime = dayjs().subtract(3, 'months').startOf('day').valueOf();
    }

    return `${startTime},${endTime}`;
  };

  // useEffect(() => {
  //   const init = async () => {
  //     let config = {
  //       connector: 'AND',
  //       propertyName: 'name',
  //       operator: 'LIKE',
  //       value: ''
  //     };
  //     let _campaignPagination = _.cloneDeep(campaignPagination);
  //     _campaignPagination.search.push(config);
  //     let res = await FunnelAnalysis.query2(_campaignPagination);// 营销活动
  //     const allCategoryList = await DashboardService.getAllchartBoardCategory([]);// 数据看板
  //     const chartBoardList = await DashboardService.getAllchartBoard([]);
  //     const menuList = getLevelData(chartBoardList, allCategoryList, 0);
  //     let _elements = _.cloneDeep(elements);
  //     _elements.customChartBoard.componentOptions.options = menuList;
  //     _elements.customCampaign.componentOptions.onSearch = (val) => campaignOnSearch(val);
  //     _elements.customCampaign.componentOptions.onClear = (val) => campaignOnSearch(val);
  //     _elements.customCampaign.componentOptions.options = _.map(res.content, (item) => ({ key: item.id, value: item.id, text: item.name }));
  //     setElements(_elements);
  //   };
  //   init();
  // }, []);

  // const campaignOnSearch = _.debounce(async (value) => {
  //   let config = {
  //     connector: 'AND',
  //     propertyName: 'name',
  //     operator: 'LIKE',
  //     value
  //   };
  //   let _campaignPagination = _.cloneDeep(campaignPagination);
  //   _campaignPagination.search.push(config);
  //   let res = await FunnelAnalysis.query2(_campaignPagination);
  //   let _elements = _.cloneDeep(elements);
  //   _elements.customCampaign.componentOptions.onSearch = (val) => campaignOnSearch(val);
  //   _elements.customCampaign.componentOptions.options = _.map(res.content, (item) => ({ key: item.id, value: item.id, text: item.name }));
  //   const allCategoryList = await DashboardService.getAllchartBoardCategory([]);// 数据看板
  //   const chartBoardList = await DashboardService.getAllchartBoard([]);
  //   const menuList = getLevelData(chartBoardList, allCategoryList, 0);
  //   _elements.customChartBoard.componentOptions.options = menuList;
  //   setElements(_elements);
  // }, 500);

  const queryData = useCallback(
    (data) => {
      let _search = _.cloneDeep(param.search);
      try {
        const newVal = {};
        data.forEach((n) => {
          if (n.value) {
            newVal[n.propertyName] = n.value;
          }
          if (n.propertyName === 'id') {
            _.forEach(n.value, (item) => {
              if (_.isNaN(Number(item)) || item.indexOf(' ') > -1) {
                throw new Error(t('analysisCenter-7KZxKw5KbGRl'));
              } else if (item.length > 19) {
                throw new Error(t('analysisCenter-1ADbNVtaEVXd'));
              }
            });
            n.value = _.join(n.value, ',');
          }
        });

        _search = !data.length ? [...data] : [...param.search, ...data];
        if (!data.length) {
          setFormVal({});
        }

        const _filters = _.map(_search, (item) => {
          return {
            connector: 'AND',
            propertyName: item.propertyName,
            operator: item.operator,
            value:
              item.operator === 'DATE_BETWEEN' && _.isArray(item.value)
                ? item.value.map((item) => dayjs(item).valueOf()).join(',')
                : item.value
          };
        });
        // setFormVal(newVal);
        setParam({ ...param, search: [..._filters], page: 1 });
      } catch (err) {
        message.error(err.message);
      }
    },
    [param]
  );

  const handleTableChange = (page, pageSize) => {
    const newParam = { ...param };
    newParam.page = page;
    newParam.size = pageSize;
    setPagination({ ...pagination, current: page, pageSize });
    setParam(newParam);
  };

  const delData = async (id, name) => {
    Modal.confirm({
      title: t('analysisCenter-xmt2JlB0WqS4'),
      content: (
        <p className="confirmDelete">
          {t('analysisCenter-SfavsZlmJMVQ', { name })}
          {/* <span>{val.id}</span> */}
          {/* <br /> */}
          {/* 项目ID： */}
          {/* <span>{`${projectList.find((k) => k.id === val.projectId).name} [${val.projectId}]`}</span> */}
          <br />
          {t('analysisCenter-GTvc0pSDGiMP')}
        </p>
      ),
      okText: t('analysisCenter-YCT08lBSKrDF'),
      okType: 'danger',
      cancelText: t('analysisCenter-ffqeS4fiG1h8'),
      async onOk() {
        try {
          await AnalysisCenterService.deleteChartConfigById(id);
          message.success(t('analysisCenter-Ct9KESmrDEgX'));
          const page = calcPageNo(pagination.total, param.page, param.size);
          setParam({ ...param, page });
        } catch {
          console.error(11);
        }
      },
      onCancel() {}
    });
  };

  const edit = (id, chartType, n) => {
    if (chartType === 'NEW_FUNNEL') {
      saveRecordInfo(id, userId, n);
      props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/funnelAnalysis/${id}`);
    } else if (chartType === 'RETENTION_ANALYSIS') {
      saveRecordInfo(id, userId, n);
      props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/retentionAnalysis/${id}`);
    } else if (chartType === 'EVENT_ANALYSIS') {
      saveRecordInfo(id, userId, n);
      props.history.push(`/aimarketer/home/<USER>/analysisCenter/database/eventAnalysis/${id}`);
    } else if (chartType === 'cancelCollect') {
      delCollect(id, userId, n);
    } else if (chartType === 'collect') {
      saveCollect(id, userId);
    } else {
      saveRecordInfo(id, userId, n);
      // props.history.push(
      //   `/aimarketer/home/<USER>/database/edit/${id}?fullScreen=${true}`
      // );
      localStorage.setItem('currentFlag', 'edit');
      props.history.push(`/aimarketer/home/<USER>/database/edit/${id}`, { currentFlag: 'edit' });
    }
  };

  const onClickDetail = (id, n) => {
    saveRecordInfo(id, userId, n);
    props.history.push(`/aimarketer/home/<USER>/database/detail/${id}?fullScreen=${true}`);
  };

  const onVisibilityChange = (isVisible, n, index) => {
    if (isVisible && !_.isEqual(_.uniq([...loadedComChartIndexArr, index]), loadedComChartIndexArr)) {
      setLoadedComChartIndexArr((prevState) => {
        return _.uniq([...prevState, index]);
      });
    }
  };

  const boardMenu = () => {
    return [
      {
        label: (
          <div className="boardSubMenuWrap" onClick={analysis}>
            <div className="menuIconWrap">
              <MyIcon type="icon-icon-box" className="chartIcon" />
            </div>
            <div className="menuTextWrap">
              <div className="title">{t('analysisCenter-xAhSszLLUkG9')}</div>
              <div className="menuDesc">{t('analysisCenter-t97gJQt9SAF7')}</div>
            </div>
          </div>
        ),
        key: '1'
      },
      {
        label: (
          <div className="boardSubMenuWrap" onClick={funnelAnalysis}>
            <div className="menuIconWrap">
              <MyIcon type="icon-a-icon-funnelplot" className="chartIcon" />
            </div>
            <div className="menuTextWrap">
              <div className="title">{t('analysisCenter-hsGJcAF9TNtc')}</div>
              <div className="menuDesc">{t('analysisCenter-VXpcZGnvwi0D')}</div>
            </div>
          </div>
        ),
        key: '2'
      },
      {
        label: (
          <div className="boardSubMenuWrap" onClick={retentionAnalysis}>
            <div className="menuIconWrap">
              <MyIcon type="icon-icon-retaion" className="chartIcon" />
            </div>
            <div className="menuTextWrap">
              <div className="title">{t('analysisCenter-D4WrwslKrZ4e')}</div>
              <div className="menuDesc">{t('analysisCenter-ih2dPUtm8rQ7')}</div>
            </div>
          </div>
        ),
        key: '3'
      },
      {
        label: (
          <div className="boardSubMenuWrap" onClick={eventAnalysis}>
            <div className="menuIconWrap">
              <MyIcon type="icon-icon-shijianfenxi" className="chartIcon" />
            </div>
            <div className="menuTextWrap">
              <div className="title">{t('analysisCenter-sYbVOzckNs8b')}</div>
              <div className="menuDesc">{t('analysisCenter-W2zht4RC9ij1')}</div>
            </div>
          </div>
        ),
        key: '4'
      }
    ];
  };

  const analysis = () => {
    state.scenarioId = undefined;
    state.campaignId = undefined;
    setVisible(true);
  };

  const funnelAnalysis = () => {
    props.history.push('/aimarketer/home/<USER>/analysisCenter/database/funnelAnalysis');
  };

  const retentionAnalysis = () => {
    props.history.push('/aimarketer/home/<USER>/analysisCenter/database/retentionAnalysis');
  };

  const eventAnalysis = () => {
    // message.info('事件分析功能开发中，敬请期待');
    props.history.push('/aimarketer/home/<USER>/analysisCenter/database/eventAnalysis');
  };

  const onTabsClick = (key) => {
    if (key === 'ALL' || key === 'MINE') {
      const _search = _.filter(param.search, (n) => n.propertyName !== 'recordType');
      setParam({
        ...param,
        search: _search,
        sorts: [{ direction: 'desc', propertyName: 'updateTime' }],
        page: 1
      });
      setActiveKey(key);
    } else {
      setParam({
        ...param,
        sorts: [{ direction: 'desc', propertyName: 'operationTime' }],
        page: 1
      });
      setActiveKey(key);
    }
  };

  const onRecentMenuChange = (key) => {
    setRecentSelectKey(key);
    const _param = _.cloneDeep(param);
    const index = _.findIndex(_param.search, (v) => v.propertyName === 'operationTime');
    if (index !== -1) {
      _param.search.splice(index, 1);
    }

    setParam({
      ...param,
      search: [
        ..._param.search,
        {
          propertyName: 'operationTime',
          operator: 'DATE_BETWEEN',
          value: getRecentRange(key)
        }
      ]
    });

    props.history.replace({ state: { ...state, recentKey: key } });
  };

  const items = [
    {
      label: (
        <a rel="noopener noreferrer" onClick={() => onRecentMenuChange('WEEK')}>
          {t('analysisCenter-2cCHWf2ZiUrd')}
        </a>
      ),
      key: 'WEEK'
    },
    {
      label: (
        <a rel="noopener noreferrer" onClick={() => onRecentMenuChange('MON')}>
          {t('analysisCenter-TFo3SW8foezD')}
        </a>
      ),
      key: 'MON'
    },
    {
      label: (
        <a rel="noopener noreferrer" onClick={() => onRecentMenuChange('TREEEMON')}>
          {t('analysisCenter-PAqvYV0JEcoV')}
        </a>
      ),
      key: 'TREEEMON'
    },
    {
      label: (
        <a rel="noopener noreferrer" onClick={() => onRecentMenuChange('SIXMON')}>
          {t('analysisCenter-A3lwSiIPFjrp')}
        </a>
      ),
      key: 'SIXMON'
    }
  ];

  const renderTime = (data) => {
    const { chartType, chartConfig, dateRange2, updateTime } = data;

    const analysisTypes = ['NEW_FUNNEL', 'RETENTION_ANALYSIS', 'EVENT_ANALYSIS'];
    let timeTerm = null;

    if (analysisTypes.includes(chartType)) {
      timeTerm =
        chartConfig?.retentionAnalysisDataQuery?.analysisDate?.timeTerm ||
        chartConfig?.funnelDataQuery?.timeConfig?.timeTerm ||
        chartConfig?.eventAnalysis?.timeTerm;
    }

    const dateString1 = getString(dateRange2[0], !timeTerm);
    const dateString2 = getString(dateRange2[1], !timeTerm);
    const timestamp1 = getTime(dateRange2[0]);
    const timestamp2 = getTime(dateRange2[1]);
    const formattedHoverTime1 = dayjs(timestamp1).format('YYYY-MM-DD HH:mm:ss');
    const formattedHoverTime2 = dayjs(timestamp2).format('YYYY-MM-DD HH:mm:ss');
    const formattedUpdateTime = dayjs(updateTime).format('YYYY-MM-DD HH:mm:ss');

    return (
      <div className="time flex gap-24">
        <Tooltip
          overlayStyle={{ maxWidth: 'fit-content' }}
          title={
            <>
              <div>{`${dateString1} ~ ${dateString2} | ${formattedHoverTime1} ~ ${formattedHoverTime2}`}</div>
              <div>{t('analysisCenter-QOVLzzRmfIjQ', { formattedUpdateTime })}</div>
            </>
          }
        >
          <span>{`${dateString1} ~ ${dateString2}`}</span>{' '}
          <span>{t('analysisCenter-QOVLzzRmfIjQ', { formattedUpdateTime })}</span>
        </Tooltip>
      </div>
    );
  };

  return (
    <div className="databaseList">
      <header>
        <h1>{getMenuTitle(props.meunData, props.history.location.pathname)}</h1>
        <div className="btnGroup">
          <Button onClick={() => setIsFold(!isFold)}>
            <FilterOutlined />
            {t('analysisCenter-7gIrISo16jbn')}
          </Button>
          <CheckAuth code="aim_chart_anaysis_edit">
            <Dropdown
              menu={{ items: boardMenu() }}
              className="boardDropdown"
              trigger="click"
              overlayClassName="boardChartOverDropdown"
            >
              <Button type="primary">{t('analysisCenter-aHyS75omz4u3')}</Button>
            </Dropdown>
          </CheckAuth>
          {/* <Button type="primary" onClick={() => setVisible(true)}>创建图表</Button> */}
        </div>
      </header>
      {isFold ? <QueryForList show elements={elements} onQuery={queryData} defaultFormData={formVal} /> : null}

      {/* {activeKey === 'RECENT' ? <Dropdown menu={{ items, selectedKeys: recentSelectKey }}><DownOutlined style={{ marginLeft: 8, cursor: 'pointer', color: 'rgba(0, 0, 0, 0.65)' }} /></Dropdown> : null} */}
      <div className="filterWrap">
        <div className="left">
          <div className={`filterItem ${activeKey === 'ALL' ? 'active' : ''}`} onClick={() => onTabsClick('ALL')}>
            {t('analysisCenter-mYBOmdEr3Dgg')}
            {!loading ? `${activeKey === 'ALL' ? `(${pagination.total})` : ''}` : null}
          </div>
          <div className={`filterItem ${activeKey === 'MINE' ? 'active' : ''}`} onClick={() => onTabsClick('MINE')}>
            {t('analysisCenter-jADBdXb2ocoT')}
            {!loading ? `${activeKey === 'MINE' ? `(${pagination.total})` : ''}` : null}
          </div>
          {/* <div className={`filterItem ${activeKey === 'RECENT' ? 'active' : ''}`} onClick={() => onTabsClick('RECENT')}>最近{!loading ? `${activeKey === 'RECENT' ? `(${pagination.total})` : ''}` : null}<span></span></div> */}
          <div style={{ marginRight: 32 }}>
            <span
              style={{ marginRight: 0 }}
              className={`filterItem ${activeKey === 'RECENT' ? 'active' : ''}`}
              onClick={() => onTabsClick('RECENT')}
            >
              {t('analysisCenter-68EhYSh1tntL')}
              {loading ? null : `${activeKey === 'RECENT' ? `(${pagination.total})` : ''}`}
            </span>
            <span>
              {activeKey === 'RECENT' ? (
                <Dropdown menu={{ items, selectedKeys: recentSelectKey }}>
                  <DownOutlined
                    style={{
                      marginLeft: 8,
                      cursor: 'pointer',
                      color: 'rgba(0, 0, 0, 0.65)'
                    }}
                  />
                </Dropdown>
              ) : null}
            </span>
          </div>
          <div
            className={`filterItem ${activeKey === 'FAVORITE' ? 'active' : ''}`}
            onClick={() => onTabsClick('FAVORITE')}
          >
            {t('analysisCenter-p82cR4gFBrWR')}
            {!loading ? `${activeKey === 'FAVORITE' ? `(${pagination.total})` : ''}` : null}
          </div>
        </div>
      </div>

      <Spin className="mainChartSpin" spinning={loading}>
        <div className="main">
          <Row gutter={16}>
            {list?.map((n, index) => {
              return (
                <VisibilitySensor
                  partialVisibility
                  key={n.id}
                  onChange={(isVisible) => onVisibilityChange(isVisible, n, index)}
                >
                  <Col
                    style={{
                      marginBottom: 10,
                      paddingLeft: 5,
                      paddingRight: 5
                    }}
                    key={n.id}
                    xxl={8}
                    xl={12}
                    lg={12}
                    md={24}
                    sm={24}
                  >
                    <div
                      className="colItem"
                      style={{
                        border: '1px solid #fff',
                        width: '100%',
                        height: 470,
                        backgroundColor: '#fff',
                        padding: n.chartType === 'NEW_FUNNEL' && '0 20px 18px 20px'
                      }}
                    >
                      <div
                        className="toolbar"
                        style={{
                          margin: n.chartType === 'NEW_FUNNEL' && '0 -20px'
                        }}
                      >
                        <div className="left">
                          <div className="title">{n.name}</div>
                          {renderTime(n)}
                        </div>
                        <div className="right">
                          <FullscreenOutlined
                            style={{ fontSize: 16, marginRight: 10 }}
                            onClick={() => {
                              onClickDetail(n.id, n);
                            }}
                          />
                          <Dropdown
                            placement="bottomRight"
                            getPopupContainer={(triggerNode) => triggerNode.parentNode}
                            menu={{
                              items: [
                                CheckAuth.checkAuth('aim_chart_anaysis_edit') && {
                                  label: (
                                    <span onClick={() => edit(n.id, n.chartType, n)}>
                                      {t('analysisCenter-Xk9mN2pQr5vT')}
                                    </span>
                                  ),
                                  key: 'edit'
                                },
                                {
                                  label: (
                                    <span
                                      onClick={() => edit(n.id, n.userOperationRecord ? 'cancelCollect' : 'collect', n)}
                                    >
                                      {n.userOperationRecord
                                        ? t('analysisCenter-HDqEL61vJm')
                                        : t('analysisCenter-p82cR4gFBrWR')}
                                    </span>
                                  ),
                                  key: 'collect'
                                },
                                CheckAuth.checkAuth('aim_chart_anaysis_delete') && {
                                  label: (
                                    <span onClick={() => delData(n.id, n.name, n)}>
                                      {t('analysisCenter-abvq1BROo4hs')}
                                    </span>
                                  ),
                                  key: 'delete'
                                }
                              ]
                            }}
                          >
                            <Button type="default" icon={<MoreOutlined />} size="small" shape="circle" />
                          </Dropdown>
                        </div>
                      </div>
                      <div
                        style={{
                          height: n.chartType === 'NEW_FUNNEL' ? '84%' : 'calc(100% - 58px)',
                          overflow: n.chartType === 'NEW_FUNNEL' && 'auto',
                          paddingRight: n.chartType === 'NEW_FUNNEL' || (n.chartType === 'EVENT_ANALYSIS' && '5px'),
                          paddingLeft: n.chartType === 'EVENT_ANALYSIS' && '8px'
                        }}
                        className="smWrap"
                      >
                        {_.includes(loadedComChartIndexArr, index) ? <ComChart info={{ ...n }} /> : null}
                      </div>
                    </div>
                  </Col>
                </VisibilitySensor>
              );
            })}
          </Row>
          <div style={{ margin: '30px 10px', textAlign: 'right' }} hidden={list?.length === 0}>
            <Pagination
              onChange={handleTableChange}
              current={pagination.current}
              pageSize={pagination.pageSize}
              total={pagination.total}
              pageSizeOptions={pagination.pageSizeOptions}
            />
          </div>
        </div>
      </Spin>
      {visible && (
        <AddChart
          visible={visible}
          history={props.history}
          action={() => setVisible(false)}
          scenarioId={state?.scenarioId}
          campaignId={state?.campaignId}
          widgets={state?.widgets}
          version={state?.version}
          boardChart={state?.boardType}
          hasWidgets={state?.hasWidgets}
          id={state?.id}
        />
      )}
    </div>
  );
};

export default connect(stateToProps)(AnalysisCenter);
