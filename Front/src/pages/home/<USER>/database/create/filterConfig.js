import { t } from 'utils/translation';

export default {
  maxFilterCount: 20,
  operatorList: [
    {
      name: t('analysisCenter-nHEKdPV3duCE'),
      operator: 'EQ'
    },
    {
      name: t('analysisCenter-J8p36jQvRfS2'),
      operator: 'NE'
    },
    {
      name: t('analysisCenter-HloIElz75lZX'),
      operator: 'GT'
    },
    {
      name: t('analysisCenter-UHa944QrsP66'),
      operator: 'GTE'
    },
    {
      name: t('analysisCenter-4M2Gxy24KNxz'),
      operator: 'LT'
    },
    {
      name: t('analysisCenter-M0CtrUs8009U'),
      operator: 'LTE'
    },
    {
      name: t('analysisCenter-0GfIcXdHbSwg'),
      operator: 'BETWEEN'
    },
    {
      name: t('analysisCenter-w0MjCV5KCosC'),
      operator: 'IN'
    },
    {
      name: t('analysisCenter-Bz1xbUcmXYHs'),
      operator: 'NOT_IN'
    },
    {
      name: t('analysisCenter-is4mVDrsnQwg'),
      operator: 'IS_NOT_NULL'
    },
    {
      name: t('analysisCenter-TE8B31Sjuw7A'),
      operator: 'IS_NULL'
    },
    {
      name: t('analysisCenter-IBfVD40IJjQK'),
      operator: 'LIKE'
    },
    {
      name: t('analysisCenter-J8p36jQvRfS2'),
      operator: 'NOT_LIKE'
    },
    {
      name: t('analysisCenter-75sO9X3h9sHw'),
      operator: 'START_WITH'
    },
    {
      name: t('analysisCenter-1DyjD2ZT90Lg'),
      operator: 'NOT_START_WITH'
    },
    {
      name: t('analysisCenter-ViSXKUGLy1m6'),
      operator: 'END_WITH'
    },
    {
      name: t('analysisCenter-MaOwTVoKpeFI'),
      operator: 'NOT_END_WITH'
    },
    {
      name: t('analysisCenter-FhROnEOXeuPb'),
      operator: 'IS_TRUE'
    },
    {
      name: t('analysisCenter-l3YJhOxnC8k9'),
      operator: 'IS_FALSE'
    }
  ],
  typeOperator: {
    INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN'],
    DATETIME: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN'],
    TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN'],
    HIVE_DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN'],
    HIVE_TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN'],
    STRING: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'LIKE',
      'NOT_LIKE',
      'START_WITH',
      'NOT_START_WITH',
      'END_WITH',
      'NOT_END_WITH',
      'IN',
      'NOT_IN'
    ],
    BOOL: ['IS_TRUE', 'IS_FALSE']
  },
  typeFormatter: {
    DATE: 'YYYY-MM-DD',
    HIVE_DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
    HIVE_TIMESTAMP: 'YYYY-MM-DD HH:mm:ss'
  },
  connector: [
    {
      name: t('analysisCenter-puepF1oTvynU'),
      value: 'AND'
    },
    {
      name: t('analysisCenter-WTOfQ5NS2Udd'),
      value: 'OR'
    }
  ],
  validator: {
    STRING: {
      option: {
        required: true,
        maxLen: 500
      },
      message: {
        required: t('analysisCenter-JQkO0xZWlCiV'),
        maxLen: t('analysisCenter-fCSGkPTenyjm')
      }
    },
    INT: {
      option: {
        required: true,
        maxLen: 11,
        regex: '^\\d*$'
      },
      message: {
        required: t('analysisCenter-JQkO0xZWlCiV'),
        maxLen: t('analysisCenter-gWFXCO0EEQfl'),
        regex: t('analysisCenter-Rg7h4ZYxMUZn')
      }
    },
    LONG: {
      option: {
        required: true,
        maxLen: 20,
        regex: '^\\d*$'
      },
      message: {
        required: t('analysisCenter-JQkO0xZWlCiV'),
        maxLen: t('analysisCenter-6hpjQuiRSZuw'),
        regex: t('analysisCenter-Rg7h4ZYxMUZn')
      }
    },
    DOUBLE: {
      option: {
        required: true,
        regex: '^\\d*[.]?\\d*$',
        maxLen: 20
      },
      message: {
        required: t('analysisCenter-JQkO0xZWlCiV'),
        maxLen: t('analysisCenter-6hpjQuiRSZuw'),
        regex: t('analysisCenter-GnoAW2JLfsb2')
      }
    },
    DATETIME: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-BFnSD63rog4a')
      }
    },
    TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-BFnSD63rog4a')
      }
    },
    DATE: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-aPVMhrwI0Yfg')
      }
    },
    HIVE_DATE: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-aPVMhrwI0Yfg')
      }
    },
    HIVE_TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('analysisCenter-BFnSD63rog4a')
      }
    },
    BOOL: {
      option: {
        required: false
      },
      message: {
        required: t('analysisCenter-JQkO0xZWlCiV')
      }
    }
  }
};
