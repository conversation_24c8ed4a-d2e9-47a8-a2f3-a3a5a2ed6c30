import { DatabaseOutlined, DoubleRightOutlined, DownOutlined, SearchOutlined } from '@ant-design/icons';
import { Input, Tooltip, Tree } from 'antd';
import React, { useContext, useMemo, useState } from 'react';
import { t } from 'utils/translation';
import { userContrastTypeMap } from '../../config';
import { DataContext } from '../context';
import Box from './box';
import './index.scss';

const selectedStyle = {
  borderBottom: '2px solid var(--ant-primary-color)'
};

export default (props) => {
  const {
    state: { tableSchemaList, info, metricsList },
    dispatch
  } = useContext(DataContext);
  // const [tableSchemaList, setTableSchemaList] = useState({});
  const [selectedTag, setSelectedTag] = useState('field');
  const [userContrastSearchValue, setUserContrastSearchValue] = useState('');
  const [metricsSearchValue, setMetricsSearchValue] = useState('');
  const [fieldsSearchValue, setFieldsSearchValue] = useState('');
  const memoCom = useMemo(() => {
    const treeData = Object.entries(tableSchemaList).reduce((data1, item) => {
      const childrenList = item[1].filter((w) => w.displayName.indexOf(fieldsSearchValue) > -1);
      if (childrenList.length > 0) {
        const info = {
          title: item[1][0].table.displayName,
          key: `table_${item[0]}`,
          children: childrenList.map((n) => ({
            title: (
              <Box name={n.displayName} keyValue={`${n.table.name}.${n.name}`} dataType="TABLE_SCHEMA" dataSource={n} />
            ),
            key: n.id
          }))
        };
        data1.push(info);
      }
      return data1;
    }, []);
    return (
      <>
        <div className="titileStyle">
          <div className="title">
            <DatabaseOutlined />
            <span style={{ marginLeft: 8 }}>{t('analysisCenter-QWqRvFs4eTsu')}</span>
          </div>
          <div className="hideIconWrap">
            <DoubleRightOutlined
              style={{ float: 'right', fontSize: 12 }}
              onClick={() => dispatch({ dataSourceFold: true })}
            />
          </div>
        </div>
        <div className="tabsStyle">
          <div
            onClick={() => setSelectedTag('field')}
            className="tabStyle"
            style={selectedTag === 'field' ? selectedStyle : {}}
          >
            <span
              className="nameStyle"
              style={{
                color: selectedTag === 'field' ? 'var(--ant-primary-color)' : 'rgba(0,0,0, .85)',
                fontWeight: selectedTag === 'field' ? 600 : 400
              }}
            >
              {t('analysisCenter-rnDuyl6apJKZ')}
            </span>
          </div>
          {props?.createInfo?.isBusinessTable !== 2 && (
            <div
              onClick={() => setSelectedTag('metrics')}
              className="tabStyle"
              style={selectedTag === 'metrics' ? selectedStyle : {}}
            >
              <span
                className="nameStyle"
                style={{
                  color: selectedTag === 'metrics' ? 'var(--ant-primary-color)' : 'rgba(0,0,0, .85)',
                  fontWeight: selectedTag === 'metrics' ? 600 : 400
                }}
              >
                {t('analysisCenter-rVJbNVxDzBQH')}
              </span>
            </div>
          )}
          {props?.createInfo?.isBusinessTable !== 2 && (
            <div
              onClick={() => setSelectedTag('usergroup')}
              className="tabStyle"
              style={selectedTag === 'usergroup' ? selectedStyle : {}}
            >
              <span
                className="nameStyle"
                style={{
                  color: selectedTag === 'usergroup' ? 'var(--ant-primary-color)' : 'rgba(0,0,0, .85)',
                  fontWeight: selectedTag === 'usergroup' ? 600 : 400
                }}
              >
                {t('analysisCenter-0VALJi6t7U5Y')}
              </span>
            </div>
          )}
        </div>
        <div
          style={{
            marginTop: 16,
            display: 'flex',
            flexDirection: 'column',
            height: '100%'
          }}
          hidden={selectedTag !== 'field'}
        >
          {props.createInfo && !props.curFlag && (
            <div className="back_create">
              <Tooltip title={props.createInfo?.tableTitle}>
                <span>{props.createInfo?.tableTitle}</span>
              </Tooltip>
              <Tooltip title={t('analysisCenter-G0bGLLOh4A1J')}>
                <div>
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 14 14"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    onClick={(e) => {
                      e.stopPropagation();
                      dispatch({ createVisible: true });
                    }}
                  >
                    <path
                      d="M1.85938 7.54688H2.72891C2.78906 7.54688 2.83828 7.49766 2.83828 7.4375V3.94707H10.0652V4.93965C10.0652 4.96563 10.0734 4.99024 10.0898 5.01075C10.0991 5.02255 10.1105 5.03242 10.1236 5.03978C10.1366 5.04714 10.151 5.05185 10.1658 5.05363C10.1807 5.05541 10.1958 5.05422 10.2102 5.05015C10.2246 5.04608 10.2381 5.03919 10.2498 5.02989L12.209 3.4918C12.2678 3.42344 12.2582 3.35098 12.209 3.31133L10.2498 1.77461C10.2296 1.75849 10.2045 1.7498 10.1787 1.75C10.1158 1.75 10.0639 1.80196 10.0639 1.86485V2.85743H2.73027C2.19023 2.85743 1.75 3.29766 1.75 3.83907V7.4375C1.75 7.49766 1.79922 7.54688 1.85938 7.54688ZM12.1406 6.45313H11.2711C11.2109 6.45313 11.1617 6.50235 11.1617 6.5625V10.0529H3.93477V9.06035C3.93477 9.03438 3.92656 9.00977 3.91016 8.98926C3.90095 8.97745 3.88949 8.96758 3.87645 8.96022C3.8634 8.95286 3.84903 8.94816 3.83416 8.94638C3.81929 8.9446 3.80422 8.94578 3.78981 8.94986C3.77539 8.95393 3.76193 8.96082 3.7502 8.97012L1.79102 10.5082C1.73223 10.5766 1.7418 10.649 1.79102 10.6887L3.7502 12.2254C3.7707 12.2418 3.79531 12.25 3.82129 12.25C3.88418 12.25 3.93613 12.198 3.93613 12.1352V11.1426H11.2725C11.8125 11.1426 12.2527 10.7023 12.2527 10.1609V6.5625C12.25 6.50235 12.2008 6.45313 12.1406 6.45313Z"
                      fill="black"
                      fillOpacity="0.85"
                    />
                  </svg>
                </div>
              </Tooltip>
            </div>
          )}
          <Input
            onChange={(e) => setFieldsSearchValue(e.target.value)}
            placeholder={t('analysisCenter-EheP6vgZsvdA')}
            allowClear
            suffix={<SearchOutlined />}
          />
          <div className="treeWrapper" style={{ flex: 1 }}>
            <Tree
              selectable={false}
              className="tableSchemaTree"
              treeData={treeData}
              showIcon
              switcherIcon={<DownOutlined />}
              defaultExpandAll
            >
              {/* {Object.entries(tableSchemaList).map(item => {
              const childrenList = item[1].filter(w => w.displayName.indexOf(fieldsSearchValue) > -1);
              if (childrenList.length === 0) return null;
              return (<TreeNode title={item[1][0].table.displayName} key={item[0]}>
                {childrenList.map(n => <TreeNode title={<Box name={n.displayName} keyValue={`${n.table.name}.${n.name}`} dataType="TABLE_SCHEMA" dataSource={n} />} key={n.id} />)}
              </TreeNode>);
              // return (<TreeNode icon={<Icon type="table" />} title={item[1][0].table.displayName} key={item[0]}>
              //   {childrenList.map(n => <TreeNode title={<Box name={n.displayName} keyValue={`${n.table.name}.${n.name}`} dataType="TABLE_SCHEMA" dataSource={n} />} key={n.id} />)}
              // </TreeNode>);
            })} */}
            </Tree>
          </div>
        </div>
        {info?.isBusinessTable !== 2 && (
          <div
            style={{
              marginTop: 16,
              display: 'flex',
              flexDirection: 'column',
              height: '100%'
            }}
            hidden={selectedTag !== 'metrics'}
          >
            <Input
              onChange={(e) => setMetricsSearchValue(e.target.value)}
              value={metricsSearchValue}
              placeholder={t('analysisCenter-EheP6vgZsvdA')}
              allowClear
              suffix={<SearchOutlined />}
            />
            <div className="treeWrapper" style={{ marginTop: 10, padding: '0px 20px', flex: 1 }}>
              {metricsList
                .filter((n) => n.name.indexOf(metricsSearchValue) > -1)
                .map((n) => (
                  <div key={n.id} style={{ marginBottom: 5 }}>
                    <Box name={n.name} keyValue={`${n.id}`} dataType="METRICS" dataSource={n} />
                  </div>
                ))}
            </div>
          </div>
        )}

        {info?.isBusinessTable !== 2 && (
          <div style={{ marginTop: 16 }} hidden={selectedTag !== 'usergroup' || info.isBusinessTable !== 1}>
            <Input
              onChange={(e) => setUserContrastSearchValue(e.target.value)}
              placeholder={t('analysisCenter-EheP6vgZsvdA')}
              allowClear
              suffix={<SearchOutlined />}
            />
            <div className="treeWrapper" style={{ marginTop: 10, padding: '0px 20px' }}>
              {userContrastTypeMap
                .filter((n) => n.name.indexOf(userContrastSearchValue) > -1)
                .map((n) => (
                  <div key={n.dataType} style={{ marginBottom: 5 }}>
                    <Box name={n.name} keyValue={n.dataType} dataType={n.dataType} dataSource={{}} />
                  </div>
                ))}
            </div>
          </div>
        )}
      </>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableSchemaList, info, selectedTag, userContrastSearchValue, fieldsSearchValue, metricsSearchValue]);

  return (
    <div className="dataSource">
      {/* <div className="labelStyle">日期</div>
      <div className="inputStyle">
        <RangePicker />
      </div>
      <Divider /> */}
      {memoCom}
    </div>
  );
};
