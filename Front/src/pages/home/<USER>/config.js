import { t } from '@/utils/translation';

const statusList = {
  RUNNING: t('approve-cjLhV8mX8XEu'),
  PASS: t('approve-xfUALfgz8iaf'),
  REJECT: t('approve-WTu8SpaYe7NL'),
  BACKOUT: t('approve-alPFKV5eeZdZ'),
  CANCEL: t('approve-6syfqhJzfnCe')
};

const statusV2List = {
  START: t('approve-Tz7LXM7oiauE'),
  RUNNING: t('approve-oxOg1ceV1Idp'),
  PASS: t('approve-ZXiAAOW0sN1x'),
  REJECT: t('approve-ZXiAAOW0sN1x'),
  BACKOUT: t('approve-alPFKV5eeZdZ'),
  STOP: t('approve-H46puG4mXzfo'),
  ERROR: t('approve-5j8n4hMP0zqb')
};

const statusColor = {
  RUNNING: '#1677FF',
  PASS: '#52C41A',
  REJECT: '#FF4D4F',
  BACKOUT: '#FAAD14',
  CANCEL: '#D9D9D9'
};

export { statusColor, statusList, statusV2List };
