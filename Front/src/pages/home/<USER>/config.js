import { t } from '@/utils/translation';

export const elements = (tabKey, userList, dictTypeList) => {
  if (tabKey === 2) {
    return [
      {
        type: 'input',
        name: 'taskId',
        label: t('setting-NlnALnV2edTP'),
        operator: 'LIKE'
      },
      {
        type: 'input',
        name: 'contentName',
        label: t('setting-os2cT70oD9m2'),
        operator: 'LIKE'
      },
      {
        type: 'dateRange',
        name: 'startTime',
        label: t('setting-bVj3Vgvn1bCT'),
        operator: 'DATE_BETWEEN'
      },
      {
        name: 'starterId',
        type: 'select',
        label: t('setting-Nip5KRu5hlK1'),
        operator: 'EQ',
        options: userList
          ? userList.map((item) => {
              return { key: item.id, value: item.id, label: item.name };
            })
          : []
      },
      {
        name: 'approvalTypePcode',
        type: 'select',
        label: t('setting-1xyOEkKLUb5b'),
        operator: 'EQ',
        options: dictTypeList.map((item) => {
          return { key: item?.code, value: item?.code, label: item?.name };
        })
      },
      {
        name: 'typeCodeName',
        type: 'input',
        label: t('setting-G6S1JQLNJ1f4'),
        operator: 'LIKE'
      }
    ];
  } else if (tabKey === 3) {
    return [
      {
        type: 'input',
        name: 'approvalNo',
        label: t('setting-NlnALnV2edTP'),
        operator: 'LIKE'
      },
      {
        type: 'input',
        name: 'contentName',
        label: t('setting-os2cT70oD9m2'),
        operator: 'LIKE'
      },
      {
        type: 'dateRange',
        name: 'createTime',
        label: t('setting-bVj3Vgvn1bCT'),
        operator: 'DATE_BETWEEN'
      },
      {
        name: 'status',
        type: 'select',
        label: t('setting-D1PQtuGlweYg'),
        operator: 'EQ',
        options: [
          { key: 'REJECT', value: 'REJECT', label: t('setting-2CGzOnrlLV3n') },
          { key: 'PASS', value: 'PASS', label: t('setting-bYvd4muPSaIw') }
        ]
      },
      {
        name: 'contentType',
        type: 'select',
        label: t('setting-KdBgpjlIFbHd'),
        operator: 'EQ',
        options: dictTypeList.map((item) => {
          return { key: item.value, value: item.value, label: item.label };
        })
      },
      {
        name: 'approverId',
        type: 'select',
        label: t('setting-4jkm2leY0arn'),
        operator: 'EQ',
        options: userList
          ? userList.map((item) => {
              return { key: item.id, value: item.id, label: item.name };
            })
          : []
      },
      {
        name: 'createUserId',
        type: 'select',
        label: t('setting-Nip5KRu5hlK1'),
        operator: 'EQ',
        options: userList
          ? userList.map((item) => {
              return { key: item.id, value: item.id, label: item.name };
            })
          : []
      }
    ];
  }
  return [
    {
      type: 'input',
      name: 'approvalNo',
      label: t('setting-NlnALnV2edTP'),
      operator: 'LIKE'
      // componentConfig: {
      //   placeholder: '请输入图标名称'
      // },
      // options:[]
    },
    {
      type: 'input',
      name: 'contentName',
      label: t('setting-os2cT70oD9m2'),
      operator: 'LIKE'
    },
    {
      type: 'dateRange',
      name: 'createTime',
      label: t('setting-bVj3Vgvn1bCT'),
      operator: 'DATE_BETWEEN'
    },
    {
      name: 'status',
      type: 'select',
      label: t('setting-D1PQtuGlweYg'),
      operator: 'EQ',
      options: [
        { key: 'RUNNING', value: 'RUNNING', label: t('setting-O7MOlpe3qY7F') },
        { key: 'REJECT', value: 'REJECT', label: t('setting-2CGzOnrlLV3n') },
        { key: 'PASS', value: 'PASS', label: t('setting-bYvd4muPSaIw') },
        { key: 'BACKOUT', value: 'BACKOUT', label: t('setting-VUD9MZHeCQWG') },
        { key: 'CANCEL', value: 'CANCEL', label: t('setting-EtVVaAMzxzWm') }
      ]
    },
    {
      name: 'contentType',
      type: 'select',
      label: t('setting-KdBgpjlIFbHd'),
      operator: 'EQ',
      options: dictTypeList.map((item) => {
        return { key: item.value, value: item.value, label: item.label };
      })
    },
    {
      name: 'createUserId',
      type: 'select',
      label: t('setting-Nip5KRu5hlK1'),
      operator: 'EQ',
      options: userList
        ? userList.map((item) => {
            return { key: item.id, value: item.id, label: item.name };
          })
        : []
    }
  ];
};

export const initParam = {
  page: 1,
  search: [],
  size: 10,
  sorts: [{ propertyName: 'createTime', direction: 'desc' }]
};

export const initHistoryParam = {
  page: 1,
  search: [],
  size: 10,
  sorts: [{ propertyName: 'createTime', direction: 'asc' }]
};
