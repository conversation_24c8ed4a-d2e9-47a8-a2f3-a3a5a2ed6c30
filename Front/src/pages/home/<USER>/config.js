import { t } from 'utils/translation';

export const initParam = {
  page: 1,
  search: [],
  size: 10,
  sorts: [{ propertyName: 'updateTime', direction: 'desc' }]
};

export const entityMap = {
  entity_user: t('operationCenter-7jUdFDK2ouMh'),
  entity_employee: t('operationCenter-wfTBLNZbvvWZ'),
  entity_item: t('operationCenter-waWI5sfyVPTu'),
  entity_equity: t('operationCenter-4BiZeKsM40cr'),
  entity_content: t('operationCenter-JnDNnieRH0Qw'),
  entity_device: t('operationCenter-fUPsY5Rg3Ibo'),
  entity_department: t('operationCenter-4eo0fU0QMtvs')
};
