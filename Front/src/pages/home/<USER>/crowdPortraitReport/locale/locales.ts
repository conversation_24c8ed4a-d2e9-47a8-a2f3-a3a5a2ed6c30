export default {
  cn: {
    'portraitCenter-JOMTubofq4UQ': '共 {{totals}} 条',
    'portraitCenter-wCUX5wQVrfdU': '报告名称',
    'portraitCenter-SjoX1rg6cYJv': '删除',
    'portraitCenter-VzzGpHsDlWtb': '确认删除',
    'portraitCenter-Rzb5Q0zpSxUC': '您将删除选项：',
    'portraitCenter-GOQ0CvN8746I': '该操作无法撤销，确定删除吗？',
    'portraitCenter-u6TbhxVQDOdE': '删除成功',
    'portraitCenter-6l4C7PEMO3Zl': '搜索报告名称',
    'portraitCenter-uphHOJsnZL3S': '更多',
    'portraitCenter-vQZsYRxmYFQC': '查看',
    'portraitCenter-OJYareU4jMq2': '计算完成时间',
    'portraitCenter-SQL7gyOCc2Pd': '创建者',
    'portraitCenter-bxelIqwIUd1C': '创建时间',
    'portraitCenter-iXYp6nXCPsHN': '操作',
    'portraitCenter-2HYMAbWyBruG': '天',
    'portraitCenter-enFOSm6WqPJO': '周',
    'portraitCenter-MpFSNShUeShm': '月',
    'portraitCenter-d2Kffv7SQ6Gs': '之前',
    'portraitCenter-8FiwQRb6PFOG': '之后',
    'portraitCenter-n04ddFMlVpGL': '过去',
    'portraitCenter-WFZASRXz7wkB': '将来',
    'portraitCenter-uAfiajIawzUu': '现在',
    'portraitCenter-MbUD5O179e5V': '等于',
    'portraitCenter-N828HNw6zTSW': '大于',
    'portraitCenter-9fJOkvveKyg7': '大于等于',
    'portraitCenter-0wrg1Owtas97': '小于',
    'portraitCenter-AAaDwzZyJusv': '小于等于',
    'portraitCenter-MsHoVxEVu4Ta': '属于以下分群',
    'portraitCenter-0ZDTPql07w2B': '排除以下分群',
    'portraitCenter-EbN76tf4VljS': '上传或流程分群无上传规则！',
    'portraitCenter-tEeFVNCpgA0V': '短链任务名称：{{name}}',
    'portraitCenter-riueGsOr72W9': '短链任务ID：{{id}}',
    'portraitCenter-re0aOKeVBQCH': '总点击次数：',
    'portraitCenter-1999zutTF9sy': '分钟',
    'portraitCenter-inzqp4KgiDuo': '小时',
    'portraitCenter-Tp1jegWhu37C': '天',
    'portraitCenter-Tgn7t6PqvJ0l': '人群：{{name}}',
    'portraitCenter-ZoOvK0CWq17u': '时间：{{time1}} - {{time2}}',
    'portraitCenter-bB479l0jINiS': '转化窗口期：',
    'portraitCenter-MKkTSqvwoJJ8': '初始行为：{{name}}',
    'portraitCenter-LJvVxxj5luvT': '留存行为：{{name}}',
    'portraitCenter-MHRo5Tzojwth': '分群画像报告',
    'portraitCenter-NZGPDMh90jNQ': '分群规则',
    'portraitCenter-4F46u6MJIR5X': '分析报告',
    'portraitCenter-XYUlC8RTnBOU': 'ID类型',
    'portraitCenter-KrAhQdjD1Pzm': '画像目标分群',
    'portraitCenter-MuRS5MDZMJ5h': '创建者',
    'portraitCenter-qQx9a6koHHN4': '所属部门',
    'portraitCenter-fYcF5lGTqK0X': '分群人数',
    'portraitCenter-UCisDIrWiLFV': '人，占该 ID 类型下全部用户',
    'portraitCenter-ALYHSEYUypTR': '创建时间',
    'portraitCenter-Q7uX1LN4Lpi1': '数据计算完成时间'
  },
  en: {
    'portraitCenter-JOMTubofq4UQ': 'Total {{totals}}',
    'portraitCenter-wCUX5wQVrfdU': 'Report Name',
    'portraitCenter-SjoX1rg6cYJv': 'Delete',
    'portraitCenter-VzzGpHsDlWtb': 'Confirm Delete',
    'portraitCenter-Rzb5Q0zpSxUC': 'You will delete the option:',
    'portraitCenter-GOQ0CvN8746I': 'This operation cannot be undone, are you sure to delete?',
    'portraitCenter-u6TbhxVQDOdE': 'Delete successful',
    'portraitCenter-6l4C7PEMO3Zl': 'Search report name',
    'portraitCenter-uphHOJsnZL3S': 'More',
    'portraitCenter-vQZsYRxmYFQC': 'View',
    'portraitCenter-OJYareU4jMq2': 'Calculation completion time',
    'portraitCenter-SQL7gyOCc2Pd': 'Creator',
    'portraitCenter-bxelIqwIUd1C': 'Create time',
    'portraitCenter-iXYp6nXCPsHN': 'Actions',
    'portraitCenter-2HYMAbWyBruG': 'Day',
    'portraitCenter-enFOSm6WqPJO': 'Week',
    'portraitCenter-MpFSNShUeShm': 'Month',
    'portraitCenter-d2Kffv7SQ6Gs': 'Before',
    'portraitCenter-8FiwQRb6PFOG': 'After',
    'portraitCenter-n04ddFMlVpGL': 'Past',
    'portraitCenter-WFZASRXz7wkB': 'Future',
    'portraitCenter-uAfiajIawzUu': 'Now',
    'portraitCenter-MbUD5O179e5V': 'Equal',
    'portraitCenter-N828HNw6zTSW': 'Greater than',
    'portraitCenter-9fJOkvveKyg7': 'Greater than or equal to',
    'portraitCenter-0wrg1Owtas97': 'Less than',
    'portraitCenter-AAaDwzZyJusv': 'Less than or equal to',
    'portraitCenter-MsHoVxEVu4Ta': 'Belongs to the following groups',
    'portraitCenter-0ZDTPql07w2B': 'Exclude the following groups',
    'portraitCenter-EbN76tf4VljS': 'Upload or flow crowd has no upload rule!',
    'portraitCenter-tEeFVNCpgA0V': 'Short chain task name: {{name}}',
    'portraitCenter-riueGsOr72W9': 'Short chain task ID: {{id}}',
    'portraitCenter-re0aOKeVBQCH': 'Total clicks:',
    'portraitCenter-1999zutTF9sy': 'Minutes',
    'portraitCenter-inzqp4KgiDuo': 'Hours',
    'portraitCenter-Tp1jegWhu37C': 'Days',
    'portraitCenter-Tgn7t6PqvJ0l': 'Crowd:',
    'portraitCenter-ZoOvK0CWq17u': 'Time: {{time1}} - {{time2}}',
    'portraitCenter-bB479l0jINiS': 'Conversion window period:',
    'portraitCenter-MKkTSqvwoJJ8': 'Initial behavior:',
    'portraitCenter-LJvVxxj5luvT': 'Retention behavior:',
    'portraitCenter-MHRo5Tzojwth': 'Crowd portrait report',
    'portraitCenter-NZGPDMh90jNQ': 'Crowd rule',
    'portraitCenter-4F46u6MJIR5X': 'Analysis report',
    'portraitCenter-XYUlC8RTnBOU': 'ID type',
    'portraitCenter-KrAhQdjD1Pzm': 'Portrait target crowd',
    'portraitCenter-MuRS5MDZMJ5h': 'Creator',
    'portraitCenter-qQx9a6koHHN4': 'Department',
    'portraitCenter-fYcF5lGTqK0X': 'Group number',
    'portraitCenter-UCisDIrWiLFV': 'people, accounting for all users of this ID type',
    'portraitCenter-ALYHSEYUypTR': 'Create time',
    'portraitCenter-Q7uX1LN4Lpi1': 'Data calculation completion time'
  }
};
