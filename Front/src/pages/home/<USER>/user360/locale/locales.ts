export default {
  cn: {
    // list/index.jsx
    'portraitCenter-OCSZjmxnxcHg': '数据表',
    'portraitCenter-qIH2hGshGCB8': '表字段',
    'portraitCenter-FppnukoNh4JH': '字段值',
    'portraitCenter-7DD7JoHsz1VA': '无限制',
    'portraitCenter-8UVStzF7eFti': '手机号',
    'portraitCenter-0kqZ5MAMZdH3': '请选择ID类型',
    'portraitCenter-EhMp6fKnMiiz': '过滤规则必须填写完整且在一条以上才能预览',
    'portraitCenter-yStLlvz0BSy9': '筛选内容不能为空',
    'portraitCenter-SJGDPqZA6FjV': '筛选',
    'portraitCenter-0NyyvIOllRZF': 'ID类型',
    'portraitCenter-yqpdyPKuYncJ': '清空',
    'portraitCenter-t73VHaghjWUK': '简洁筛选',
    'portraitCenter-tUSdkihSYfuH': '高级筛选',
    'portraitCenter-juEotqqWlJzi': '查询',
    'portraitCenter-q4DsRJtBCGbd': '排除以下条件',
    'portraitCenter-OniRUUmH475s': '请输入{{filterName}}',
    'portraitCenter-vJzTFBeUeork': '列表',
    'portraitCenter-ODvfSOokG0iw': '字段权限',
    'portraitCenter-rCxffyDbZu7w': '脱敏字段',
    'portraitCenter-BXVVEEwJOAji': '数据权限',

    // list/ColumnTable.jsx
    'portraitCenter-sUAFuXifxcj3': '共 {{totals}} 条',
    'portraitCenter-yZHNqYzPoqzo': '至少保留一个展示字段',
    'portraitCenter-AMlIoglyt1oi': '保存成功',
    'portraitCenter-H62EKSeVKzuH': '列设置',

    // detail/uesr360Detail.jsx
    'portraitCenter-wjkdd1Cl6Z8b': '数据看板',
    'portraitCenter-Qe5AV3e566bS': '移除档案?',
    'portraitCenter-uQqVAXIhVApX': '移除该档案后，将移除相关的历史数据，且不可恢复。',
    'portraitCenter-y9VoAVLdgCll': '确认移除',
    'portraitCenter-fPZPK2oAhQWR': '取消',
    'portraitCenter-24w6j4IY1YNj': '删除成功',
    'portraitCenter-7An6jPK2B56I': '客户列表',
    'portraitCenter-4pi7U9ZEc0mw': '客户详情',
    'portraitCenter-5G1FcyOMndQq': '详情',
    'portraitCenter-9S5jBJLt6Zl7': '新增档案模块',
    'portraitCenter-7RtIemrz5MGa': '编辑',
    'portraitCenter-daVeOptHPgPH': '移除',
    'portraitCenter-iI2j5ravadJ6': '编辑档案模块',
    'portraitCenter-d5RfJCzznHiM': '新增档案模块',

    // detail/create.jsx
    'portraitCenter-rqYrWeR78Aod': '档案类型',
    'portraitCenter-vgBvK78sCvWd': '编辑档案',
    'portraitCenter-INlBQuoW0Zsq': '新建档案',

    // detail/create/chart/filterConfig.js
    'portraitCenter-3Or0IIYsZ4eI': '等于',
    'portraitCenter-9oku9WlUTMVK': '不等于',
    'portraitCenter-2Ye6qTc9cD4H': '大于',
    'portraitCenter-kQjPG1oZGDYa': '大于等于',
    'portraitCenter-ZuXamizt4LO6': '小于',
    'portraitCenter-tgk2duNbaMjC': '小于等于',
    'portraitCenter-SUoUODgEL6EO': '范围',
    'portraitCenter-owxuK5cn9swB': '包含',
    'portraitCenter-3d4pWg5dfTy8': '不包含',
    'portraitCenter-wsU9XzJw5QrI': '有值',
    'portraitCenter-AXHPPrHQO28f': '空值',
    'portraitCenter-skoeyUhyRmPy': '匹配',
    'portraitCenter-ozXwHaZLNGPz': '不匹配',
    'portraitCenter-56c9rxlgkOXU': '开头匹配',
    'portraitCenter-5RLBhAMqYnQh': '开头不匹配',
    'portraitCenter-Uoy5eeGc1kcu': '结尾匹配',
    'portraitCenter-l4oxk5ow9OSn': '结尾不匹配',
    'portraitCenter-r0jaTDUd1aoF': '是',
    'portraitCenter-St6LTK0g9Am2': '否',
    'portraitCenter-1v3vr2wYrV3t': '且',
    'portraitCenter-rbv9J1dwm00a': '或',
    'portraitCenter-wl188LKybdqG': '请输入',
    'portraitCenter-2vT1GY81hKc2': '最大输入500个字符',
    'portraitCenter-DWf0eaq428OX': '最大长度11个字符',
    'portraitCenter-WNlyhYrMtnYN': '请输入数字',
    'portraitCenter-wLZT39oqOYpp': '最大长度20个字符',
    'portraitCenter-Ztg3P90ZIGBJ': '请输入浮点数字',
    'portraitCenter-mXmOpj7OcZmV': '请输入日期时间',
    'portraitCenter-hu1rx2UoUJDj': '请输入日期',

    // detail/create/chart/index.jsx
    'portraitCenter-yQclvPbC5Kdg': '柱状图',

    // detail/create/chart/drop.js
    'portraitCenter-DhXXx3jJs8fv': '时间粒度',
    'portraitCenter-3K8XYEJJqHH4': '移除字段',
    'portraitCenter-l9B7AKrO0TUo': '横轴',
    'portraitCenter-IPRlU5Ia4Waz': '选择左边的数据表字段拖到这里',
    'portraitCenter-dfsR3IvSvjlj': '值(纵轴)',
    'portraitCenter-BOj6NHtLWuzc': '图例',
    'portraitCenter-E9RoC80KZELv': '粒度：',
    'portraitCenter-aNozdD8kDj1M': '属性档案',
    'portraitCenter-FwogTNTKLd7y': '该用户档案模块为行业下通用',
    'portraitCenter-dRMrvYQ6dNPc': '该用户档案模块为项目下专属',
    'portraitCenter-np3N6GCs2Tdd': '行业',
    'portraitCenter-8SJtHXWFSTsp': '项目',
    'portraitCenter-SuC5VtcSRPGV': '活动档案',
    'portraitCenter-4tVM2co7nZ6e': '行为档案',
    'portraitCenter-jx4qgkrSCJ': '图例影响：',
    'portraitCenter-yt7q1bOuup': '所选字段作为柱状图值的分类纬度。',
    'portraitCenter-8W3bg8SSkp': '在簇状图中，柱体按图例类型并列排列；',
    'portraitCenter-kuSwfop1YL': '在堆积图中，柱体按图例类型堆叠展示；',
    'portraitCenter-YUrPYJ75q8': '单柱类型，图例选项可为空。'
  },
  en: {
    // list/index.jsx
    'portraitCenter-OCSZjmxnxcHg': 'Data Table',
    'portraitCenter-qIH2hGshGCB8': 'Table Field',
    'portraitCenter-FppnukoNh4JH': 'Field Value',
    'portraitCenter-7DD7JoHsz1VA': 'No Limit',
    'portraitCenter-8UVStzF7eFti': 'Phone Number',
    'portraitCenter-0kqZ5MAMZdH3': 'Please select ID type',
    'portraitCenter-EhMp6fKnMiiz': 'Filter rules must be complete and more than one to preview',
    'portraitCenter-yStLlvz0BSy9': 'Filter content cannot be empty',
    'portraitCenter-SJGDPqZA6FjV': 'Filter',
    'portraitCenter-0NyyvIOllRZF': 'ID Type',
    'portraitCenter-yqpdyPKuYncJ': 'Clear',
    'portraitCenter-t73VHaghjWUK': 'Simple Filter',
    'portraitCenter-tUSdkihSYfuH': 'Advanced Filter',
    'portraitCenter-juEotqqWlJzi': 'Query',
    'portraitCenter-q4DsRJtBCGbd': 'Exclude the following conditions',
    'portraitCenter-OniRUUmH475s': 'Please enter {{filterName}}',
    'portraitCenter-vJzTFBeUeork': 'List',
    'portraitCenter-ODvfSOokG0iw': 'Field Permissions',
    'portraitCenter-rCxffyDbZu7w': 'Sensitive Fields',
    'portraitCenter-BXVVEEwJOAji': 'Data Permissions',

    // list/ColumnTable.jsx
    'portraitCenter-sUAFuXifxcj3': 'Total {{totals}} items',
    'portraitCenter-yZHNqYzPoqzo': 'At least one display field must be retained',
    'portraitCenter-AMlIoglyt1oi': 'Save successful',
    'portraitCenter-H62EKSeVKzuH': 'Column Settings',

    // detail/uesr360Detail.jsx
    'portraitCenter-wjkdd1Cl6Z8b': 'Data Dashboard',
    'portraitCenter-Qe5AV3e566bS': 'Remove profile?',
    'portraitCenter-uQqVAXIhVApX': 'After removing this profile, related historical data will be removed and cannot be recovered.',
    'portraitCenter-y9VoAVLdgCll': 'Confirm Remove',
    'portraitCenter-fPZPK2oAhQWR': 'Cancel',
    'portraitCenter-24w6j4IY1YNj': 'Delete successful',
    'portraitCenter-7An6jPK2B56I': 'Customer List',
    'portraitCenter-4pi7U9ZEc0mw': 'Customer Details',
    'portraitCenter-5G1FcyOMndQq': 'Details',
    'portraitCenter-9S5jBJLt6Zl7': 'Add Profile Module',
    'portraitCenter-7RtIemrz5MGa': 'Edit',
    'portraitCenter-daVeOptHPgPH': 'Remove',
    'portraitCenter-iI2j5ravadJ6': 'Edit Profile Module',
    'portraitCenter-d5RfJCzznHiM': 'Add Profile Module',

    // detail/create.jsx
    'portraitCenter-rqYrWeR78Aod': 'Profile Type',
    'portraitCenter-vgBvK78sCvWd': 'Edit Profile',
    'portraitCenter-INlBQuoW0Zsq': 'Create Profile',

    // detail/create/chart/filterConfig.js
    'portraitCenter-3Or0IIYsZ4eI': 'Equal',
    'portraitCenter-9oku9WlUTMVK': 'Not Equal',
    'portraitCenter-2Ye6qTc9cD4H': 'Greater Than',
    'portraitCenter-kQjPG1oZGDYa': 'Greater Than or Equal',
    'portraitCenter-ZuXamizt4LO6': 'Less Than',
    'portraitCenter-tgk2duNbaMjC': 'Less Than or Equal',
    'portraitCenter-SUoUODgEL6EO': 'Range',
    'portraitCenter-owxuK5cn9swB': 'Contains',
    'portraitCenter-3d4pWg5dfTy8': 'Not Contains',
    'portraitCenter-wsU9XzJw5QrI': 'Has Value',
    'portraitCenter-AXHPPrHQO28f': 'Null Value',
    'portraitCenter-skoeyUhyRmPy': 'Match',
    'portraitCenter-ozXwHaZLNGPz': 'Not Match',
    'portraitCenter-56c9rxlgkOXU': 'Start With',
    'portraitCenter-5RLBhAMqYnQh': 'Not Start With',
    'portraitCenter-Uoy5eeGc1kcu': 'End With',
    'portraitCenter-l4oxk5ow9OSn': 'Not End With',
    'portraitCenter-r0jaTDUd1aoF': 'Yes',
    'portraitCenter-St6LTK0g9Am2': 'No',
    'portraitCenter-1v3vr2wYrV3t': 'And',
    'portraitCenter-rbv9J1dwm00a': 'Or',
    'portraitCenter-wl188LKybdqG': 'Please input',
    'portraitCenter-2vT1GY81hKc2': 'Maximum 500 characters',
    'portraitCenter-DWf0eaq428OX': 'Maximum 11 characters',
    'portraitCenter-WNlyhYrMtnYN': 'Please input number',
    'portraitCenter-wLZT39oqOYpp': 'Maximum 20 characters',
    'portraitCenter-Ztg3P90ZIGBJ': 'Please input decimal number',
    'portraitCenter-mXmOpj7OcZmV': 'Please input date time',
    'portraitCenter-hu1rx2UoUJDj': 'Please input date',

    // detail/create/chart/index.jsx
    'portraitCenter-yQclvPbC5Kdg': 'Bar Chart',

    // detail/create/chart/drop.js
    'portraitCenter-DhXXx3jJs8fv': 'Time Granularity',
    'portraitCenter-3K8XYEJJqHH4': 'Remove Field',
    'portraitCenter-l9B7AKrO0TUo': 'X-Axis',
    'portraitCenter-IPRlU5Ia4Waz': 'Drag data table fields from the left here',
    'portraitCenter-dfsR3IvSvjlj': 'Value (Y-Axis)',
    'portraitCenter-BOj6NHtLWuzc': 'Legend',
    'portraitCenter-E9RoC80KZELv': 'Granularity: ',
    'portraitCenter-aNozdD8kDj1M': 'Attribute Profile',
    'portraitCenter-FwogTNTKLd7y': 'This user profile module is industry-wide general',
    'portraitCenter-dRMrvYQ6dNPc': 'This user profile module is project-specific',
    'portraitCenter-np3N6GCs2Tdd': 'Industry',
    'portraitCenter-8SJtHXWFSTsp': 'Project',
    'portraitCenter-SuC5VtcSRPGV': 'Campaign Profile',
    'portraitCenter-4tVM2co7nZ6e': 'Behavior Profile',
    'portraitCenter-jx4qgkrSCJ': 'Legend Impact:',
    'portraitCenter-yt7q1bOuup': 'The selected field is used as the classification dimension of the value of the bar chart.',
    'portraitCenter-8W3bg8SSkp': 'In the clustered chart, the columns are arranged side by side according to the legend type;',
    'portraitCenter-kuSwfop1YL': 'In the stacked chart, the columns are displayed stacked according to the legend type;',
    'portraitCenter-YUrPYJ75q8': 'For single column type, the legend option can be empty.'
  }
};
