export default {
  cn: {
    // chart/index.jsx
    'portraitCenter-vR5CxNjKdGfH': '计数',
    'portraitCenter-wS6DyOjKdGfH': '求和',
    'portraitCenter-xT7EzPjKdGfH': '平均值',
    'portraitCenter-yU8FaQjKdGfH': '最大',
    'portraitCenter-zV9GbRjKdGfH': '最小',
    'portraitCenter-aW0HcSjKdGfH': '唯一计数',
    'portraitCenter-bX1IdTjKdGfH': '堆积柱状图',
    'portraitCenter-cY2JeUjKdGfH': '蔟状柱状图',
    'portraitCenter-dZ3KfVjKdGfH': '折线图',
    'portraitCenter-eA4LgWjKdGfH': '柱状图',
    'portraitCenter-fB5MhXjKdGfH': '请输入名称',
    'portraitCenter-gC6NiYjKdGfH': '开始时间必须早于结束时间',
    'portraitCenter-hD7OjZjKdGfH': '请选择图表类型',
    'portraitCenter-iE8PkaKdGfH': '轴、值为必填项',
    'portraitCenter-jF9QlbKdGfH': '编辑成功',
    'portraitCenter-kG0RmcKdGfH': '创建成功',
    'portraitCenter-lH1SndKdGfH': '名称',
    'portraitCenter-mI2ToeKdGfH': '最大长度限制为40位字符',
    'portraitCenter-nJ3UpfKdGfH': '请输入档案名称',
    'portraitCenter-oK4VqgKdGfH': '仅支持中文、英文、数字、下划线',
    'portraitCenter-pL5WrhKdGfH': '名称已存在',
    'portraitCenter-qM6XsiKdGfH': '请求错误',
    'portraitCenter-rN7YtjKdGfH': '请输入',
    'portraitCenter-sO8ZukKdGfH': '时间范围',
    'portraitCenter-tP9AvlKdGfH': '图表类型',
    'portraitCenter-uQ0BwmKdGfH': '请选择',
    'portraitCenter-vR1CxnKdGfH': '数据表',
    'portraitCenter-wS2DyoKdGfH': '取消',
    'portraitCenter-xT3EzpKdGfH': '上一步',
    'portraitCenter-yU4FaqKdGfH': '保存',
    'portraitCenter-bLASpFcH1A': '基本筛选',
    'portraitCenter-YaxzxhrzSy': '显示值满足以下条件的项：',
    'portraitCenter-YbJZUyZL4w': ' 且 ',
    'portraitCenter-iAwe2ZdWh2': ' 或 ',
    'portraitCenter-oR3MFdCFSR': '是（全部）',
    'portraitCenter-9wt1FV89Qa': '等于',
    'portraitCenter-aRJ5FTscVE': '过滤器',
    'portraitCenter-DdcqblJA0H': '在上述所选的横轴、值、图例的基础上进一步过滤',
    'portraitCenter-v70VPXfoah': '搜索',
    'portraitCenter-TO8KT4F4e4': '清除筛选条件',
    'portraitCenter-kiw8JQKUMv': '选择左边的数据表字段拖到这里',

    // KV.jsx
    'portraitCenter-fB0IjxKdGfH': '请选择属性',
    'portraitCenter-gC0JkyKdGfH': '请输入名称',
    'portraitCenter-hD1LmzKdGfH': '请选择头像',
    'portraitCenter-iE2NozKdGfH': '选择{{type}}',
    'portraitCenter-jF3PpaKdGfH': '数据',
    'portraitCenter-kG4QqbKdGfH': '属性',
    'portraitCenter-zV5GbrKdGfH': '暂无数据',

    // KV_TAG.jsx
    'portraitCenter-aW6HcsKdGfH': '暂无数据',

    // List.jsx
    'portraitCenter-bX7IdtKdGfH': '暂无数据',

    // KV_TAG.jsx
    'portraitCenter-q3WyRvD15pXm': '请选择标签',
    'portraitCenter-DCnsQdg8PpUD': '选择标签',
    'portraitCenter-J0NfcnFlZblv': '点击选择标签',
    'portraitCenter-aVRyPzcOZKFM': '选择要显示的标签',
    'portraitCenter-BCMXT0bVhE35': '已选择 {{count}} 字段',
    'portraitCenter-J9C8uiZ6gqRu': '清除',

    // List.jsx
    'portraitCenter-mZSZJxlcdOBV': '请选择字段',
    'portraitCenter-iWk1d8lWbFWU': '主表',
    'portraitCenter-gg5EO8IwABDN': '下钻关联表',
    'portraitCenter-Ty7BftbXUVwP': '非必选',
    'portraitCenter-DVpzcsYudMtG': '请选择',
    'portraitCenter-EK7fcOok7Mbt': '时间范围',
    'portraitCenter-ntBYM4xwBrNc': '字段统计',

    // Log.jsx
    'portraitCenter-Zkk5R7qFELX5': '时间范围',
    'portraitCenter-cY8JeuKdGfH': '暂无数据'
  },
  en: {
    // chart/index.jsx
    'portraitCenter-vR5CxNjKdGfH': 'Count',
    'portraitCenter-wS6DyOjKdGfH': 'Sum',
    'portraitCenter-xT7EzPjKdGfH': 'Average',
    'portraitCenter-yU8FaQjKdGfH': 'Maximum',
    'portraitCenter-zV9GbRjKdGfH': 'Minimum',
    'portraitCenter-aW0HcSjKdGfH': 'Unique Count',
    'portraitCenter-bX1IdTjKdGfH': 'Stacked Bar Chart',
    'portraitCenter-cY2JeUjKdGfH': 'Clustered Bar Chart',
    'portraitCenter-dZ3KfVjKdGfH': 'Line Chart',
    'portraitCenter-eA4LgWjKdGfH': 'Bar Chart',
    'portraitCenter-fB5MhXjKdGfH': 'Please enter name',
    'portraitCenter-gC6NiYjKdGfH': 'Start time must be earlier than end time',
    'portraitCenter-hD7OjZjKdGfH': 'Please select chart type',
    'portraitCenter-iE8PkaKdGfH': 'Axis and value are required',
    'portraitCenter-jF9QlbKdGfH': 'Edit successful',
    'portraitCenter-kG0RmcKdGfH': 'Create successful',
    'portraitCenter-lH1SndKdGfH': 'Name',
    'portraitCenter-mI2ToeKdGfH': 'Maximum length limit is 40 characters',
    'portraitCenter-nJ3UpfKdGfH': 'Please enter profile name',
    'portraitCenter-oK4VqgKdGfH': 'Only Chinese, English, numbers, and underscores are supported',
    'portraitCenter-pL5WrhKdGfH': 'Name already exists',
    'portraitCenter-qM6XsiKdGfH': 'Request error',
    'portraitCenter-rN7YtjKdGfH': 'Please enter',
    'portraitCenter-sO8ZukKdGfH': 'Time Range',
    'portraitCenter-tP9AvlKdGfH': 'Chart Type',
    'portraitCenter-uQ0BwmKdGfH': 'Please select',
    'portraitCenter-vR1CxnKdGfH': 'Data Table',
    'portraitCenter-wS2DyoKdGfH': 'Cancel',
    'portraitCenter-xT3EzpKdGfH': 'Previous',
    'portraitCenter-yU4FaqKdGfH': 'Save',
    'portraitCenter-bLASpFcH1A': 'Basic Filter',
    'portraitCenter-YaxzxhrzSy': 'Display items that meet the following conditions:',
    'portraitCenter-YbJZUyZL4w': ' and ',
    'portraitCenter-iAwe2ZdWh2': ' or ',
    'portraitCenter-oR3MFdCFSR': 'Yes (All)',
    'portraitCenter-9wt1FV89Qa': 'Equal',
    'portraitCenter-aRJ5FTscVE': 'Filter',
    'portraitCenter-DdcqblJA0H': 'Further filter based on the selected horizontal axis, value, and legend',
    'portraitCenter-v70VPXfoah': 'Search',
    'portraitCenter-TO8KT4F4e4': 'Clear Filter',
    'portraitCenter-kiw8JQKUMv': 'Select the left data table field here',

    // KV.jsx
    'portraitCenter-fB0IjxKdGfH': 'Please select attributes',
    'portraitCenter-gC0JkyKdGfH': 'Please enter name',
    'portraitCenter-hD1LmzKdGfH': 'Please select avatar',
    'portraitCenter-iE2NozKdGfH': 'Select {{type}}',
    'portraitCenter-jF3PpaKdGfH': 'Data',
    'portraitCenter-kG4QqbKdGfH': 'Attributes',
    'portraitCenter-zV5GbrKdGfH': 'No data',

    // KV_TAG.jsx
    'portraitCenter-aW6HcsKdGfH': 'No data',

    // List.jsx
    'portraitCenter-bX7IdtKdGfH': 'No data',

    // KV_TAG.jsx
    'portraitCenter-q3WyRvD15pXm': 'Please select tags',
    'portraitCenter-DCnsQdg8PpUD': 'Select Tags',
    'portraitCenter-J0NfcnFlZblv': 'Click to select tags',
    'portraitCenter-aVRyPzcOZKFM': 'Select tags to display',
    'portraitCenter-BCMXT0bVhE35': 'Selected {{count}} fields',
    'portraitCenter-J9C8uiZ6gqRu': 'Clear',

    // List.jsx
    'portraitCenter-mZSZJxlcdOBV': 'Please select fields',
    'portraitCenter-iWk1d8lWbFWU': 'Main Table',
    'portraitCenter-gg5EO8IwABDN': 'Drill-down Association Table',
    'portraitCenter-Ty7BftbXUVwP': 'Optional',
    'portraitCenter-DVpzcsYudMtG': 'Please select',
    'portraitCenter-EK7fcOok7Mbt': 'Time Range',
    'portraitCenter-ntBYM4xwBrNc': 'Field Statistics',

    // Log.jsx
    'portraitCenter-Zkk5R7qFELX5': 'Time Range',
    'portraitCenter-cY8JeuKdGfH': 'No data'
  }
};
