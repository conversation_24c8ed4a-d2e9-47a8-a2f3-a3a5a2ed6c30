import { KeyOutlined, LockOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Form, Input, Space, Spin } from 'antd';
import React from 'react';
import { t } from '@/utils/translation';
import background from '../../assets/images/loginSW.png';
import logo from '../../assets/images/logoSW.png';

export default function LoginSW({ handleSubmit, loading, codeStatus, codeImg, getCodeImg }) {
  return (
    <Spin spinning={loading}>
      <div className="loginSW">
        <div className="bgimg">
          <img src={background} tabIndex="-1" alt="" />
        </div>
        <div className="loginForm">
          <img src={logo} alt="" />
          <div className="title">{t('login-uakMhQsuRmNZ')}</div>
          <div>
            <Form name="basic" onFinish={handleSubmit} autoComplete="off">
              <Form.Item
                name="username"
                rules={[
                  () => ({
                    validator(_, value) {
                      const valueValid = /^[a-zA-Z0-9]{1,8}$/.test(value);
                      if (!valueValid) {
                        return Promise.reject(new Error(t('login-1P7iTEaLLNmC')));
                      } else {
                        return Promise.resolve();
                      }
                    }
                  })
                ]}
              >
                <Input
                  prefix={<UserOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                  placeholder={t('login-kw7tPjucwWUo')}
                  style={{ height: 38 }}
                />
              </Form.Item>

              <Form.Item name="password" rules={[{ required: true, message: t('login-Sy1TW89aFVbp') }]}>
                <Input
                  type="password"
                  prefix={<LockOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                  placeholder={t('login-rS6k8dbJBek2')}
                  style={{ height: 38 }}
                />
              </Form.Item>

              {codeStatus && (
                <Form.Item name="code" rules={[{ required: true, message: t('login-smow1UZDgLrB') }]}>
                  <Space>
                    <Input
                      prefix={<KeyOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                      placeholder={t('login-ar5EsTI6tCUs')}
                      style={{ height: 38, width: 212 }}
                    />
                    <div onClick={getCodeImg}>
                      <img src={codeImg} alt="" className="w-[100px] cursor-pointer" />
                    </div>
                  </Space>
                </Form.Item>
              )}

              <Form.Item>
                <Button type="primary" htmlType="submit" style={{ width: '100%', height: 40, borderRadius: 8 }}>
                  {t('login-w4dFGRM2P6SS')}
                </Button>
              </Form.Item>
            </Form>
          </div>
        </div>
      </div>
    </Spin>
  );
}
