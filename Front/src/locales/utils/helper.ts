import i18next from 'i18next';

type FileModule = Record<string, string>;
// type LangType = { cn: Record<string, string>; en: Record<string, string> };

const convertToCSVList = [
  {
    type: 'setting',
    name: '设置'
  },
  {
    type: 'portraitCenter',
    name: '画像中心'
  },
  {
    type: 'operationCenter',
    name: '运营中心'
  },
  {
    type: 'dataCenter',
    name: '数据中心'
  },
  {
    type: 'global',
    name: '全局'
  },
  {
    type: 'components',
    name: '组件'
  },
  {
    type: 'approve',
    name: '审批'
  },
  {
    type: 'productCenter',
    name: '产品中心'
  },
  {
    type: 'analysisCenter',
    name: '分析中心'
  },
  {
    type: 'cpnt',
    name: '组件库'
  },
  {
    type: 'evaluation',
    name: '指标中心'
  }
] as const;

const loadLangFiles = (context: any, langKey: string): FileModule => {
  const files: FileModule = {};
  context.keys().forEach((key: any) => {
    const module = context(key).default;
    if (module[langKey]) {
      Object.assign(files, module[langKey]);
    } else {
      Object.assign(files, module);
    }
  });
  return files;
};

/**
 * @description 通过require.context获取上下文 此方法只适用webpack
 * 参考链接 https://webpack.js.org/guides/dependency-management/#requirecontext
 * @returns 语言包
 */
const getInitCnLang = (): FileModule => {
  const zhContext = (require as NodeRequire).context('../../', true, /(cn\.ts|locales\.ts)$/);
  return loadLangFiles(zhContext, 'cn');
};

const getInitEnLang = (): FileModule => {
  const enContext = (require as NodeRequire).context('../../', true, /(en\.ts|locales\.ts)$/);
  return loadLangFiles(enContext, 'en');
};

/**
 * 语言文件转CSV
 * @param startWith 前缀
 * @returns
 */
function convertToCSV(startWith: string, defaultLang = false) {
  const cnLang = defaultLang ? getInitCnLang() : getNewLang().cn;
  const enLang = defaultLang ? getInitEnLang() : getNewLang().en;

  const keys = Object.keys(cnLang).filter((key) => (startWith ? key.startsWith(startWith) : true));

  const jsonData = keys.map((key) => ({
    key,
    cn: cnLang[key as keyof typeof cnLang] || '',
    en: enLang[key as keyof typeof enLang] || ''
  }));

  const str = jsonData.reduce((acc, { key, cn, en }) => {
    const escape = (text: string) => `"${(text || '').replace(/"/g, '""')}"`; // 确保text不为undefined
    return `${acc}${escape(key)},${escape(cn)},${escape(en)}\n`;
  }, 'key,cn,en\n');

  const uri = `data:text/csv;charset=utf-8,\ufeff${encodeURIComponent(str)}`;

  const link = document.createElement('a');
  link.href = uri;
  link.download = `中英文对照表${defaultLang ? '-默认' : ''}.csv`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * 获取语言包中所有类型
 * @param lang 语言
 * @returns 类型列表 如：['components', 'global', 'operationCenter', 'portraitCenter']
 */
function getUniquePrefixes(lang: 'cn' | 'en') {
  const { cn: cnLang, en: enLang } = getNewLang();
  const langData = lang === 'cn' ? cnLang : enLang;
  const keys = Object.keys(langData);

  const prefixes = keys.map((key) => key.split('-')[0] || '');
  const uniquePrefixes = Array.from(new Set(prefixes));

  return uniquePrefixes;
}

/**
 * 获取语言包中所有类型
 * @param lang 语言
 * @returns 类型列表 如 convertToCSVList
 */
function getLangTypeList(lang: 'cn' | 'en') {
  const types = getUniquePrefixes(lang);
  const typeNameMap = convertToCSVList.reduce(
    (acc, { type, name }) => {
      acc[type] = name;
      return acc;
    },
    {} as Record<string, string | null>
  );

  return types.map((type) => ({
    type,
    name: typeNameMap[type] || null
  }));
}

/**
 * 每次下载的时候 获取当前系统新的语言包
 */
function getNewLang() {
  const cn = i18next.getResourceBundle('zh_CN', 'translation') || {};
  const en = i18next.getResourceBundle('en_US', 'translation') || {};
  return { cn, en };
}

/**
 * 获取项目默认语言包
 */
function getProjectDefaultCSV() {
  convertToCSV('', true);
}

/**
 * 统计每个key在locales.ts文件中的定义次数
 * @returns key的定义次数统计对象
 */
const countKeyDefinitions = (): Record<string, number> => {
  const keyCount: Record<string, number> = {};

  // 获取所有locales.ts文件的上下文
  const context = (require as NodeRequire).context('../../', true, /locales\.ts$/);

  context.keys().forEach((key: string) => {
    const module = context(key).default;

    // 统计中文语言包中的key
    if (module.cn) {
      Object.keys(module.cn).forEach((langKey) => {
        keyCount[langKey] = (keyCount[langKey] || 0) + 1;
      });
    }

    // 统计英文语言包中的key（避免重复计算）
    if (module.en) {
      Object.keys(module.en).forEach((langKey) => {
        // 只有在中文包中不存在时才计算，避免重复
        if (!module.cn || !module.cn[langKey]) {
          keyCount[langKey] = (keyCount[langKey] || 0) + 1;
        }
      });
    }
  });

  return keyCount;
};

/**
 * 导出key定义次数统计的CSV文件
 */
function exportKeyDefinitionCountCSV() {
  const keyCount = countKeyDefinitions();

  // 按定义次数降序排列
  const sortedKeys = Object.keys(keyCount).sort((a, b) => keyCount[b] - keyCount[a]);

  const jsonData = sortedKeys.map((key) => ({
    key,
    count: keyCount[key]
  }));

  const str = jsonData.reduce((acc, { key, count }) => {
    const escape = (text: string) => `"${(text || '').replace(/"/g, '""')}"`; // 确保text不为undefined
    return `${acc}${escape(key)},${count}\n`;
  }, 'key,definition_count\n');

  const uri = `data:text/csv;charset=utf-8,\ufeff${encodeURIComponent(str)}`;

  const link = document.createElement('a');
  link.href = uri;
  link.download = 'key定义次数统计.csv';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  console.log(`已导出${sortedKeys.length}个key的定义次数统计`);
  console.log(
    '重复定义最多的key:',
    sortedKeys.slice(0, 10).map((key) => `${key}: ${keyCount[key]}次`)
  );
}

Object.assign(window, {
  getProjectDefaultCSV,
  convertToCSV,
  getLangTypeList,
  getInitCnLang,
  getInitEnLang,
  getNewLang,
  exportKeyDefinitionCountCSV,
  countKeyDefinitions
});

export {
  convertToCSV,
  getInitCnLang,
  getInitEnLang,
  getLangTypeList,
  getNewLang,
  exportKeyDefinitionCountCSV,
  countKeyDefinitions
};

// function csvToJson(csvData: string): LangType {
//   const jsonResult: LangType = { cn: {}, en: {} };
//   const rows = decodeURIComponent(csvData).split('\n');

//   rows.slice(1).forEach((row) => {
//     const [key, cn, en] = row.split(',').map((item) => item.replace(/^"|"$/g, '')); // 去掉开头和结尾的引号
//     if (key && cn && en) {
//       jsonResult.cn[key] = cn;
//       jsonResult.en[key] = en;
//     }
//   });

//   return jsonResult;
// }

// const csvData = '';
// const jsonData = csvToJson(csvData.split(',')[1]);
// console.log('🚀 ~ jsonData:', jsonData);
