import { t } from '@/utils/translation';
import React, { Component } from 'react';
import { Drawer, Button } from 'antd';
import './index.scss';

export default class index extends Component {
  constructor(props) {
    super(props);
    // console.log(props);
    this.state = {
      visible: false,
      width: props.width ? props.width : 600
    };
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    if (nextProps.visible !== prevState.visible) {
      return {
        visible: nextProps.visible,
        width: nextProps.width
      };
    }
    return null;
  }

  render() {
    const { visible } = this.state;
    const { title, children, disabled } = this.props;

    return (
      <div>
        <Drawer
          className="drawer"
          title={title}
          placement="right"
          closable
          destroyOnClose
          width={this.state.width}
          onClose={() => this.props.onClose(false)}
          open={visible}
          bodyStyle={{ height: '100%' }}
        >
          {children}
          <footer>
            <div>
              <Button onClick={() => this.props.onClose(false)}>{t('components-cancel')}</Button>
              <Button
                loading={this.props.loading}
                disabled={disabled}
                onClick={() => this.props.onClose(false, null, 'submit')}
                type="primary"
              >
                {t('components-submit')}
              </Button>
            </div>
          </footer>
        </Drawer>
      </div>
    );
  }
}
