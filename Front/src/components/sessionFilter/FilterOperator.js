import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Input, Menu } from 'antd';
import React, { useEffect, useState } from 'react';
import FilterConfig from './FilterConfig';
import { t } from '@/utils/translation';

const { operatorList } = FilterConfig;
// 操作符map
const OPERATOR_MAP = operatorList.reduce((map, obj) => {
  map[obj.operator] = obj;
  return map;
}, {});

const valueType = ['INT', 'LONG', 'DOUBLE', 'TIME', 'DATE', 'TIMESTAMP', 'HIVE_DATE', 'HIVE_TIMESTAMP'];
const type = {
  valueOperator: ['EQ', 'GT', 'GTE', 'LT', 'LTE'],
  orderOperator: ['EQ', 'LIKE', 'START_WITH', 'END_WITH'],
  boolOperator: ['EQ']
};

export default function FilterOperator({ value, onChange, disabled }) {
  const [searchText, setSearchText] = useState(OPERATOR_MAP[value.operator]?.name || '');
  const [menuVisible, setMenuVisible] = useState(false);

  useEffect(() => {
    if (menuVisible) {
      setSearchText('');
    } else if (OPERATOR_MAP[value.operator]) {
      setSearchText(OPERATOR_MAP[value.operator].name);
    }
  }, [value.operator, menuVisible]);

  useEffect(() => {
    setSearchText(OPERATOR_MAP[value.operator]?.name || '');
  }, [value.operator]);

  const operatorMenu = () => {
    return getTypeOperators()
      .map((v) => OPERATOR_MAP[v])
      .filter((v) => !searchText || v.name.indexOf(searchText) >= 0)
      .map((p, i) => {
        return (
          <Menu.Item key={i} onClick={() => onSelect(p)}>
            {p.name}
          </Menu.Item>
        );
      });
  };

  const onSelect = (oeprator) => {
    onChange(oeprator);
    setSearchText(OPERATOR_MAP[value.operator]?.name);
  };

  const getTypeOperators = () => {
    return value.fieldType && value.fieldType === 'BOOL'
      ? type.boolOperator
      : valueType.includes(value.fieldType)
        ? type.valueOperator
        : type.orderOperator;
  };

  const menu = () => {
    return (
      <Menu
        forceSubMenuRender
        style={{
          maxHeight: 400,
          overflowY: 'auto',
          maxWidth: 500,
          overflowX: 'auto'
        }}
      >
        {value?.fieldType && operatorMenu()}
      </Menu>
    );
  };

  const onMenuVisible = (v) => {
    if (v) {
      setSearchText('');
    }
    setMenuVisible(v);
  };

  return (
    <Dropdown
      getPopupContainer={(triggerNode) => triggerNode.parentNode}
      overlay={menu}
      trigger={['click']}
      onOpenChange={onMenuVisible}
      disabled={disabled}
    >
      <div className="clickWrapper">
        <Input
          disabled={disabled}
          className="ant-dropdown-link"
          placeholder={t(
            'dataCenter-dataModelConfig-dataModel-createStep-loadConfig-content-advancedSetting-select2-placeholder'
          )}
          // suffix={<DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />}
          onChange={(e) => setSearchText(e.target.value)}
          onFocus={(event) => event.target.select()}
          value={searchText}
        />
        <DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />
      </div>
    </Dropdown>
  );
}
