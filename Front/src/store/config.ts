const campaign_version_flows = {
  1: [
    {
      id: 15,
      name: '起始事件',
      displayName: '触发事件',
      busiType: 'EventEntryNode',
      editorWidth: 0,
      fakeable: true,
      displayInBox: true,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-2',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'ENTRY',
      status: 'NORMAL',
      nodeId: 1,
      fatherIds: [],
      childrenIds: [4],
      branchIndex: 0,
      x: 0,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'EventEntryNode',
        eventList: [
          {
            event: {
              createTime: 1706523732000,
              updateTime: 1706523732000,
              createUserId: 1,
              updateUserId: 1,
              createUserName: 'admin',
              updateUserName: 'admin',
              projectId: 'Jevf4ghaKT091r5E',
              id: 244,
              name: '0129',
              eventNameValue: '0129',
              specialPropertyMappingList: [],
              eventType: 'BURIED_POINT_EVENT',
              level1: '',
              level2: '',
              remark: ''
            },
            filter: {
              filters: []
            }
          }
        ],
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 25,
      name: '退出节点',
      displayName: '退出节点',
      busiType: 'ExitHelperNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: false,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-1',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'END',
      status: 'NORMAL',
      nodeId: 2,
      fatherIds: [4],
      childrenIds: [],
      branchIndex: 0,
      x: 2,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'ExitHelperNode',
        isValid: false,
        isInit: false,
        hasData: false
      },
      segmentIds: []
    },
    {
      id: 2869,
      name: 'DT短信',
      displayName: 'DT短信',
      busiType: 'IFrameNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#6B98CE',
      icon: '#iconicon_shouji',
      shape: 'SQUARE',
      orders: 1,
      joinType: 'FUNCTION',
      status: 'NORMAL',
      nodeId: 4,
      fatherIds: [1],
      childrenIds: [2],
      branchIndex: 0,
      x: 1,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'IFrameNode',
        data: '{"pushChoice":"GFSMS","pushType":"SMS","noDisturb":true,"activityNo":"5139","projectId":"Jevf4ghaKT091r5E","sourceId":36,"channelId":36,"doNotDisturbConfigChoice":true,"touchPeriod":"14","touchTimes":"1","idenParam":"phone","smsPassParam":"hejiaSalesChannels","templateId":131,"msgContent":"测试短信{username}哈哈哈，我永远{age}{$SmartUrl}","scStatus":"NORMAL","scId":119,"informPublishTime":1728716879750}',
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    }
  ],
  2: [
    {
      id: 15,
      name: '起始事件',
      displayName: '触发事件',
      busiType: 'EventEntryNode',
      editorWidth: 0,
      fakeable: true,
      displayInBox: true,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-2',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'ENTRY',
      status: 'NORMAL',
      nodeId: 1,
      fatherIds: [],
      childrenIds: [43],
      branchIndex: 0,
      x: 0,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'EventEntryNode',
        eventList: [
          {
            event: {
              createTime: 1593501976000,
              updateTime: 1614318337000,
              createUserId: 1,
              updateUserId: 1,
              createUserName: 'admin',
              updateUserName: 'admin',
              projectId: 'qvAD1jk8q0hA0Oxm',
              id: 47,
              name: '压力_登录',
              filter: {
                connector: 'AND',
                filters: [
                  {
                    connector: 'AND',
                    filters: [
                      {
                        field: 'eventName',
                        fieldType: 'STRING',
                        operator: 'EQ',
                        value: 'login',
                        tableId: 31,
                        schemaId: 8,
                        level1: '',
                        level2: '',
                        fieldName: 'eventName',
                        isEnum: false
                      }
                    ],
                    empty: false
                  }
                ]
              },
              specialPropertyMappingList: [],
              eventType: 'CUSTOM',
              level1: '',
              level2: '',
              remark: ''
            },
            filter: {
              filters: []
            }
          }
        ],
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 25,
      name: '退出节点',
      displayName: '退出节点',
      busiType: 'ExitHelperNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: false,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-1',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'END',
      status: 'NORMAL',
      nodeId: 2,
      fatherIds: [26],
      childrenIds: [],
      branchIndex: 0,
      x: 6,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'ExitHelperNode',
        isValid: false,
        isInit: false,
        hasData: false
      },
      segmentIds: []
    },
    {
      id: 613,
      name: '多事件分支V2',
      displayName: '多事件分支V2',
      busiType: 'MultiEventSplitV2Node',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#FCA400',
      icon: '#iconduoshijian',
      shape: 'DIAMOND',
      orders: 1,
      joinType: 'SPLIT',
      status: 'NORMAL',
      nodeId: 3,
      fatherIds: [48],
      childrenIds: [7, 5, 40],
      branchIndex: 0,
      x: 3,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'MultiEventSplitV2Node',
        branchList: [
          {
            branchName: '111',
            type: 'MAIN',
            eventAndFilterList: [
              {
                event: {
                  createTime: 1593501976000,
                  updateTime: 1614318337000,
                  createUserId: 1,
                  updateUserId: 1,
                  createUserName: 'admin',
                  updateUserName: 'admin',
                  projectId: 'qvAD1jk8q0hA0Oxm',
                  id: 47,
                  name: '压力_登录',
                  filter: {
                    connector: 'AND',
                    filters: [
                      {
                        connector: 'AND',
                        filters: [
                          {
                            field: 'eventName',
                            fieldType: 'STRING',
                            operator: 'EQ',
                            value: 'login',
                            tableId: 31,
                            schemaId: 8,
                            level1: '',
                            level2: '',
                            fieldName: 'eventName',
                            isEnum: false
                          }
                        ],
                        empty: false
                      }
                    ]
                  },
                  specialPropertyMappingList: [],
                  eventType: 'CUSTOM',
                  level1: '',
                  level2: '',
                  remark: ''
                },
                filter: {
                  filters: []
                }
              }
            ],
            matchPushMessageId: false
          },
          {
            branchName: '分支2',
            type: 'MAIN',
            eventAndFilterList: [
              {
                event: {
                  createTime: 1593501998000,
                  updateTime: 1618209741000,
                  createUserId: 1,
                  updateUserId: 1,
                  createUserName: 'admin',
                  updateUserName: 'admin',
                  projectId: 'qvAD1jk8q0hA0Oxm',
                  id: 48,
                  name: '压力_登出',
                  eventNameValue: 'test_logout',
                  specialPropertyMappingList: [],
                  eventType: 'BURIED_POINT_EVENT',
                  level1: '',
                  level2: '',
                  remark: ''
                },
                filter: {
                  filters: []
                }
              }
            ],
            matchPushMessageId: false
          },
          {
            branchName: '222',
            type: 'OTHER',
            matchPushMessageId: false
          }
        ],
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 26,
      name: '嵌个baidu试试',
      displayName: '嵌个baidu试试',
      busiType: 'IFrameNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#0099FF',
      icon: '#iconicon_shezhi_',
      shape: 'SQUARE',
      orders: 1,
      joinType: 'FUNCTION',
      status: 'NORMAL',
      nodeId: 5,
      fatherIds: [3],
      childrenIds: [6],
      branchIndex: 1,
      x: 4,
      y: 1,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'IFrameNode',
        data: '{}',
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 504,
      name: '等待时长V2',
      displayName: '等待时长V2',
      busiType: 'WaitTimerV2Node',
      editorWidth: 0,
      fakeable: true,
      displayInBox: true,
      color: '#4A90E2',
      icon: '#iconicon_shijian',
      shape: 'SQUARE',
      orders: 1,
      joinType: 'FUNCTION',
      status: 'NORMAL',
      nodeId: 6,
      fatherIds: [5],
      childrenIds: [12],
      branchIndex: 0,
      x: 5,
      y: 1,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'WaitTimerV2Node',
        day: 0,
        hour: 0,
        minute: 1,
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 64,
      name: '指定时刻',
      displayName: '指定时刻',
      busiType: 'AtTimeNode',
      editorWidth: 0,
      fakeable: true,
      displayInBox: true,
      color: '#0099FF',
      icon: '#iconicon_rili',
      shape: 'SQUARE',
      orders: 1,
      joinType: 'FUNCTION',
      status: 'NORMAL',
      nodeId: 7,
      fatherIds: [3],
      childrenIds: [26],
      branchIndex: 0,
      x: 4,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'AtTimeNode',
        type: 'ONCE',
        once: 1730514605301,
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 23,
      name: '合并分支',
      displayName: '合并分支',
      busiType: 'JoinNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#FCA400',
      icon: '#icongaibanxianxingtubiao-',
      shape: 'DIAMOND',
      orders: 1,
      joinType: 'JOIN',
      status: 'NORMAL',
      nodeId: 12,
      fatherIds: [6],
      childrenIds: [13],
      branchIndex: 0,
      x: 6,
      y: 1,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'JoinNode',
        isValid: false,
        isInit: false,
        hasData: false
      },
      segmentIds: []
    },
    {
      id: 24,
      name: '合并分支辅助节点',
      displayName: '合并分支辅助节点',
      busiType: 'JoinHelperNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: false,
      color: '#FCA400',
      icon: '#iconicon_xiaochengxu',
      shape: 'LIT_DIAMOND',
      orders: 1,
      joinType: 'JOIN',
      status: 'NORMAL',
      nodeId: 13,
      fatherIds: [12],
      childrenIds: [52],
      branchIndex: 0,
      x: 7,
      y: 1,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'JoinHelperNode',
        isValid: false,
        isInit: false,
        hasData: false
      },
      segmentIds: []
    },
    {
      id: 26,
      name: '嵌个baidu试试',
      displayName: '嵌个baidu试试',
      busiType: 'IFrameNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#0099FF',
      icon: '#iconicon_shezhi_',
      shape: 'SQUARE',
      orders: 1,
      joinType: 'FUNCTION',
      status: 'NORMAL',
      nodeId: 26,
      fatherIds: [7],
      childrenIds: [2],
      branchIndex: 0,
      x: 5,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'IFrameNode',
        data: '{}',
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 25,
      name: '退出节点',
      displayName: '退出节点',
      busiType: 'ExitHelperNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: false,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-1',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'END',
      status: 'NORMAL',
      nodeId: 38,
      fatherIds: [39],
      childrenIds: [],
      branchIndex: 0,
      x: 6,
      y: 2,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'ExitHelperNode',
        isValid: false,
        isInit: false,
        hasData: false
      },
      segmentIds: []
    },
    {
      id: 1807,
      name: 'GF撤回',
      displayName: 'GF撤回',
      busiType: 'IFrameNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#E72B2B',
      icon: '#icontag',
      shape: 'SQUARE',
      orders: 1,
      joinType: 'FUNCTION',
      status: 'NORMAL',
      nodeId: 39,
      fatherIds: [40],
      childrenIds: [38],
      branchIndex: 0,
      x: 5,
      y: 2,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'IFrameNode',
        data: '{}',
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 2869,
      name: 'DT短信',
      displayName: 'DT短信',
      busiType: 'IFrameNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#6B98CE',
      icon: '#iconicon_shouji',
      shape: 'SQUARE',
      orders: 1,
      joinType: 'FUNCTION',
      status: 'NORMAL',
      nodeId: 40,
      fatherIds: [3],
      childrenIds: [39],
      branchIndex: 2,
      x: 4,
      y: 2,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'IFrameNode',
        data: '{}',
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 20,
      name: '触发事件分支',
      displayName: '触发事件分支',
      busiType: 'EventTriggerSplitNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#FCA400',
      icon: '#iconyonghuzhongxin-2',
      shape: 'DIAMOND',
      orders: 1,
      joinType: 'SPLIT',
      status: 'NORMAL',
      nodeId: 43,
      fatherIds: [1],
      childrenIds: [48, 47],
      branchIndex: 0,
      x: 1,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'EventTriggerSplitNode',
        branchList: [
          {
            branchName: '做过：压力_登录',
            inCounts: 0
          },
          {
            branchName: '未做过：压力_登录',
            inCounts: 0
          }
        ],
        eventAndFilter: {
          event: {
            createTime: 1593501976000,
            updateTime: 1614318337000,
            createUserId: 1,
            updateUserId: 1,
            createUserName: 'admin',
            updateUserName: 'admin',
            projectId: 'qvAD1jk8q0hA0Oxm',
            id: 47,
            name: '压力_登录',
            filter: {
              connector: 'AND',
              filters: [
                {
                  connector: 'AND',
                  filters: [
                    {
                      field: 'eventName',
                      fieldType: 'STRING',
                      operator: 'EQ',
                      value: 'login',
                      tableId: 31,
                      schemaId: 8,
                      level1: '',
                      level2: '',
                      fieldName: 'eventName',
                      isEnum: false
                    }
                  ],
                  empty: false
                }
              ]
            },
            specialPropertyMappingList: [],
            eventType: 'CUSTOM',
            level1: '',
            level2: '',
            remark: ''
          },
          filter: {
            filters: []
          },
          eventCount: 1
        },
        matchPushMessageId: false,
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 25,
      name: '退出节点',
      displayName: '退出节点',
      busiType: 'ExitHelperNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: false,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-1',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'END',
      status: 'NORMAL',
      nodeId: 44,
      fatherIds: [46],
      childrenIds: [],
      branchIndex: 0,
      x: 6,
      y: 3,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'ExitHelperNode',
        isValid: false,
        isInit: false,
        hasData: false
      },
      segmentIds: []
    },
    {
      id: 2449,
      name: '华福证券短信',
      displayName: '华福证券短信',
      busiType: 'IFrameNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#4A90E2',
      icon: '#iconicon_xiaoxi',
      shape: 'SQUARE',
      orders: 999,
      joinType: 'FUNCTION',
      status: 'NORMAL',
      nodeId: 45,
      fatherIds: [47],
      childrenIds: [50],
      branchIndex: 0,
      x: 3,
      y: 3,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'IFrameNode',
        data: '{}',
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 2449,
      name: '华福证券短信',
      displayName: '华福证券短信',
      busiType: 'IFrameNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#4A90E2',
      icon: '#iconicon_xiaoxi',
      shape: 'SQUARE',
      orders: 999,
      joinType: 'FUNCTION',
      status: 'NORMAL',
      nodeId: 46,
      fatherIds: [50],
      childrenIds: [44],
      branchIndex: 0,
      x: 5,
      y: 3,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'IFrameNode',
        data: '{}',
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 2449,
      name: '华福证券短信',
      displayName: '华福证券短信',
      busiType: 'IFrameNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#4A90E2',
      icon: '#iconicon_xiaoxi',
      shape: 'SQUARE',
      orders: 999,
      joinType: 'FUNCTION',
      status: 'NORMAL',
      nodeId: 47,
      fatherIds: [43],
      childrenIds: [45],
      branchIndex: 1,
      x: 2,
      y: 3,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'IFrameNode',
        data: '{}',
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 1807,
      name: 'GF撤回',
      displayName: 'GF撤回',
      busiType: 'IFrameNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#E72B2B',
      icon: '#icontag',
      shape: 'SQUARE',
      orders: 1,
      joinType: 'FUNCTION',
      status: 'NORMAL',
      nodeId: 48,
      fatherIds: [43],
      childrenIds: [3],
      branchIndex: 0,
      x: 2,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'IFrameNode',
        data: '{}',
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 20,
      name: '触发事件分支',
      displayName: '触发事件分支',
      busiType: 'EventTriggerSplitNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: true,
      color: '#FCA400',
      icon: '#iconyonghuzhongxin-2',
      shape: 'DIAMOND',
      orders: 1,
      joinType: 'SPLIT',
      status: 'NORMAL',
      nodeId: 50,
      fatherIds: [45],
      childrenIds: [46, 51],
      branchIndex: 0,
      x: 4,
      y: 3,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'EventTriggerSplitNode',
        branchList: [
          {
            branchName: '做过：压力_城市',
            inCounts: 0
          },
          {
            branchName: '未做过：压力_城市',
            inCounts: 0
          }
        ],
        eventAndFilter: {
          event: {
            createTime: 1598515640000,
            updateTime: 1614318337000,
            createUserId: 1,
            updateUserId: 1,
            createUserName: 'admin',
            updateUserName: 'admin',
            projectId: 'qvAD1jk8q0hA0Oxm',
            id: 59,
            name: '压力_城市',
            filter: {
              connector: 'AND',
              filters: [
                {
                  connector: 'AND',
                  filters: [
                    {
                      field: 'eventName',
                      fieldType: 'STRING',
                      operator: 'EQ',
                      value: 'city',
                      tableId: 31,
                      schemaId: 8,
                      level1: '',
                      level2: '',
                      fieldName: 'eventName',
                      isEnum: false
                    }
                  ],
                  empty: false
                }
              ]
            },
            specialPropertyMappingList: [],
            eventType: 'CUSTOM',
            level1: '',
            level2: '',
            remark: ''
          },
          filter: {
            filters: []
          },
          eventCount: 1
        },
        matchPushMessageId: false,
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 25,
      name: '退出节点',
      displayName: '退出节点',
      busiType: 'ExitHelperNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: false,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-1',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'END',
      status: 'NORMAL',
      nodeId: 51,
      fatherIds: [50],
      childrenIds: [],
      branchIndex: 1,
      x: 5,
      y: 4,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'ExitHelperNode',
        isValid: false,
        isInit: false,
        hasData: false
      },
      segmentIds: []
    },
    {
      id: 260,
      name: '退出节点',
      displayName: '退出节点',
      busiType: 'ExitHelperNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: false,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-1',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'END',
      status: 'NORMAL',
      nodeId: 52,
      fatherIds: [13],
      childrenIds: [],
      branchIndex: 0,
      x: 8,
      y: 1,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'ExitHelperNode',
        isValid: false,
        isInit: false,
        hasData: false
      },
      segmentIds: []
    }
  ],
  3: [
    {
      id: 15,
      name: '起始事件',
      displayName: '触发事件',
      busiType: 'EventEntryNode',
      editorWidth: 0,
      fakeable: true,
      displayInBox: true,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-2',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'ENTRY',
      status: 'NORMAL',
      nodeId: 19,
      fatherIds: [],
      childrenIds: [31],
      branchIndex: 0,
      x: 0,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'EventEntryNode',
        eventList: [
          {
            event: {
              createTime: 1730187842000,
              updateTime: 1730187842000,
              createUserId: 1,
              updateUserId: 1,
              createUserName: 'admin',
              updateUserName: 'admin',
              projectId: 'Jevf4ghaKT091r5E',
              id: 332,
              name: '测试空值事件',
              eventNameValue: 'TestNull',
              specialPropertyMappingList: [
                {
                  displayName: '空值测试',
                  propertySchema: 'testnull',
                  dataType: 'STRING',
                  index: 0
                }
              ],
              eventType: 'BURIED_POINT_EVENT',
              level1: '',
              level2: '',
              remark: ''
            },
            filter: {
              connector: 'AND',
              filters: [
                {
                  connector: 'AND',
                  filters: [
                    {
                      field: 's_var0',
                      fieldType: 'STRING',
                      operator: 'IS_NOT_NULL',
                      tableId: 34,
                      level1: '事件专有属性',
                      level2: '',
                      fieldName: '空值测试',
                      isEnum: false
                    }
                  ],
                  empty: false
                }
              ]
            }
          }
        ],
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 25,
      name: '退出节点',
      displayName: '退出节点',
      busiType: 'ExitHelperNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: false,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-1',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'END',
      status: 'NORMAL',
      nodeId: 31,
      fatherIds: [19],
      childrenIds: [],
      branchIndex: 0,
      x: 1,
      y: 0,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'ExitHelperNode',
        isValid: false,
        isInit: false,
        hasData: false
      },
      segmentIds: []
    },
    {
      id: 15,
      name: '起始事件',
      displayName: '触发事件',
      busiType: 'EventEntryNode',
      editorWidth: 0,
      fakeable: true,
      displayInBox: true,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-2',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'ENTRY',
      status: 'NORMAL',
      nodeId: 32,
      fatherIds: [],
      childrenIds: [33],
      branchIndex: 0,
      x: 0,
      y: 1,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'EventEntryNode',
        eventList: [
          {
            event: {
              createTime: 1730187842000,
              updateTime: 1730187842000,
              createUserId: 1,
              updateUserId: 1,
              createUserName: 'admin',
              updateUserName: 'admin',
              projectId: 'Jevf4ghaKT091r5E',
              id: 332,
              name: '测试空值事件',
              eventNameValue: 'TestNull',
              specialPropertyMappingList: [
                {
                  displayName: '空值测试',
                  propertySchema: 'testnull',
                  dataType: 'STRING',
                  index: 0
                }
              ],
              eventType: 'BURIED_POINT_EVENT',
              level1: '',
              level2: '',
              remark: ''
            },
            filter: {
              connector: 'AND',
              filters: [
                {
                  connector: 'AND',
                  filters: [
                    {
                      field: 's_var0',
                      fieldType: 'STRING',
                      operator: 'IS_NULL',
                      tableId: 34,
                      level1: '事件专有属性',
                      level2: '',
                      fieldName: '空值测试',
                      isEnum: false
                    }
                  ],
                  empty: false
                }
              ]
            }
          }
        ],
        isValid: true,
        isInit: true,
        hasData: true
      },
      segmentIds: []
    },
    {
      id: 25,
      name: '退出节点',
      displayName: '退出节点',
      busiType: 'ExitHelperNode',
      editorWidth: 0,
      fakeable: false,
      displayInBox: false,
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-1',
      shape: 'CIRCLE',
      orders: 2,
      joinType: 'END',
      status: 'NORMAL',
      nodeId: 33,
      fatherIds: [32],
      childrenIds: [],
      branchIndex: 0,
      x: 1,
      y: 1,
      retryTime: 0,
      inCounts: 0,
      holdupCounts: 0,
      detail: {
        busiType: 'ExitHelperNode',
        isValid: false,
        isInit: false,
        hasData: false
      },
      segmentIds: []
    }
  ]
};

export { campaign_version_flows };
