import BaseService from './BaseService';

class DataQualityService extends BaseService {
  // 事件校验规则集合添加
  saveAll(data) {
    return super
      ._request({
        method: 'POST',
        url: '/dataQuality/v1/eventRuleCheck/saveAll.do',
        data,
        headers: {}
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }

  // 事件校验规则条件查询
  listBy(data) {
    return super
      ._request({
        method: 'POST',
        url: '/dataQuality/v1/eventRuleCheck/listBy.do',
        data,
        headers: {}
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }

  // 事件校验规则批量删除
  delByIds(data) {
    return super
      ._request({
        method: 'POST',
        url: '/dataQuality/v1/eventRuleCheck/delByIds.do',
        data,
        headers: {}
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }

  // 事件校验日志
  searchByFields(data) {
    return super
      ._request({
        method: 'POST',
        url: '/dataQuality/v1/eventBnormalLog/searchByFields.do',
        data,
        headers: {}
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }

  // 事件校验规则日志下载
  searchByFieldsDownload(data) {
    return super
      ._request({
        method: 'POST',
        url: '/dataQuality/v1/eventBnormalLog/searchByFieldsDownload.do',
        data,
        headers: {}
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }

  // // 事件检查数记录条件查询
  // getChartData(data) {
  //   return super
  //     ._request({
  //       method: 'POST',
  //       url: '/dataQuality/v1/eventRuleCheckNum/listBy.do',
  //       data,
  //       headers: {}
  //     })
  //     .then((response) => {
  //       const { header, body } = response;
  //       if (header.code === 0) {
  //         return body;
  //       }
  //     });
  // }

  // 事件检查数记录条件查询图表
  getChartData(data) {
    return super
      ._request({
        method: 'POST',
        url: '/dataQuality/v1/eventRuleCheckNum/listByQraph.do',
        data,
        headers: {}
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }
}

export default new DataQualityService();
