import BaseService from 'service/BaseService';

class CampaignOverviewV2Service extends BaseService {
  listBy(data) {
    return super
      ._request({
        method: 'POST',
        url: '/analyzer/businessMetricsData/listBy.do',
        data,
        headers: {}
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }

  calcCampaignV2PassedCount(data) {
    return super
      ._request({
        method: 'POST',
        url: '/analyzer/businessMetricsData/calcCampaignV2PassedCount.do',
        data,
        headers: {}
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }
}

export default new CampaignOverviewV2Service();
