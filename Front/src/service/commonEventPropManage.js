import BaseService from './BaseService';

class CommonEventPropManage extends BaseService {
  // 获取列表
  pageQuery(data) {
    return super
      ._request({
        method: 'POST',
        url: '/analyzer/eventProperty/query.do',
        data,
        headers: {}
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }

  // 获取项目ID列表
  projectID(data) {
    return super
      ._request({
        method: 'POST',
        url: '/analyzer/project/listBy.do',
        data,
        headers: {}
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }

  // 获取通用事件属性options
  findPropertyList(data) {
    return super
      ._request({
        method: 'POST',
        url: '/analyzer/event/findPropertyList.do',
        data,
        headers: {}
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }

  // 详情
  get(id) {
    return super
      ._request({
        method: 'POST',
        url: '/analyzer/eventProperty/get.do',
        headers: {
          'Content-Type': 'application/json'
        },
        data: id
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }

  // 保存
  save(data) {
    return super
      ._request({
        method: 'POST',
        url: '/analyzer/eventProperty/save.do',
        headers: {},
        data
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }

  // 删除
  delById(data) {
    return super
      ._request({
        method: 'POST',
        url: '/analyzer/eventProperty/delById.do',
        headers: {},
        data
      })
      .then((response) => {
        const { header, body } = response;
        if (header.code === 0) {
          return body;
        }
      });
  }
}

export default CommonEventPropManage;
