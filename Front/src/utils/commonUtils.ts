import { useStore } from '@/store/globalStore';

/**
 * 获取当前用户部门id
 * @returns 当前用户部门id
 */
const getDeptId = (): number | undefined => {
  const userId = localStorage.getItem('userId');
  if (!userId) return undefined;
  return useStore.getState().lastLoginDeptMap[parseInt(userId)];
};

/**
 * 保存用户最后登录的部门信息到 zustand store
 * 一般会调用两次，初始化一次，进入home路由再调用一次
 * todo 可以优化为监听localStorage的变化
 * 操作条件：
 * 1. 切换部门
 * 2. 用户退出
 * 3. 离开home页
 * @returns void
 */
const setDeptId = (deptId = window.getDeptId(), userId = localStorage.getItem('userId') as any): void => {
  // console.warn('设置了deptId', '用户', userId, 'deptId', deptId);
  if (userId && deptId) {
    useStore.getState().setStore({
      lastLoginDeptMap: {
        ...useStore.getState().lastLoginDeptMap,
        [userId]: deptId
      }
    });
  }
};

const getSsoInfo = () => {
  const userId = localStorage.getItem('userId')!;
  const deptId = getDeptId()!;
  const defaultProjectId = localStorage.getItem('projectId')!;
  const defaultCompanyId = localStorage.getItem('organizationId')!;
  const token = localStorage.getItem('aim_authorization')!;
  return { userId: parseInt(userId), deptId, defaultProjectId, defaultCompanyId, token };
};

const getDeptRoutes = () => {
  const deptAllList = JSON.parse(localStorage.getItem('departmentListAll')!);
  if (!deptAllList) return '';
  const routes = deptAllList.find((item: { id: number }) => item?.id === Number(getDeptId())).routes;
  const deptRoutes = `${routes}${window.getDeptId()}`;
  return deptRoutes;
};

if (typeof window !== 'undefined') {
  window.getDeptId = getDeptId;
  window.setDeptId = setDeptId;
  window.getSsoInfo = getSsoInfo;
  window.getDeptRoutes = getDeptRoutes;
}

export { getDeptId, getDeptRoutes, setDeptId };
