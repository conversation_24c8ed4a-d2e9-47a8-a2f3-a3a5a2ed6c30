{
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "eslint.run": "onType",
  "eslint.options": {
    "extensions": [
      ".js",
      ".jsx",
      ".ts",
      ".tsx"
    ]
  },
  "files.exclude": {
    "**/.vscode": true
  },
  "editor.tabSize": 2,
  "diffEditor.ignoreTrimWhitespace": false,
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "javascript.updateImportsOnFileMove.enabled": "always",
  "[html]": {
    "editor.defaultFormatter": "vscode.html-language-features"
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  "files.autoGuessEncoding": true,
  "eslint.codeAction.showDocumentation": {
    "enable": true
  },
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "editor.formatOnPaste": true,
  "eslint.format.enable": true,
  "editor.formatOnSave": true,
  "editor.inlineSuggest.enabled": true,
  "diffEditor.wordWrap": "on",
  "editor.unicodeHighlight.allowedCharacters": {
    "：": true,
    "！": true,
    " ": true
  },
  "editor.wordWrap": "on",
  "[typescriptreact]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "git.enableSmartCommit": true,
  "git.autofetch": true,
  "[scss]": {
    "editor.defaultFormatter": "vscode.css-language-features"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "ti18n.enable": true,
  "ti18n.customLanguages": [
    "cn",
    "en",
    "zh",
    "locales"
  ],
  "ti18n.configDirs": [
    "i18n",
    "locale"
  ],
  "ti18n.extnames": [
    ".js",
    ".jsx",
    ".ts",
    ".tsx",
    ".vue",
    "cn.ts",
    "en.ts",
    "locales.ts"
  ],
  "ti18n.exclude": [],
  "ti18n.shortcutLanguages": [
    "cn",
  ],
  "ti18n.shortcutLanguageMaxLength": 50
}